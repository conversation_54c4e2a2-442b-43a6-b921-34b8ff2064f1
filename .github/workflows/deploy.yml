name: Build and Deploy

on:
  push:
    branches: [ main ]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
    - name: Checkout code
      uses: actions/checkout@v4

    - name: Install dependencies
      run: |
        echo "Installing dependencies..."
        timeout 300 npm install --no-audit --no-fund || echo "npm install timed out, continuing anyway"
        echo "Dependencies installed."

    - name: Build application
      run: |
        echo "Building application..."
        npm run build
        echo "Build completed."

    - name: Create .env file
      run: |
        echo "Creating .env file..."
        echo "DATABASE_URL=${{ secrets.DATABASE_URL }}" > .env
        echo "OPENAI_API_KEY=${{ secrets.OPENAI_API_KEY }}" >> .env
        echo "GEMINI_API_KEY=${{ secrets.GEMINI_API_KEY }}" >> .env
        echo "AI_PROVIDER=${{ secrets.AI_PROVIDER }}" >> .env
        echo "NEXT_PUBLIC_SITE_URL=${{ secrets.NEXT_PUBLIC_SITE_URL }}" >> .env
        echo "AUTH0_DOMAIN=${{ secrets.AUTH0_DOMAIN }}" >> .env
        echo "AUTH0_AUDIENCE=${{ secrets.AUTH0_AUDIENCE }}" >> .env
        echo "NEXT_PUBLIC_AUTH0_DOMAIN=${{ secrets.NEXT_PUBLIC_AUTH0_DOMAIN }}" >> .env
        echo "NEXT_PUBLIC_AUTH0_CLIENT_ID=${{ secrets.NEXT_PUBLIC_AUTH0_CLIENT_ID }}" >> .env
        echo "NEXT_PUBLIC_AUTH0_AUDIENCE=${{ secrets.NEXT_PUBLIC_AUTH0_AUDIENCE }}" >> .env
        echo ".env file created."

    - name: Create runtime config file
      run: |
        echo "Creating runtime config file..."
        mkdir -p dist/public
        cat > out/config.js << EOF
        window.ENV = {
          SITE_URL: "https://domainmate.net",
          AUTH0_DOMAIN: "${{ secrets.NEXT_PUBLIC_AUTH0_DOMAIN }}",
          AUTH0_CLIENT_ID: "${{ secrets.NEXT_PUBLIC_AUTH0_CLIENT_ID }}",
          AUTH0_AUDIENCE: "${{ secrets.NEXT_PUBLIC_AUTH0_AUDIENCE }}"
        };
        EOF
        echo "Runtime config file created."

    - name: Update index.html
      run: |
        echo "Updating index.html..."
        sed -i '/<\/head>/i \    <script src="/config.js"></script>' out/index.html
        echo "index.html updated."

    - name: Create deployment package
      run: |
        echo "Creating deployment package..."
        mkdir -p deployment
        cp -r dist deployment/
        cp -r out deployment/
        cp -r migrations deployment/
        cp -r scripts deployment/
        cp -r shared deployment/
        cp package.json package-lock.json .env drizzle.config.ts deployment/
        cd deployment && tar -czf ../domainmate-deploy.tar.gz .
        echo "Deployment package created."

    - name: Set up SSH
      run: |
        echo "Setting up SSH..."
        mkdir -p ~/.ssh
        echo "${{ secrets.SSH_PRIVATE_KEY }}" > ~/.ssh/id_rsa
        chmod 600 ~/.ssh/id_rsa
        ssh-keyscan -H ${{ secrets.VPS_IP }} >> ~/.ssh/known_hosts
        echo "SSH setup completed."

    - name: Create systemd service file
      run: |
        echo "Creating systemd service file..."
        cat > domainmate.service << EOF
        [Unit]
        Description=DomainMate Application
        After=network.target

        [Service]
        Type=simple
        User=${{ secrets.VPS_USERNAME }}
        WorkingDirectory=/root/domainmate
        ExecStart=node /root/domainmate/dist/index.js
        Restart=on-failure
        Environment=NODE_ENV=production
        # Add Auth0 environment variables
        Environment=AUTH0_DOMAIN=${{ secrets.AUTH0_DOMAIN }}
        Environment=AUTH0_AUDIENCE=${{ secrets.AUTH0_AUDIENCE }}

        [Install]
        WantedBy=multi-user.target
        EOF
        echo "Systemd service file created."

    - name: Transfer files to VPS
      run: |
        echo "Starting file transfer to VPS..."
        timeout 60 scp domainmate-deploy.tar.gz domainmate.service ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }}:~/
        echo "File transfer completed."

    - name: Create directory and extract files on VPS
      run: |
        echo "Creating directory and extracting files..."
        timeout 120 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "mkdir -p ~/domainmate && tar -xzf ~/domainmate-deploy.tar.gz -C ~/domainmate"
        echo "Files extracted successfully."

    - name: Install dependencies on VPS
      run: |
        echo "Installing dependencies..."
        timeout 600 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "cd ~/domainmate && npm install --production --no-fund"
        echo "Dependencies installed."

    - name: Run database migrations
      run: |
        echo "Running database migrations..."
        timeout 300 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "cd ~/domainmate && npm run db:push:all"
        echo "Database migrations completed."

    - name: Configure systemd service
      run: |
        echo "Configuring systemd service..."
        timeout 120 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "if [ ! -f /etc/systemd/system/domainmate.service ]; then sudo mv ~/domainmate.service /etc/systemd/system/ && sudo systemctl daemon-reload && sudo systemctl enable domainmate.service; fi"
        echo "Systemd service configured."

    - name: Restart application
      run: |
        echo "Restarting application..."
        timeout 60 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "sudo systemctl restart domainmate.service"
        echo "Application restarted."

    - name: Check application status
      run: |
        echo "Checking application status..."
        timeout 60 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "sudo systemctl status domainmate.service"
        echo "Status check completed."

    - name: Clean up temporary files
      run: |
        echo "Cleaning up temporary files..."
        timeout 60 ssh ${{ secrets.VPS_USERNAME }}@${{ secrets.VPS_IP }} "rm ~/domainmate-deploy.tar.gz"
        echo "Cleanup completed."