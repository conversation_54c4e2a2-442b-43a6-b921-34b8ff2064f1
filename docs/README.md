# DomainMate

DomainMate is an AI-powered domain name generator and availability checker that helps you find the perfect domain name for your business or project. Built with Next.js SSG (Static Site Generation) for optimal SEO and performance.

## Features

- **AI-Powered Generation**: Uses advanced AI models (OpenAI GPT-4 and Google Gemini) to generate creative and relevant domain names
- **Real-time Domain Availability**: Checks domain availability across multiple TLDs
- **Bulk Domain Checker**: Check availability of up to 100 domains at once (requires authentication)
- **User Authentication**: Secure authentication via Auth0
- **Favorites System**: Save and manage favorite domain names
- **Blog System**: Static blog with SEO-optimized articles
- **SEO Optimized**: Static Site Generation with proper meta tags and structured data
- **Responsive Design**: Works seamlessly on desktop and mobile devices

## Architecture

DomainMate uses a **Static Site Generated (SSG)** architecture:
- **Frontend**: Next.js 14 with static generation for optimal SEO and performance
- **Backend**: Express.js API server for dynamic functionality (authentication, domain checking, etc.)
- **Database**: PostgreSQL with Dr<PERSON>zle ORM
- **Authentication**: Auth0 for secure user management

## AI Provider Support

DomainMate now supports multiple AI providers for domain name generation:

- **OpenAI**: Uses OpenAI's GPT models for creative domain name generation
- **Google Gemini**: Uses Google's Gemini models as an alternative

You can easily switch between these providers by changing the `AI_PROVIDER` environment variable.

## Setup

1. Clone the repository
2. Install dependencies:
   ```
   npm install
   ```
3. Create a `.env` file based on `.env.example`:
   ```
   # Database connection
   DATABASE_URL=postgres://username:password@localhost:5432/domainmate

   # OpenAI API key
   OPENAI_API_KEY=your_openai_api_key_here

   # Google Gemini API key
   GEMINI_API_KEY=your_gemini_api_key_here

   # AI provider selection (openai or gemini)
   AI_PROVIDER=openai
   ```
4. Run database migrations:
   ```
   npm run db:push
   ```
5. Start the development server:
   ```
   npm run dev
   ```

   This will start the Next.js development server on port 3000. For API development, you can also run:
   ```
   npm run dev:server
   ```

## Switching AI Providers

To switch between AI providers, simply change the `AI_PROVIDER` environment variable to either `openai` or `gemini`. The application will automatically use the corresponding API key and service.

## Building for Production

The build process generates static files for the frontend and compiles the API server:

```bash
npm run build
```

This will:
1. Generate static HTML/CSS/JS files in the `out/` directory
2. Compile the Express.js API server to `dist/`

To start the production server:
```bash
npm run start
```

The production server serves the static files and handles API requests.

## Deployment

DomainMate's SSG architecture provides flexible deployment options. See [DEPLOYMENT.md](DEPLOYMENT.md) for detailed deployment instructions.

### Quick Deploy Options

1. **Vercel** (Recommended): Connect your GitHub repo to Vercel for automatic deployments
2. **Static Hosting + API Server**: Deploy static files to CDN and API server separately
3. **VPS**: Deploy both static files and API server to a single server

### Environment Variables

Set the following environment variables for your deployment:

**Frontend (NEXT_PUBLIC_*)**:
- `NEXT_PUBLIC_SITE_URL`: Your domain URL
- `NEXT_PUBLIC_AUTH0_DOMAIN`: Auth0 domain
- `NEXT_PUBLIC_AUTH0_CLIENT_ID`: Auth0 client ID
- `NEXT_PUBLIC_AUTH0_AUDIENCE`: Auth0 API audience

**Backend**:
- `DATABASE_URL`: PostgreSQL connection string
- `OPENAI_API_KEY`: OpenAI API key
- `GEMINI_API_KEY`: Google Gemini API key
- `AI_PROVIDER`: AI provider (openai or gemini)

## License

MIT
