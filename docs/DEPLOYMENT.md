# Deployment Guide

DomainMate uses a Static Site Generated (SSG) architecture with Next.js, which provides several deployment options.

## Architecture Overview

- **Frontend**: Static HTML/CSS/JS files generated by Next.js (in `out/` directory)
- **Backend**: Express.js API server (compiled to `dist/` directory)
- **Database**: PostgreSQL (can be hosted separately)

## Deployment Options

### Option 1: Static Hosting + Separate API Server

This is the recommended approach for optimal performance and cost efficiency.

#### Frontend (Static Files)
Deploy the `out/` directory to any static hosting service:
- **Vercel** (recommended for Next.js)
- **Netlify**
- **AWS S3 + CloudFront**
- **GitHub Pages**
- **Cloudflare Pages**

#### Backend (API Server)
Deploy the compiled server to any Node.js hosting service:
- **Railway**
- **Render**
- **Heroku**
- **DigitalOcean App Platform**
- **AWS ECS/Lambda**

### Option 2: Single Server Deployment

Deploy both static files and API server to a single VPS or container.

## Environment Variables

### Frontend (Next.js)
```env
NEXT_PUBLIC_SITE_URL=https://domainmate.net
NEXT_PUBLIC_AUTH0_DOMAIN=auth.domainmate.net
NEXT_PUBLIC_AUTH0_CLIENT_ID=your_auth0_client_id
NEXT_PUBLIC_AUTH0_AUDIENCE=https://api.domainmate.net
```

### Backend (Express.js)
```env
DATABASE_URL=**************************************/database
OPENAI_API_KEY=your_openai_api_key
GEMINI_API_KEY=your_gemini_api_key
AI_PROVIDER=openai
AUTH0_DOMAIN=auth.domainmate.net
AUTH0_CLIENT_ID=your_auth0_client_id
AUTH0_CLIENT_SECRET=your_auth0_client_secret
```

## Build Process

1. **Install dependencies**:
   ```bash
   npm install
   ```

2. **Build the application**:
   ```bash
   npm run build
   ```

3. **Deploy static files** from `out/` directory to static hosting

4. **Deploy API server** from `dist/` directory to Node.js hosting

## Vercel Deployment (Recommended)

For the easiest deployment, use Vercel which can handle both static files and API routes:

1. Connect your GitHub repository to Vercel
2. Set environment variables in Vercel dashboard
3. Deploy automatically on push to main branch

## Manual VPS Deployment

For deploying to a VPS:

1. **Setup server**:
   ```bash
   # Install Node.js, PostgreSQL, and PM2
   curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
   sudo apt-get install -y nodejs postgresql pm2
   ```

2. **Deploy application**:
   ```bash
   # Clone and build
   git clone <your-repo>
   cd domainmate
   npm install
   npm run build
   
   # Start with PM2
   pm2 start dist/index.js --name domainmate
   pm2 startup
   pm2 save
   ```

3. **Setup reverse proxy** (nginx):
   ```nginx
   server {
       listen 80;
       server_name domainmate.net;
       
       # Serve static files
       location / {
           root /path/to/domainmate/out;
           try_files $uri $uri/ /index.html;
       }
       
       # Proxy API requests
       location /api/ {
           proxy_pass http://localhost:3000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
       }
   }
   ```

## Database Setup

1. **Create PostgreSQL database**
2. **Run migrations**:
   ```bash
   npm run db:push
   npm run db:apply-rls
   ```

## Monitoring

- Use PM2 for process management on VPS
- Monitor API server logs and performance
- Set up database backups
- Configure SSL certificates (Let's Encrypt)

## Performance Optimization

- Static files are automatically optimized by Next.js
- Use CDN for static file delivery
- Enable gzip compression on server
- Implement database connection pooling
- Monitor API response times
