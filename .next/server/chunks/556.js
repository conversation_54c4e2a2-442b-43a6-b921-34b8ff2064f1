"use strict";exports.id=556,exports.ids=[556],exports.modules={7304:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{$:()=>y});var r=t(997),n=t(5570),i=t(9232),o=t(3749),l=t(6127),d=t(2089),c=t(2553),m=t(3432),h=t(8655),x=t(6689),p=t(8503),f=t(5079),u=t(1664),g=t.n(u),v=e([n,i,o,p]);function y(){let[e,a]=(0,x.useState)(""),[t,s]=(0,x.useState)(!1),[u,v]=(0,x.useState)(""),{toast:y}=(0,f.pm)(),{mutate:w,isPending:j}=(0,p.g)();return r.jsx("footer",{className:"mt-12 sm:mt-20 relative overflow-hidden",children:r.jsx(o.Zb,{className:"bg-white shadow-md w-full",children:(0,r.jsxs)(o.aY,{className:"p-6 sm:p-10",children:[r.jsx("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-teal-500"}),r.jsx("div",{className:"absolute -top-16 -left-16 w-32 h-32 rounded-full bg-gradient-to-br from-primary-100 to-transparent opacity-30"}),r.jsx("div",{className:"absolute -bottom-20 -right-20 w-40 h-40 rounded-full bg-gradient-to-tr from-secondary-100 to-transparent opacity-30"}),(0,r.jsxs)("div",{className:"max-w-7xl mx-auto",children:[(0,r.jsxs)("div",{className:"grid grid-cols-1 sm:grid-cols-3 gap-8 sm:gap-10",children:[(0,r.jsxs)("div",{className:"flex flex-col space-y-4",children:[(0,r.jsxs)("a",{href:"/",className:"flex items-center gap-2 group",children:[r.jsx("div",{className:"w-10 h-10 rounded-lg overflow-hidden shadow-lg",children:r.jsx("img",{src:"/domainmate.png",alt:"DomainMate Logo",className:"w-full h-full object-cover"})}),r.jsx("div",{children:r.jsx("span",{className:"text-xl font-bold text-gradient",children:"DomainMate"})})]}),r.jsx("p",{className:"text-gray-600 text-sm mt-2 max-w-xs",children:"AI-powered domain name generator to find the perfect domain for your next project."}),(0,r.jsxs)("div",{className:"flex mt-4 space-x-2 sm:space-x-3",children:[r.jsx("a",{href:"https://twitter.com/domain_mate",target:"_blank",rel:"noopener noreferrer",className:"w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md",children:r.jsx("svg",{className:"w-4 h-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:r.jsx("path",{d:"M22 4s-.7 2.1-2 3.4c1.6 10-9.4 17.3-18 11.6 2.2.1 4.4-.6 6-2C3 15.5.5 9.6 3 5c2.2 2.6 5.6 4.1 9 4-.9-4.2 4-6.6 7-3.8 1.1 0 3-1.2 3-1.2z"})})}),r.jsx("a",{href:"https://www.linkedin.com/company/domain-mate",target:"_blank",rel:"noopener noreferrer",className:"w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md",children:(0,r.jsxs)("svg",{className:"w-4 h-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[r.jsx("path",{d:"M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"}),r.jsx("rect",{x:"2",y:"9",width:"4",height:"12"}),r.jsx("circle",{cx:"4",cy:"4",r:"2"})]})}),r.jsx("a",{href:"https://instagram.com/domain_mate",target:"_blank",rel:"noopener noreferrer",className:"w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md",children:(0,r.jsxs)("svg",{className:"w-4 h-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[r.jsx("rect",{x:"2",y:"2",width:"20",height:"20",rx:"5",ry:"5"}),r.jsx("path",{d:"M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"}),r.jsx("line",{x1:"17.5",y1:"6.5",x2:"17.51",y2:"6.5"})]})}),r.jsx("a",{href:"https://www.tiktok.com/@domain_mate",target:"_blank",rel:"noopener noreferrer",className:"w-8 sm:w-9 h-8 sm:h-9 rounded-full bg-gray-100 hover:bg-primary-100 flex items-center justify-center transition-all duration-300 hover:text-primary-600 hover:-translate-y-1 hover:shadow-md",children:(0,r.jsxs)("svg",{className:"w-4 h-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[r.jsx("path",{d:"M9 12a4 4 0 1 0 0 8 4 4 0 0 0 0-8z"}),r.jsx("path",{d:"M15 8v6c0 5-4 5-6 5a7 7 0 0 1-3-1"}),r.jsx("path",{d:"M15 2v6m-3 3c5.7 0 6.4-4 7-6"})]})})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4",children:"Domain Resources"}),(0,r.jsxs)("ul",{className:"space-y-3",children:[r.jsx("li",{children:(0,r.jsxs)(g(),{href:"/",className:"text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1",children:[r.jsx(l.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Generate Domain Names"})]})}),r.jsx("li",{children:(0,r.jsxs)(g(),{href:"/blog",className:"text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1.5 group",children:[r.jsx(d.Z,{className:"w-4 h-4 group-hover:rotate-3 transition-transform duration-300"}),r.jsx("span",{children:"DomainMate Blog"})]})}),r.jsx("li",{children:(0,r.jsxs)("a",{href:"/#faq",className:"text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1",children:[r.jsx(c.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Domain Name Generator FAQ"})]})}),r.jsx("li",{children:(0,r.jsxs)("a",{href:"https://namecheap.pxf.io/domainmate",target:"_blank",rel:"noopener noreferrer",className:"text-gray-600 hover:text-primary-600 transition-colors text-sm flex items-center gap-1",children:[r.jsx("svg",{className:"w-4 h-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:r.jsx("path",{d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"})}),r.jsx("span",{children:"Namecheap Domain Registrar"})]})}),(0,r.jsxs)("li",{children:[(0,r.jsxs)("a",{href:"https://namecheap.pxf.io/c/6159477/624623/5618",target:"_blank",rel:"sponsored noopener",className:"text-green-600 hover:text-green-700 font-medium transition-colors text-sm flex items-center gap-1",children:[r.jsx("svg",{className:"w-4 h-4",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:r.jsx("path",{d:"M12 2v4m0 12v4M4.93 4.93l2.83 2.83m8.48 8.48l2.83 2.83M2 12h4m12 0h4M4.93 19.07l2.83-2.83m8.48-8.48l2.83-2.83"})}),r.jsx("span",{children:"Special: 56% off 1st yr Shared Hosting!"})]}),r.jsx("div",{style:{position:"absolute",visibility:"hidden"},children:r.jsx("img",{height:"0",width:"0",src:"https://namecheap.pxf.io/c/6159477/624623/5618",style:{border:"0"},alt:"Namecheap affiliate tracking pixel"})})]})]})]}),(0,r.jsxs)("div",{children:[r.jsx("h3",{className:"text-sm font-semibold text-gray-800 uppercase tracking-wider mb-4",children:"Subscribe to our newsletter"}),r.jsx("p",{className:"text-gray-600 text-sm mb-3",children:"Get the latest updates, news and special offers."}),t?r.jsx("div",{className:"bg-green-50 border border-green-200 rounded-lg p-4 animate-scale-in",children:r.jsx("p",{className:"text-green-700 text-sm font-medium",children:"Thank you for subscribing!"})}):(0,r.jsxs)("form",{onSubmit:a=>{if(a.preventDefault(),e.trim()){if(!/^\S+@\S+\.\S+$/.test(e)){v("Please enter a valid email address");return}}else{v("Please enter your email address");return}w({email:e},{onSuccess:e=>{s(!0),v(""),y({title:"Success!",description:e.message,variant:"default"})},onError:e=>{v(e.message||"Failed to subscribe. Please try again."),y({title:"Error",description:e.message||"Failed to subscribe. Please try again.",variant:"destructive"})}})},className:"mt-2",children:[(0,r.jsxs)("div",{className:"flex flex-col gap-2",children:[r.jsx(i.I,{type:"email",placeholder:"Your email address",className:`bg-gray-50 w-full ${u?"border-red-500":""}`,value:e,onChange:e=>{a(e.target.value),u&&v("")}}),r.jsx(n.z,{type:"submit",variant:"default",className:"bg-primary-600 hover:bg-primary-700 text-white whitespace-nowrap",disabled:j,children:j?(0,r.jsxs)(r.Fragment,{children:[r.jsx(m.Z,{className:"w-4 h-4 mr-2 animate-spin"}),r.jsx("span",{children:"Subscribing..."})]}):r.jsx("span",{children:"Subscribe"})})]}),u&&(0,r.jsxs)("div",{className:"mt-2 flex items-center text-red-600 text-xs animate-slide-up",children:[r.jsx(h.Z,{className:"w-3 h-3 mr-1"}),r.jsx("span",{children:u})]})]})]})]}),(0,r.jsxs)("div",{className:"mt-8 sm:mt-12 pt-6 sm:pt-8 border-t border-gray-200 text-center",children:[(0,r.jsxs)("div",{className:"flex justify-center space-x-4 mb-4",children:[r.jsx("a",{href:"/privacy-policy",className:"text-gray-500 text-sm hover:text-primary-600 transition-colors",children:"Privacy Policy"}),r.jsx("a",{href:"/terms-of-service",className:"text-gray-500 text-sm hover:text-primary-600 transition-colors",children:"Terms of Service"})]}),(0,r.jsxs)("p",{className:"text-gray-500 text-sm",children:["\xa9 ",new Date().getFullYear()," DomainMate. All rights reserved."]})]})]})]})})})}[n,i,o,p]=v.then?(await v)():v,s()}catch(e){s(e)}})},2498:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{w:()=>c});var r=t(997),n=t(2089),i=t(3489),o=t(1664),l=t.n(o),d=e([i]);function c(){return r.jsx("div",{className:"relative z-20 px-4 sm:px-6",children:(0,r.jsxs)("nav",{className:"flex justify-between items-center py-4 mb-8 max-w-full overflow-visible",children:[(0,r.jsxs)("a",{href:"/",className:"flex items-center gap-2 group shrink-0",children:[r.jsx("div",{className:"w-10 h-10 sm:w-12 sm:h-12 rounded-xl overflow-hidden shadow-lg transition-all duration-300 group-hover:shadow-primary-500/25 group-hover:rotate-3",children:r.jsx("img",{src:"/logo.png",alt:"DomainMate Logo",className:"w-full h-full object-cover"})}),r.jsx("div",{children:r.jsx("span",{className:"text-xl sm:text-2xl font-bold text-gradient",children:"DomainMate"})})]}),(0,r.jsxs)("div",{className:"flex items-center gap-3 sm:gap-6 ml-2",children:[(0,r.jsxs)(l(),{href:"/blog",className:"flex items-center gap-1 sm:gap-1.5 px-2 sm:px-3 py-1.5 rounded-lg bg-gradient-to-r from-primary-50 to-secondary-50 border border-primary-100 text-primary-700 hover:from-primary-100 hover:to-secondary-100 hover:text-primary-800 hover:shadow-sm transition-all duration-300 group shrink-0",children:[r.jsx(n.Z,{className:"w-4 h-4 sm:w-5 sm:h-5 group-hover:rotate-3 transition-transform duration-300"}),r.jsx("span",{className:"font-medium hidden md:inline",children:"DomainMate Blog"}),r.jsx("span",{className:"font-medium md:hidden",children:"Blog"})]}),r.jsx(i.I,{})]})]})})}i=(d.then?(await d)():d)[0],s()}catch(e){s(e)}})},1679:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{F$:()=>c,Q5:()=>m,qE:()=>d});var r=t(997),n=t(6689),i=t(5458),o=t(1239),l=e([i,o]);[i,o]=l.then?(await l)():l;let d=n.forwardRef(({className:e,...a},t)=>r.jsx(i.Root,{ref:t,className:(0,o.cn)("relative flex h-10 w-10 shrink-0 overflow-hidden rounded-full",e),...a}));d.displayName=i.Root.displayName;let c=n.forwardRef(({className:e,...a},t)=>r.jsx(i.Image,{ref:t,className:(0,o.cn)("aspect-square h-full w-full",e),...a}));c.displayName=i.Image.displayName;let m=n.forwardRef(({className:e,...a},t)=>r.jsx(i.Fallback,{ref:t,className:(0,o.cn)("flex h-full w-full items-center justify-center rounded-full bg-muted",e),...a}));m.displayName=i.Fallback.displayName,s()}catch(e){s(e)}})},5570:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{z:()=>m});var r=t(997),n=t(6689),i=t(4338),o=t(6926),l=t(1239),d=e([i,o,l]);[i,o,l]=d.then?(await d)():d;let c=(0,o.cva)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input bg-background hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-10 px-4 py-2",sm:"h-9 rounded-md px-3",lg:"h-11 rounded-md px-8",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),m=n.forwardRef(({className:e,variant:a,size:t,asChild:s=!1,...n},o)=>{let d=s?i.Slot:"button";return r.jsx(d,{className:(0,l.cn)(c({variant:a,size:t,className:e})),ref:o,...n})});m.displayName="Button",s()}catch(e){s(e)}})},3749:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{Ol:()=>d,SZ:()=>m,Zb:()=>l,aY:()=>h,ll:()=>c});var r=t(997),n=t(6689),i=t(1239),o=e([i]);i=(o.then?(await o)():o)[0];let l=n.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...a}));l.displayName="Card";let d=n.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...a}));d.displayName="CardHeader";let c=n.forwardRef(({className:e,...a},t)=>r.jsx("h3",{ref:t,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...a}));c.displayName="CardTitle";let m=n.forwardRef(({className:e,...a},t)=>r.jsx("p",{ref:t,className:(0,i.cn)("text-sm text-muted-foreground",e),...a}));m.displayName="CardDescription";let h=n.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("p-6 pt-0",e),...a}));h.displayName="CardContent",n.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,i.cn)("flex items-center p-6 pt-0",e),...a})).displayName="CardFooter",s()}catch(e){s(e)}})},1405:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{$F:()=>x,AW:()=>p,Xi:()=>f,h_:()=>h});var r=t(997),n=t(6689),i=t(1481),o=t(4307),l=t(8865),d=t(162),c=t(1239),m=e([i,c]);[i,c]=m.then?(await m)():m;let h=i.Root,x=i.Trigger;i.Group,i.Portal,i.Sub,i.RadioGroup,n.forwardRef(({className:e,inset:a,children:t,...s},n)=>(0,r.jsxs)(i.SubTrigger,{ref:n,className:(0,c.cn)("flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none focus:bg-accent data-[state=open]:bg-accent",a&&"pl-8",e),...s,children:[t,r.jsx(o.Z,{className:"ml-auto h-4 w-4"})]})).displayName=i.SubTrigger.displayName,n.forwardRef(({className:e,...a},t)=>r.jsx(i.SubContent,{ref:t,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-lg data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})).displayName=i.SubContent.displayName;let p=n.forwardRef(({className:e,sideOffset:a=4,...t},s)=>r.jsx(i.Portal,{children:r.jsx(i.Content,{ref:s,sideOffset:a,className:(0,c.cn)("z-50 min-w-[8rem] overflow-hidden rounded-md border bg-popover p-1 text-popover-foreground shadow-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...t})}));p.displayName=i.Content.displayName;let f=n.forwardRef(({className:e,inset:a,...t},s)=>r.jsx(i.Item,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0",a&&"pl-8",e),...t}));f.displayName=i.Item.displayName,n.forwardRef(({className:e,children:a,checked:t,...s},n)=>(0,r.jsxs)(i.CheckboxItem,{ref:n,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),checked:t,...s,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.ItemIndicator,{children:r.jsx(l.Z,{className:"h-4 w-4"})})}),a]})).displayName=i.CheckboxItem.displayName,n.forwardRef(({className:e,children:a,...t},s)=>(0,r.jsxs)(i.RadioItem,{ref:s,className:(0,c.cn)("relative flex cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none transition-colors focus:bg-accent focus:text-accent-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50",e),...t,children:[r.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:r.jsx(i.ItemIndicator,{children:r.jsx(d.Z,{className:"h-2 w-2 fill-current"})})}),a]})).displayName=i.RadioItem.displayName,n.forwardRef(({className:e,inset:a,...t},s)=>r.jsx(i.Label,{ref:s,className:(0,c.cn)("px-2 py-1.5 text-sm font-semibold",a&&"pl-8",e),...t})).displayName=i.Label.displayName,n.forwardRef(({className:e,...a},t)=>r.jsx(i.Separator,{ref:t,className:(0,c.cn)("-mx-1 my-1 h-px bg-muted",e),...a})).displayName=i.Separator.displayName,s()}catch(e){s(e)}})},9232:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{I:()=>l});var r=t(997),n=t(6689),i=t(1239),o=e([i]);i=(o.then?(await o)():o)[0];let l=n.forwardRef(({className:e,type:a,...t},s)=>r.jsx("input",{type:a,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:s,...t}));l.displayName="Input",s()}catch(e){s(e)}})},3489:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{I:()=>x});var r=t(997),n=t(873),i=t(5570),o=t(1679),l=t(1405),d=t(8267),c=t(775),m=t(1163),h=e([i,o,l]);function x(){let{isAuthenticated:e,isLoading:a,user:t,login:s,logout:h}=(0,n.a)();return((0,m.useRouter)(),a)?r.jsx(i.z,{variant:"ghost",size:"sm",disabled:!0,className:"h-8 w-auto min-w-0 px-2 sm:px-3",children:r.jsx("span",{className:"animate-pulse text-xs sm:text-sm",children:"Loading..."})}):e?(0,r.jsxs)(l.h_,{children:[r.jsx(l.$F,{asChild:!0,children:r.jsx(i.z,{variant:"ghost",size:"sm",className:"relative h-8 w-8 rounded-full p-0",children:(0,r.jsxs)(o.qE,{className:"h-8 w-8",children:[r.jsx(o.F$,{src:t?.picture,alt:t?.name}),r.jsx(o.Q5,{children:t?.name?.charAt(0)||"U"})]})})}),(0,r.jsxs)(l.AW,{align:"end",children:[r.jsx("div",{className:"flex items-center justify-start gap-2 p-2",children:(0,r.jsxs)("div",{className:"flex flex-col space-y-1 leading-none",children:[t?.name&&r.jsx("p",{className:"font-medium",children:t.name}),t?.email&&r.jsx("p",{className:"w-[200px] truncate text-sm text-muted-foreground",children:t.email})]})}),(0,r.jsxs)(l.Xi,{className:"cursor-pointer",onClick:()=>{},children:[r.jsx(d.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Profile"})]}),(0,r.jsxs)(l.Xi,{className:"cursor-pointer",onClick:()=>h(),children:[r.jsx(c.Z,{className:"mr-2 h-4 w-4"}),r.jsx("span",{children:"Log out"})]})]})]}):r.jsx(i.z,{onClick:s,variant:"default",size:"sm",className:"h-8 w-auto min-w-0 px-2 sm:px-4 text-xs sm:text-sm whitespace-nowrap shrink-0",children:"Sign In"})}[i,o,l]=h.then?(await h)():h,s()}catch(e){s(e)}})},8503:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{g:()=>o});var r=t(9752),n=t(2337),i=e([r,n]);function o(){return(0,r.useMutation)({mutationFn:async({email:e})=>(await (0,n.Nv)("POST","/api/newsletter/subscribe",{email:e})).json()})}[r,n]=i.then?(await i)():i,s()}catch(e){s(e)}})},3756:(e,a,t)=>{t.r(a),t.d(a,{default:()=>n});var s=t(997),r=t(6859);function n(){return(0,s.jsxs)(r.Html,{lang:"en",children:[(0,s.jsxs)(r.Head,{children:[s.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Delayed loading of Google Analytics
              window.addEventListener('load', function() {
                setTimeout(function() {
                  var gaScript = document.createElement('script');
                  gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-H4NYY7F33M';
                  gaScript.async = true;
                  document.head.appendChild(gaScript);

                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', 'G-H4NYY7F33M');
                }, 1000); // Delay by 1 second after page load
              });
            `}}),s.jsx("script",{dangerouslySetInnerHTML:{__html:`
              window.addEventListener('load', function() {
                setTimeout(function() {
                  var adScript = document.createElement('script');
                  adScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5598928771673352';
                  adScript.async = true;
                  adScript.crossOrigin = 'anonymous';
                  document.head.appendChild(adScript);
                }, 2000); // Delay by 2 seconds after page load
              });
            `}})]}),(0,s.jsxs)("body",{children:[s.jsx(r.Main,{}),s.jsx(r.NextScript,{}),s.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Register service worker for production only
              if ('serviceWorker' in navigator && window.location.hostname !== 'localhost') {
                window.addEventListener('load', () => {
                  navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                      console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(error => {
                      console.log('ServiceWorker registration failed: ', error);
                    });
                });
              }
            `}}),s.jsx("script",{dangerouslySetInnerHTML:{__html:`
              document.addEventListener('DOMContentLoaded', function() {
                const images = document.querySelectorAll('img:not([loading])');
                images.forEach(img => {
                  if (img.classList.contains('critical')) return;
                  img.setAttribute('loading', 'lazy');
                });
              });
            `}}),s.jsx("script",{dangerouslySetInnerHTML:{__html:`
              if ('scheduler' in window && 'postTask' in window.scheduler) {
                scheduler.postTask(() => {}, { priority: 'user-visible' });
              }
            `}})]})]})}}};