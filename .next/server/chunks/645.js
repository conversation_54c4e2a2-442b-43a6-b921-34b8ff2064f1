exports.id=645,exports.ids=[645],exports.modules={3234:(e,t,a)=>{"use strict";a.d(t,{R:()=>o});var r=a(997),i=a(6988);let n=(e,t)=>process.env[e]||t,s={domain:n("NEXT_PUBLIC_AUTH0_DOMAIN","auth.domainmate.net"),clientId:n("NEXT_PUBLIC_AUTH0_CLIENT_ID","kCkykBJw9agqnP1Nl1ImKxQgVyA9T0uS"),authorizationParams:{redirect_uri:"https://domainmate.net",audience:n("NEXT_PUBLIC_AUTH0_AUDIENCE","https://api.domainmate.net")},cacheLocation:"localstorage"};function o({children:e}){return s.domain&&s.clientId||console.error("Auth0 configuration is missing required values"),r.jsx(i.Auth0Provider,{domain:s.domain,clientId:s.clientId,authorizationParams:s.authorizationParams,cacheLocation:s.cacheLocation,useRefreshTokens:!0,children:e})}},8690:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{c:()=>u,r:()=>d});var i=a(997),n=a(6689),s=a(5079),o=a(8624),c=a(9527),l=e([o]);o=(l.then?(await l)():l)[0];let f=(0,n.createContext)(void 0);function u({children:e}){let{toast:t}=(0,s.pm)(),{trackEvent:a}=(0,c.z)(),{favorites:r,addFavorite:n,deleteFavorite:l,isAuthenticated:u}=(0,o.T)(),d=async(e,r)=>{try{if(!u){t({title:"Sign in Required",description:"You need to create an account or sign in to save favorites.",variant:"default"});return}let i=await n(e,r);if(i.success)a("favorite_added",{domain_name:e.name,tld:e.tld,full_domain:e.fullDomain,available:e.available}),t({title:"Domain Favorited",description:`${e.fullDomain} has been added to your favorites.`,variant:"default"});else if(i.requiresAuth)t({title:"Sign in Required",description:"You need to create an account or sign in to save favorites.",variant:"default"});else throw Error("Failed to add favorite")}catch(e){t({title:"Error",description:"There was a problem adding this domain to your favorites.",variant:"destructive"})}},m=async e=>{try{if(!u){t({title:"Sign in Required",description:"You need to create an account or sign in to manage favorites.",variant:"default"});return}let i=r.find(t=>t.fullDomain===e||t.relatedTlds?.some(t=>t.fullDomain===e));if(!i)return;let n=await l(i.id);if(n.success)a("favorite_removed",{full_domain:e}),t({title:"Domain Removed",description:"Domain has been removed from your favorites.",variant:"default"});else if(n.requiresAuth)t({title:"Sign in Required",description:"You need to create an account or sign in to manage favorites.",variant:"default"});else throw Error("Failed to remove favorite")}catch(e){t({title:"Error",description:"There was a problem removing this domain from your favorites.",variant:"destructive"})}},p=e=>r.some(t=>t.fullDomain===e||t.relatedTlds?.some(t=>t.fullDomain===e));return i.jsx(f.Provider,{value:{favorites:r,addFavorite:d,removeFavorite:m,toggleFavorite:(e,t)=>{p(e.fullDomain)?m(e.fullDomain):d(e,t)},isFavorite:p},children:e})}function d(){let e=(0,n.useContext)(f);if(void 0===e)throw Error("useFavorites must be used within a FavoritesProvider");return e}r()}catch(e){r(e)}})},1410:(e,t,a)=>{"use strict";a.d(t,{f:()=>i});var r=a(6689);function i(){return(0,r.useRef)([]),null}},2810:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{FN:()=>p,Mi:()=>h,VW:()=>d,_i:()=>f,lj:()=>y,sA:()=>v});var i=a(997),n=a(6689),s=a(1329),o=a(6926),c=a(2190),l=a(1239),u=e([s,o,l]);[s,o,l]=u.then?(await u)():u;let d=s.Provider,f=n.forwardRef(({className:e,...t},a)=>i.jsx(s.Viewport,{ref:a,className:(0,l.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",e),...t}));f.displayName=s.Viewport.displayName;let m=(0,o.cva)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),p=n.forwardRef(({className:e,variant:t,...a},r)=>i.jsx(s.Root,{ref:r,className:(0,l.cn)(m({variant:t}),e),...a}));p.displayName=s.Root.displayName,n.forwardRef(({className:e,...t},a)=>i.jsx(s.Action,{ref:a,className:(0,l.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",e),...t})).displayName=s.Action.displayName;let v=n.forwardRef(({className:e,...t},a)=>i.jsx(s.Close,{ref:a,className:(0,l.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",e),"toast-close":"",...t,children:i.jsx(c.Z,{className:"h-4 w-4"})}));v.displayName=s.Close.displayName;let h=n.forwardRef(({className:e,...t},a)=>i.jsx(s.Title,{ref:a,className:(0,l.cn)("text-sm font-semibold",e),...t}));h.displayName=s.Title.displayName;let y=n.forwardRef(({className:e,...t},a)=>i.jsx(s.Description,{ref:a,className:(0,l.cn)("text-sm opacity-90",e),...t}));y.displayName=s.Description.displayName,r()}catch(e){r(e)}})},5372:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{x:()=>c});var i=a(997),n=a(5079),s=a(2810),o=e([s]);function c(){let{toasts:e}=(0,n.pm)();return(0,i.jsxs)(s.VW,{children:[e.map(function({id:e,title:t,description:a,action:r,...n}){return(0,i.jsxs)(s.FN,{...n,children:[(0,i.jsxs)("div",{className:"grid gap-1",children:[t&&i.jsx(s.Mi,{children:t}),a&&i.jsx(s.lj,{children:a})]}),r,i.jsx(s.sA,{})]},e)}),i.jsx(s._i,{})]})}s=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},9527:(e,t,a)=>{"use strict";a.d(t,{z:()=>i});var r=a(6689);function i(){return{trackEvent:(0,r.useCallback)((e,t)=>{window.gtag&&window.gtag("event",e,t)},[])}}},2917:(e,t,a)=>{"use strict";a.d(t,{K:()=>s});var r=a(873),i=a(8486),n=a(6689);function s(){let{getToken:e}=(0,r.a)();return{authFetch:(0,n.useCallback)(async(t,a={})=>{try{let r=await e(),n=await (0,i.SC)(t,a,r);return(0,i.Bc)(n)}catch(e){throw e}},[e])}}},873:(e,t,a)=>{"use strict";a.d(t,{a:()=>n});var r=a(6988),i=a(6689);function n(){let{isAuthenticated:e,isLoading:t,user:a,loginWithRedirect:n,logout:s,getAccessTokenSilently:o,getAccessTokenWithPopup:c}=(0,r.useAuth0)(),l=(0,i.useCallback)(()=>{n()},[n]),u=(0,i.useCallback)(()=>{s({logoutParams:{returnTo:window.location.origin}})},[s]),d=(0,i.useCallback)(async()=>{try{if(!e)return null;let t=window.ENV?.AUTH0_AUDIENCE||process.env.NEXT_PUBLIC_AUTH0_AUDIENCE||"https://api.domainmate.net";try{let e=await o({authorizationParams:{audience:t},detailedResponse:!1});if(e&&"object"==typeof e&&"access_token"in e)return e.access_token;return e}catch(e){return await c({authorizationParams:{audience:t}})}}catch(e){return null}},[o,c,e]);return{isAuthenticated:e,isLoading:t,user:a,login:l,logout:u,getToken:d}}},5079:(e,t,a)=>{"use strict";a.d(t,{pm:()=>f});var r=a(6689);let i=0,n=new Map,s=e=>{if(n.has(e))return;let t=setTimeout(()=>{n.delete(e),u({type:"REMOVE_TOAST",toastId:e})},1e6);n.set(e,t)},o=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:a}=t;return a?s(a):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===a||void 0===a?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],l={toasts:[]};function u(e){l=o(l,e),c.forEach(e=>{e(l)})}function d({...e}){let t=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),a=()=>u({type:"DISMISS_TOAST",toastId:t});return u({type:"ADD_TOAST",toast:{...e,id:t,open:!0,onOpenChange:e=>{e||a()}}}),{id:t,dismiss:a,update:e=>u({type:"UPDATE_TOAST",toast:{...e,id:t}})}}function f(){let[e,t]=r.useState(l);return r.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>u({type:"DISMISS_TOAST",toastId:e})}}},8624:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{T:()=>c});var i=a(9752),n=a(2917),s=a(873),o=e([i]);function c(){let e=(0,i.useQueryClient)(),{authFetch:t}=(0,n.K)(),{isAuthenticated:a}=(0,s.a)(),{data:r,isLoading:o,error:c,refetch:l}=(0,i.useQuery)({queryKey:["/api/favorites"],queryFn:async()=>await t("/api/favorites"),enabled:a,refetchOnWindowFocus:!1,staleTime:3e4,gcTime:3e5}),u=(0,i.useMutation)({mutationFn:async e=>{if(!a)throw Error("Authentication required");try{let a={...e,price:e.price?Number(e.price):void 0,relatedTlds:e.relatedTlds?e.relatedTlds.map(e=>({...e,price:e.price?Number(e.price):void 0})):void 0};return await t("/api/favorites",{method:"POST",body:JSON.stringify(a)})}catch(e){throw e}},onMutate:async t=>{await e.cancelQueries({queryKey:["/api/favorites"]});let a=e.getQueryData(["/api/favorites"]);return a&&e.setQueryData(["/api/favorites"],{favorites:[...a.favorites,{id:Date.now(),domainName:t.domainName,tld:t.tld,fullDomain:t.fullDomain,available:t.available,price:t.price,createdAt:new Date().toISOString(),relatedTlds:t.relatedTlds}]}),{previousFavorites:a}},onError:(t,a,r)=>{r?.previousFavorites&&e.setQueryData(["/api/favorites"],r.previousFavorites)},onSettled:()=>{e.invalidateQueries({queryKey:["/api/favorites"]})}}),d=(0,i.useMutation)({mutationFn:async e=>await t(`/api/favorites/${e}`,{method:"DELETE"}),onMutate:async t=>{await e.cancelQueries({queryKey:["/api/favorites"]});let a=e.getQueryData(["/api/favorites"]);return a&&e.setQueryData(["/api/favorites"],{favorites:a.favorites.filter(e=>e.id!==t)}),{previousFavorites:a}},onError:(t,a,r)=>{r?.previousFavorites&&e.setQueryData(["/api/favorites"],r.previousFavorites)},onSettled:()=>{e.invalidateQueries({queryKey:["/api/favorites"]})}}),f=async(e,t)=>{if(!a)return{success:!1,requiresAuth:!0};try{let a=e=>{if(null!=e)return"string"==typeof e?Number(e):e};return await u.mutateAsync({domainName:e.name,tld:e.tld,fullDomain:e.fullDomain,available:e.available,price:a(e.price),relatedTlds:t?.map(e=>({tld:e.tld,fullDomain:e.fullDomain,available:e.available,price:a(e.price)}))}),{success:!0}}catch(e){return{success:!1,error:e}}},m=async e=>{if(!a)return{success:!1,requiresAuth:!0};try{return await d.mutateAsync(e),{success:!0}}catch(e){return{success:!1,error:e}}};return{favorites:r?.favorites||[],isLoading:o,error:c,refetch:l,addFavorite:f,deleteFavorite:m,isAddingFavorite:u.isPending,isDeletingFavorite:d.isPending,isAuthenticated:a}}i=(o.then?(await o)():o)[0],r()}catch(e){r(e)}})},8486:(e,t,a)=>{"use strict";a.d(t,{Bc:()=>s,SC:()=>n});let r=()=>({"X-App-Request":"DomainMate-App","Content-Type":"application/json"}),i=async e=>{let t=r();return e&&(t.Authorization=`Bearer ${e}`),t},n=async(e,t={},a)=>{let r={...await i(a),...t.headers||{}};try{return await fetch(e,{...t,headers:r,credentials:"include",cache:"no-store"})}catch(e){throw e}},s=async e=>{if(!e.ok){let t=await e.text();throw Error(`API Error ${e.status}: ${t}`)}return e.json()}},2337:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{Eh:()=>l,Nv:()=>c});var i=a(9752),n=a(8486),s=e([i]);async function o(e){if(!e.ok){let t=await e.text()||e.statusText;throw Error(`${e.status}: ${t}`)}}async function c(e,t,a){let r={method:e,body:a?JSON.stringify(a):void 0};a&&(r.headers={"Content-Type":"application/json"});let i=await (0,n.SC)(t,r);return await o(i),i}let l=new(i=(s.then?(await s)():s)[0]).QueryClient({defaultOptions:{queries:{queryFn:(({on401:e})=>async t=>{let a=t.queryKey,r=a[0];if(t?.meta?.queryParams){let e=new URLSearchParams;Object.entries(t.meta.queryParams).forEach(([t,a])=>{void 0!==a&&""!==a&&e.append(t,a)});let a=e.toString();a&&(r=`${r}?${a}`)}else if(a.length>1&&"/api/domains/search"===r&&"string"==typeof a[1]){let e=a[1],t=a[2];r=`${r}?term=${encodeURIComponent(e)}`,t&&Array.isArray(t)&&t.length>0&&(r+=`&tlds=${t.join(",")}`)}let i=await (0,n.SC)(r);return"returnNull"===e&&401===i.status?null:(await o(i),await i.json())})({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}});r()}catch(e){r(e)}})},1239:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.d(t,{cn:()=>o});var i=a(6593),n=a(8097),s=e([i,n]);function o(...e){return(0,n.twMerge)((0,i.clsx)(e))}[i,n]=s.then?(await s)():s,r()}catch(e){r(e)}})},9645:(e,t,a)=>{"use strict";a.a(e,async(e,r)=>{try{a.r(t),a.d(t,{default:()=>p});var i=a(997),n=a(968),s=a.n(n),o=a(9752),c=a(2337),l=a(5372),u=a(8690),d=a(3234),f=a(1410);a(6764);var m=e([o,c,l,u]);function p({Component:e,pageProps:t}){return(0,i.jsxs)(i.Fragment,{children:[(0,i.jsxs)(s(),{children:[i.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),i.jsx("meta",{name:"author",content:"DomainMate"}),i.jsx("link",{rel:"icon",type:"image/x-icon",href:"/favicon.ico"}),i.jsx("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),i.jsx("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),i.jsx("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),i.jsx("link",{rel:"manifest",href:"/site.webmanifest"}),i.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),i.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),i.jsx("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap",rel:"stylesheet",media:"print",onLoad:e=>{e.target.media="all"}}),i.jsx("style",{dangerouslySetInnerHTML:{__html:`
            body, html {
              margin: 0;
              padding: 0;
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            }
            #__next {
              min-height: 100vh;
            }
            .initial-content {
              font-family: 'Inter', sans-serif;
              max-width: 800px;
              margin: 0 auto;
              padding: 20px;
              text-align: center;
            }
            .initial-heading {
              font-size: 2rem;
              font-weight: bold;
              color: #1f2937;
              margin-bottom: 1rem;
            }
            .initial-text {
              font-size: 1.125rem;
              color: #4b5563;
              max-width: 600px;
              margin: 0 auto;
            }
          `}}),i.jsx("meta",{name:"google-adsense-account",content:"ca-pub-****************"})]}),i.jsx(d.R,{children:i.jsx(o.QueryClientProvider,{client:c.Eh,children:(0,i.jsxs)(u.c,{children:[i.jsx(f.f,{}),i.jsx(e,{...t}),i.jsx(l.x,{})]})})})]})}[o,c,l,u]=m.then?(await m)():m,r()}catch(e){r(e)}})},6764:()=>{}};