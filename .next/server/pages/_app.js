"use strict";(()=>{var e={};e.id=888,e.ids=[888],e.modules={1134:(e,r,t)=>{t.d(r,{Z:()=>d});var a=t(6689);let o=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),i=(...e)=>e.filter((e,r,t)=>!!e&&t.indexOf(e)===r).join(" ");var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=(0,a.forwardRef)(({color:e="currentColor",size:r=24,strokeWidth:t=2,absoluteStrokeWidth:o,className:l="",children:d,iconNode:u,...p},c)=>(0,a.createElement)("svg",{ref:c,...s,width:r,height:r,stroke:e,strokeWidth:o?24*Number(t)/Number(r):t,className:i("lucide",l),...p},[...u.map(([e,r])=>(0,a.createElement)(e,r)),...Array.isArray(d)?d:[d]])),d=(e,r)=>{let t=(0,a.forwardRef)(({className:t,...s},d)=>(0,a.createElement)(l,{ref:d,iconNode:r,className:i(`lucide-${o(e)}`,t),...s}));return t.displayName=`${e}`,t}},2190:(e,r,t)=>{t.d(r,{Z:()=>a});let a=(0,t(1134).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},6988:e=>{e.exports=require("@auth0/auth0-react")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},1329:e=>{e.exports=import("@radix-ui/react-toast")},9752:e=>{e.exports=import("@tanstack/react-query")},6926:e=>{e.exports=import("class-variance-authority")},6593:e=>{e.exports=import("clsx")},8097:e=>{e.exports=import("tailwind-merge")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),a=r.X(0,[645],()=>t(9645));module.exports=a})();