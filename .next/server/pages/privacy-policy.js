"use strict";(()=>{var e={};e.id=396,e.ids=[396],e.modules={7258:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>x,default:()=>p,getServerSideProps:()=>m,getStaticPaths:()=>h,getStaticProps:()=>u,reportWebVitals:()=>y,routeModule:()=>b,unstable_getServerProps:()=>f,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>j,unstable_getStaticProps:()=>g});var a=r(7093),o=r(5244),s=r(1323),n=r(3756),c=r(9645),l=r(6077),d=e([c,l]);[c,l]=d.then?(await d)():d;let p=(0,s.l)(l,"default"),u=(0,s.l)(l,"getStaticProps"),h=(0,s.l)(l,"getStaticPaths"),m=(0,s.l)(l,"getServerSideProps"),x=(0,s.l)(l,"config"),y=(0,s.l)(l,"reportWebVitals"),g=(0,s.l)(l,"unstable_getStaticProps"),j=(0,s.l)(l,"unstable_getStaticPaths"),v=(0,s.l)(l,"unstable_getStaticParams"),f=(0,s.l)(l,"unstable_getServerProps"),P=(0,s.l)(l,"unstable_getServerSideProps"),b=new a.PagesRouteModule({definition:{kind:o.x.PAGES,page:"/privacy-policy",pathname:"/privacy-policy",bundlePath:"",filename:""},components:{App:c.default,Document:n.default},userland:l});i()}catch(e){i(e)}})},6077:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>p,getStaticProps:()=>u});var a=r(997),o=r(968),s=r.n(o),n=r(2498),c=r(7304),l=r(3749),d=e([n,c,l]);function p({lastUpdated:e}){return(0,a.jsxs)(a.Fragment,{children:[(0,a.jsxs)(s(),{children:[a.jsx("title",{children:"Privacy Policy | DomainMate"}),a.jsx("meta",{name:"description",content:"Read DomainMate's privacy policy to understand how we collect, use, and protect your personal information."}),a.jsx("meta",{name:"robots",content:"index, follow"}),a.jsx("meta",{property:"og:title",content:"Privacy Policy | DomainMate"}),a.jsx("meta",{property:"og:description",content:"Read DomainMate's privacy policy to understand how we collect, use, and protect your personal information."}),a.jsx("meta",{property:"og:url",content:"https://domainmate.net/privacy-policy"}),a.jsx("meta",{property:"og:type",content:"website"}),a.jsx("link",{rel:"canonical",href:"https://domainmate.net/privacy-policy"}),a.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:"Privacy Policy",description:"DomainMate's privacy policy explaining how we collect, use, and protect your personal information",url:"https://domainmate.net/privacy-policy",publisher:{"@type":"Organization",name:"DomainMate",url:"https://domainmate.net"},dateModified:e})}})]}),a.jsx("div",{className:"font-sans text-gray-700 min-h-screen",children:(0,a.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[a.jsx("header",{className:"mb-10",children:a.jsx(n.w,{})}),(0,a.jsxs)("main",{children:[(0,a.jsxs)("div",{className:"mb-8",children:[a.jsx("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 mb-4",children:"Privacy Policy"}),(0,a.jsxs)("p",{className:"text-lg text-gray-600",children:["Last updated: ",new Date(e).toLocaleDateString()]})]}),a.jsx(l.Zb,{children:a.jsx(l.aY,{className:"p-8",children:(0,a.jsxs)("div",{className:"prose prose-lg max-w-none",children:[a.jsx("h2",{children:"Introduction"}),a.jsx("p",{children:"At DomainMate, we take your privacy seriously. This Privacy Policy explains how we collect, use, disclose, and safeguard your information when you visit our website and use our services."}),a.jsx("h2",{children:"Information We Collect"}),a.jsx("h3",{children:"Personal Information"}),a.jsx("p",{children:"We may collect personal information that you voluntarily provide to us when you:"}),(0,a.jsxs)("ul",{children:[a.jsx("li",{children:"Create an account"}),a.jsx("li",{children:"Use our domain search services"}),a.jsx("li",{children:"Save domains to your favorites"}),a.jsx("li",{children:"Contact us for support"})]}),a.jsx("h3",{children:"Automatically Collected Information"}),a.jsx("p",{children:"When you visit our website, we may automatically collect certain information about your device, including information about your web browser, IP address, time zone, and some of the cookies that are installed on your device."}),a.jsx("h2",{children:"How We Use Your Information"}),a.jsx("p",{children:"We use the information we collect to:"}),(0,a.jsxs)("ul",{children:[a.jsx("li",{children:"Provide, operate, and maintain our services"}),a.jsx("li",{children:"Improve and personalize your experience"}),a.jsx("li",{children:"Process your requests and transactions"}),a.jsx("li",{children:"Send you technical notices and support messages"}),a.jsx("li",{children:"Respond to your comments and questions"})]}),a.jsx("h2",{children:"Information Sharing"}),a.jsx("p",{children:"We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this Privacy Policy."}),a.jsx("h2",{children:"Data Security"}),a.jsx("p",{children:"We implement appropriate technical and organizational security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction."}),a.jsx("h2",{children:"Cookies and Tracking Technologies"}),a.jsx("p",{children:"We use cookies and similar tracking technologies to track activity on our website and hold certain information to improve your experience."}),a.jsx("h2",{children:"Third-Party Services"}),a.jsx("p",{children:"Our website may contain links to third-party websites or services. We are not responsible for the privacy practices of these third parties."}),a.jsx("h2",{children:"Your Rights"}),a.jsx("p",{children:"Depending on your location, you may have certain rights regarding your personal information, including the right to access, update, or delete your information."}),a.jsx("h2",{children:"Changes to This Privacy Policy"}),a.jsx("p",{children:'We may update this Privacy Policy from time to time. We will notify you of any changes by posting the new Privacy Policy on this page and updating the "Last updated" date.'}),a.jsx("h2",{children:"Contact Us"}),a.jsx("p",{children:"If you have any questions about this Privacy Policy, please contact us through our website or by email."})]})})})]}),a.jsx(c.$,{})]})})]})}[n,c,l]=d.then?(await d)():d;let u=async()=>({props:{lastUpdated:new Date().toISOString()}});i()}catch(e){i(e)}})},6988:e=>{e.exports=require("@auth0/auth0-react")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},5458:e=>{e.exports=import("@radix-ui/react-avatar")},1481:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},4338:e=>{e.exports=import("@radix-ui/react-slot")},1329:e=>{e.exports=import("@radix-ui/react-toast")},9752:e=>{e.exports=import("@tanstack/react-query")},6926:e=>{e.exports=import("class-variance-authority")},6593:e=>{e.exports=import("clsx")},8097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[859,959,645,556],()=>r(7258));module.exports=i})();