"use strict";(()=>{var e={};e.id=660,e.ids=[660],e.modules={3756:(e,r,t)=>{t.r(r),t.d(r,{default:()=>a});var i=t(997),n=t(6859);function a(){return(0,i.jsxs)(n.Html,{lang:"en",children:[(0,i.jsxs)(n.Head,{children:[i.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Delayed loading of Google Analytics
              window.addEventListener('load', function() {
                setTimeout(function() {
                  var gaScript = document.createElement('script');
                  gaScript.src = 'https://www.googletagmanager.com/gtag/js?id=G-H4NYY7F33M';
                  gaScript.async = true;
                  document.head.appendChild(gaScript);

                  window.dataLayer = window.dataLayer || [];
                  function gtag(){dataLayer.push(arguments);}
                  gtag('js', new Date());
                  gtag('config', 'G-H4NYY7F33M');
                }, 1000); // Delay by 1 second after page load
              });
            `}}),i.jsx("script",{dangerouslySetInnerHTML:{__html:`
              window.addEventListener('load', function() {
                setTimeout(function() {
                  var adScript = document.createElement('script');
                  adScript.src = 'https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5598928771673352';
                  adScript.async = true;
                  adScript.crossOrigin = 'anonymous';
                  document.head.appendChild(adScript);
                }, 2000); // Delay by 2 seconds after page load
              });
            `}})]}),(0,i.jsxs)("body",{children:[i.jsx(n.Main,{}),i.jsx(n.NextScript,{}),i.jsx("script",{dangerouslySetInnerHTML:{__html:`
              // Register service worker for production only
              if ('serviceWorker' in navigator && window.location.hostname !== 'localhost') {
                window.addEventListener('load', () => {
                  navigator.serviceWorker.register('/service-worker.js')
                    .then(registration => {
                      console.log('ServiceWorker registration successful with scope: ', registration.scope);
                    })
                    .catch(error => {
                      console.log('ServiceWorker registration failed: ', error);
                    });
                });
              }
            `}}),i.jsx("script",{dangerouslySetInnerHTML:{__html:`
              document.addEventListener('DOMContentLoaded', function() {
                const images = document.querySelectorAll('img:not([loading])');
                images.forEach(img => {
                  if (img.classList.contains('critical')) return;
                  img.setAttribute('loading', 'lazy');
                });
              });
            `}}),i.jsx("script",{dangerouslySetInnerHTML:{__html:`
              if ('scheduler' in window && 'postTask' in window.scheduler) {
                scheduler.postTask(() => {}, { priority: 'user-visible' });
              }
            `}})]})]})}},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},6689:e=>{e.exports=require("react")},997:e=>{e.exports=require("react/jsx-runtime")},5315:e=>{e.exports=require("path")}};var r=require("../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),i=r.X(0,[859],()=>t(3756));module.exports=i})();