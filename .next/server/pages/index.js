"use strict";(()=>{var e={};e.id=405,e.ids=[405],e.modules={1496:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.r(a),t.d(a,{config:()=>g,default:()=>m,getServerSideProps:()=>x,getStaticPaths:()=>u,getStaticProps:()=>h,reportWebVitals:()=>p,routeModule:()=>w,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>j,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>y,unstable_getStaticProps:()=>f});var r=t(7093),i=t(5244),n=t(1323),l=t(3756),o=t(9645),d=t(1854),c=e([o,d]);[o,d]=c.then?(await c)():c;let m=(0,n.l)(d,"default"),h=(0,n.l)(d,"getStaticProps"),u=(0,n.l)(d,"getStaticPaths"),x=(0,n.l)(d,"getServerSideProps"),g=(0,n.l)(d,"config"),p=(0,n.l)(d,"reportWebVitals"),f=(0,n.l)(d,"unstable_getStaticProps"),y=(0,n.l)(d,"unstable_getStaticPaths"),v=(0,n.l)(d,"unstable_getStaticParams"),b=(0,n.l)(d,"unstable_getServerProps"),j=(0,n.l)(d,"unstable_getServerSideProps"),w=new r.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/index",pathname:"/",bundlePath:"",filename:""},components:{App:o.default,Document:l.default},userland:d});s()}catch(e){s(e)}})},3465:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{A:()=>w});var r=t(997),i=t(6689),n=t(5570),l=t(8322),o=t(3749),d=t(9013),c=t(2936),m=t(8133),h=t(8655),u=t(3432),x=t(3050),g=t(7695),p=t(7865),f=t(873),y=t(2811),v=t(9527),b=t(6263),j=e([n,l,o,d,c,m,y]);function w({selectedTlds:e}){let[a,t]=(0,i.useState)(""),[s,c]=(0,i.useState)(""),[x,g]=(0,i.useState)("all"),{isAuthenticated:p,login:v}=(0,f.a)(),{checkDomains:b,domains:j,isLoading:w,error:k}=(0,y.S)(e),C=()=>{let e=a.split("\n").map(e=>e.trim()).filter(e=>e.length>0);if(0===e.length)return c("Please enter at least one domain name"),!1;if(e.length>100)return c("You can check up to 100 domains at once"),!1;let t=e.filter(e=>!/^[a-z0-9-]+$/.test(e.toLowerCase()));return!(t.length>0)||(c(`Invalid domain format: ${t.join(", ")}`),!1)},D=(j||[]).filter(e=>"all"===x||("available"===x?e.available:"unavailable"!==x||!e.available)),M=j?.filter(e=>e.available).length||0,S=j?.filter(e=>!e.available).length||0;return r.jsx("div",{className:"w-full",children:(0,r.jsxs)(o.Zb,{children:[(0,r.jsxs)(o.Ol,{children:[r.jsx(o.ll,{children:"Bulk Domain Checker"}),r.jsx(o.SZ,{children:"Check availability for multiple domains at once. Enter one domain name per line (without TLDs)."})]}),(0,r.jsxs)(o.aY,{children:[p?r.jsx("form",{onSubmit:e=>{if(e.preventDefault(),!p){v();return}if(C()){let e=a.split("\n").map(e=>e.trim()).filter(e=>e.length>0);b(e)}},children:(0,r.jsxs)("div",{className:"space-y-4",children:[r.jsx(l.g,{placeholder:"Enter domain names (one per line, without TLDs) example1 example2 example3",value:a,onChange:e=>{t(e.target.value)},rows:8,className:`font-mono ${s?"border-red-500":""}`,disabled:w}),s&&(0,r.jsxs)(d.bZ,{variant:"destructive",children:[r.jsx(h.Z,{className:"h-4 w-4"}),r.jsx(d.Cd,{children:"Error"}),r.jsx(d.X,{children:s})]}),k&&(0,r.jsxs)(d.bZ,{variant:"destructive",children:[r.jsx(h.Z,{className:"h-4 w-4"}),r.jsx(d.Cd,{children:"Error"}),r.jsx(d.X,{children:k})]}),(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[r.jsx("div",{className:"text-sm text-gray-500",children:e.length>0?`Checking against TLDs: ${e.join(", ")}`:"Using default TLDs"}),r.jsx(n.z,{type:"submit",disabled:w||0===a.trim().length,children:w?(0,r.jsxs)(r.Fragment,{children:[r.jsx(u.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Checking..."]}):"Check Domains"})]})]})}):(0,r.jsxs)("div",{className:"p-6 text-center",children:[r.jsx("div",{className:"mb-4 text-blue-600",children:r.jsx(h.Z,{className:"h-12 w-12 mx-auto"})}),r.jsx("h3",{className:"text-lg font-medium mb-2",children:"Sign In Required"}),r.jsx("p",{className:"text-gray-600 mb-4",children:"Please sign in to use the bulk domain checker feature."}),r.jsx(n.z,{onClick:()=>v(),children:"Sign In to Continue"})]}),p&&j&&j.length>0&&r.jsx("div",{className:"mt-6",children:(0,r.jsxs)(m.mQ,{defaultValue:"all",value:x,onValueChange:e=>g(e),children:[(0,r.jsxs)(m.dr,{className:"grid w-full grid-cols-3",children:[(0,r.jsxs)(m.SP,{value:"all",children:["All (",j.length,")"]}),(0,r.jsxs)(m.SP,{value:"available",children:["Available (",M,")"]}),(0,r.jsxs)(m.SP,{value:"unavailable",children:["Unavailable (",S,")"]})]}),r.jsx(m.nU,{value:"all",className:"mt-4",children:r.jsx(N,{domains:D})}),r.jsx(m.nU,{value:"available",className:"mt-4",children:r.jsx(N,{domains:D})}),r.jsx(m.nU,{value:"unavailable",className:"mt-4",children:r.jsx(N,{domains:D})})]})})]})]})})}function N({domains:e}){let{trackEvent:a}=(0,v.z)();if(0===e.length)return r.jsx("div",{className:"text-center py-4",children:"No domains match the current filter"});let t=e=>{a("domain_registration_click",{domain_name:e.name,tld:e.tld,price:e.price,source:"bulk_checker"}),window.open(`https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D${encodeURIComponent(e.fullDomain)}`)};return r.jsx("div",{className:"rounded-md border",children:(0,r.jsxs)(c.iA,{children:[r.jsx(c.xD,{children:(0,r.jsxs)(c.SC,{children:[r.jsx(c.ss,{className:"w-[50%]",children:r.jsx("div",{className:"flex items-center",children:"Domain"})}),r.jsx(c.ss,{className:"w-[25%]",children:r.jsx("div",{className:"flex items-center",children:"Status"})}),r.jsx(c.ss,{className:"w-[25%]",children:r.jsx("div",{className:"flex items-center",children:"Register"})})]})}),r.jsx(c.RM,{children:e.map(e=>(0,r.jsxs)(c.SC,{children:[r.jsx(c.pj,{className:"font-medium",children:e.fullDomain}),r.jsx(c.pj,{children:e.available?(0,r.jsxs)("div",{className:"flex items-center text-green-600",children:[r.jsx(x.Z,{className:"mr-2 h-4 w-4"}),"Available"]}):(0,r.jsxs)("div",{className:"flex items-center text-red-600",children:[r.jsx(g.Z,{className:"mr-2 h-4 w-4"}),"Taken"]})}),r.jsx(c.pj,{children:e.available?(0,r.jsxs)(n.z,{className:"bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center justify-center gap-1 transition-all duration-300",variant:"outline",size:"sm",onClick:()=>t(e),children:[r.jsx(p.Z,{className:"w-3 h-3"}),r.jsx("span",{children:"Register"}),(0,r.jsxs)("span",{className:"text-xs opacity-75 ml-1",children:[(0,b.T4)(e.price),"/yr"]})]}):r.jsx(n.z,{className:"w-full transition-all duration-300 overflow-hidden relative",variant:"outline",disabled:!0,size:"sm",children:r.jsx("span",{className:"relative z-10",children:"Not Available"})})})]},e.fullDomain))})]})})}[n,l,o,d,c,m,y]=j.then?(await j)():j,s()}catch(e){s(e)}})},892:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{B:()=>u});var r=t(997),i=t(6689),n=t(9232),l=t(5570),o=t(9013),d=t(6127),c=t(8655),m=t(5079),h=e([n,l,o]);function u({onCheck:e,isLoading:a}){let[t,s]=(0,i.useState)(""),[h,u]=(0,i.useState)(""),[x,g]=(0,i.useState)(!1),p=(0,i.useRef)(null),{toast:f}=(0,m.pm)(),y=e=>e.trim()?e.trim().length<2?(u("Domain name must be at least 2 characters"),!1):/^[a-zA-Z0-9-]+$/.test(e.trim())?(u(""),!0):(u("Domain name can only contain letters, numbers, and hyphens"),!1):(u("Please enter a domain name"),!1);return r.jsx("div",{className:"w-full",children:(0,r.jsxs)("form",{onSubmit:a=>{a.preventDefault(),y(t)&&(e(t.trim()),p.current&&p.current.blur(),f({title:"Checking domain availability",description:`Checking availability for "${t.trim()}" with your selected TLDs`}))},className:"space-y-3",children:[(0,r.jsxs)("div",{className:"space-y-2",children:[(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsxs)("div",{className:"relative flex-1",children:[r.jsx(n.I,{ref:p,id:"customDomainInput",value:t,onChange:e=>{s(e.target.value),h&&u("")},onFocus:()=>g(!0),onBlur:()=>g(!1),className:`w-full px-3 py-2 text-sm pr-10 transition-all duration-300 border-2 ${h?"border-red-500 focus-visible:ring-red-500":x?"border-primary-500 shadow-primary-200/40":""}`,placeholder:"Enter domain name (e.g. myawesomesite)","aria-invalid":!!h,"aria-describedby":h?"custom-domain-error":void 0,disabled:a}),r.jsx(d.Z,{className:`absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 transition-all duration-300 ${x?"text-primary-500":"text-gray-400"}`})]}),r.jsx(l.z,{type:"submit",disabled:a||!t.trim(),className:"whitespace-nowrap",children:"Check Availability"})]}),r.jsx("p",{className:"text-xs text-gray-500",children:"Enter just the domain name without any extension. We'll check availability for all your selected TLDs."})]}),h&&(0,r.jsxs)(o.bZ,{variant:"destructive",className:"py-2",children:[r.jsx(c.Z,{className:"h-4 w-4"}),r.jsx(o.X,{id:"custom-domain-error",className:"text-sm",children:h})]})]})})}[n,l,o]=h.then?(await h)():h,s()}catch(e){s(e)}})},3558:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{p:()=>c});var r=t(997),i=t(3749),n=t(6263),l=t(7791),o=t(3432),d=e([i,l]);function c({domainName:e,availableDomains:a,unavailableDomains:t,isLoading:s,hasChecked:d}){if(!d&&!s)return null;let c=(0,n.tv)([...a,...t]);return r.jsx(i.Zb,{className:"bg-white shadow-md overflow-hidden",children:r.jsx(i.aY,{className:"p-4 sm:p-6",children:s?(0,r.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[r.jsx(o.Z,{className:"w-8 h-8 text-primary-500 animate-spin mb-4"}),r.jsx("h3",{className:"text-lg font-medium text-gray-700",children:"Checking domain availability..."}),(0,r.jsxs)("p",{className:"text-gray-500 mt-2",children:["We're checking availability for \"",e,'" with your selected TLDs']})]}):(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mb-4",children:[(0,r.jsxs)("h3",{className:"text-lg font-medium text-gray-800",children:["Results for ",r.jsx("span",{className:"text-primary-600 font-semibold",children:e})]}),r.jsx("p",{className:"text-sm text-gray-500 mt-1",children:a.length>0?`${a.length} available domain${1!==a.length?"s":""} found`:"No available domains found"})]}),c.length>0?r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:c.map(e=>r.jsx(l.U,{groupedDomain:e},e.baseName))}):r.jsx("div",{className:"flex flex-col items-center justify-center py-8 px-4 text-center",children:(0,r.jsxs)("p",{className:"text-gray-500",children:['No domains found for "',e,'" with your selected TLDs.']})})]})})})}[i,l]=d.then?(await d)():d,s()}catch(e){s(e)}})},3239:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{w:()=>f});var r=t(997),i=t(3749),n=t(5570),l=t(5121),o=t(6263),d=t(6689),c=t(1601),m=t(8865),h=t(7865),u=t(8690),x=t(698),g=t(9527),p=e([i,n,l,u]);function f({domain:e,index:a=0}){let[t,s]=(0,d.useState)(!1),p=(0,d.useRef)(null),{isFavorite:f,toggleFavorite:y}=(0,u.r)(),{trackEvent:v}=(0,g.z)(),b=e.available?"bg-gradient-to-r from-green-500 to-emerald-600 text-white":"bg-gradient-to-r from-red-500 to-rose-600 text-white",j=e.available?"Available":"Unavailable",w=e.available?"":"opacity-75",N=e.available?"Register Now":"Not Available",k=e.available?"Registration":"Registered on",C=e.available?`${(0,o.T4)(void 0!==e.price?e.price:null)}/year`:e.registrationDate||"Unknown",D=()=>{e.available&&s(!0)};return(0,r.jsxs)("div",{className:"relative transform transition-all duration-500 animate-scale-in",style:{animationDelay:`${.1*a}s`},ref:p,children:[r.jsx(x.G,{show:t&&e.available,onComplete:()=>s(!1)}),r.jsx("div",{className:"relative",children:r.jsx("div",{className:`${w} transition-all duration-300 relative overflow-hidden rounded-xl shadow-lg`,children:(0,r.jsxs)(i.Zb,{className:"border-0 bg-transparent overflow-hidden h-full",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center border-b p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(n.z,{variant:"ghost",size:"icon",className:`h-7 w-7 rounded-full ${f(e.fullDomain)?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-400"}`,onClick:()=>y(e),title:f(e.fullDomain)?"Remove from favorites":"Add to favorites",children:r.jsx(c.Z,{className:"h-4 w-4",fill:f(e.fullDomain)?"currentColor":"none"})}),r.jsx("h3",{className:"font-medium text-base sm:text-lg transition-all duration-300 break-words",children:e.fullDomain})]}),(0,r.jsxs)(l.C,{className:`${b} shadow transition-all duration-300`,children:[e.available&&r.jsx(m.Z,{className:"w-3 h-3 mr-1"}),j]})]}),(0,r.jsxs)(i.aY,{className:"p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3 sm:mb-4",children:[r.jsx("span",{className:"text-xs sm:text-sm text-gray-600 transition-all duration-300",children:k}),r.jsx("span",{className:"text-xs sm:text-sm font-medium transition-all duration-300",children:C})]}),r.jsx("div",{children:e.available?(0,r.jsxs)(n.z,{className:"w-full bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center justify-center gap-1 transition-all duration-300",variant:"outline",size:"sm",onClick:()=>{D(),v("domain_registration_click",{domain_name:e.name,tld:e.tld,price:e.price}),window.open(`https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D${encodeURIComponent(e.fullDomain)}`)},children:[r.jsx(h.Z,{className:"w-3 h-3"}),r.jsx("span",{children:N}),(0,r.jsxs)("span",{className:"text-xs opacity-75 ml-1",children:[(0,o.T4)(void 0!==e.price?e.price:null),"/yr"]})]}):r.jsx(n.z,{className:"w-full transition-all duration-300 overflow-hidden relative",variant:"outline",disabled:!0,children:r.jsx("span",{className:"relative z-10",children:N})})})]})]})})})]})}[i,n,l,u]=p.then?(await p)():p,s()}catch(e){s(e)}})},2304:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{_:()=>y});var r=t(997),i=t(3239),n=t(7791),l=t(7088),o=t(5570),d=t(4976),c=t(8178),m=t(3243),h=t(3058),u=t(8655),x=t(6689),g=t(7908),p=t(3766),f=e([i,n,l,o,g]);function y({domains:e,groupedDomains:a=[],isLoading:t=!1,searchStatus:s="idle",onGenerateMore:f,onRefresh:y}){let[v,b]=(0,x.useState)("grouped"),[j,w]=(0,x.useState)(!1),{rateLimitInfo:N,isLimited:k}=(0,g.U)(),C=e.filter(e=>e.available),D=e.filter(e=>!e.available);return 0!==e.length||t?(0,r.jsxs)("div",{className:`transition-opacity duration-500 ${j?"opacity-100":"opacity-0"}`,children:[C.length>0&&(0,r.jsxs)("div",{className:"mb-8",children:[C.length>0&&(0,r.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,r.jsxs)("div",{className:"flex flex-wrap items-center",children:[(0,r.jsxs)("h3",{className:"text-base sm:text-lg font-medium text-gray-800 flex items-center",children:[r.jsx("span",{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-full w-5 sm:w-6 h-5 sm:h-6 flex items-center justify-center text-xs mr-2",children:C.length}),"Available Domains"]}),r.jsx("div",{className:"ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded bg-gradient-to-r from-green-100 to-emerald-100 text-green-800",children:"Ready to register"})]}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,r.jsxs)(o.z,{variant:"grouped"===v?"default":"outline",size:"sm",className:`flex items-center gap-1 ${"grouped"===v?"bg-blue-600 text-white hover:bg-blue-700":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,onClick:()=>b("grouped"),title:"Group by domain name",children:[r.jsx(c.Z,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden xs:inline",children:"Group by name"})]}),(0,r.jsxs)(o.z,{variant:"individual"===v?"default":"outline",size:"sm",className:`flex items-center gap-1 ${"individual"===v?"bg-blue-600 text-white hover:bg-blue-700":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"}`,onClick:()=>b("individual"),title:"Show all domains",children:[r.jsx(m.Z,{className:"w-4 h-4"}),r.jsx("span",{className:"hidden xs:inline",children:"Show all domains"})]})]})]}),"grouped"===v&&a&&a.length>0?r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:a.filter(e=>e.availableTlds.length>0).map((e,a)=>r.jsx(n.U,{groupedDomain:e,index:a},e.baseName))}):r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:C.map((e,a)=>r.jsx(i.w,{domain:e,index:a},e.fullDomain))}),f&&r.jsx("div",{className:"mt-8 flex justify-center",children:r.jsx(o.z,{onClick:f,variant:"generating"===s?"default":"outline",disabled:t||"generating"===s,className:`effect-3d transition-all duration-300 ${"generating"===s?"bg-gradient-to-r from-primary-600 to-secondary-600 text-white shadow-md":"border-primary-200 hover:border-primary-300 hover:bg-primary-50"}`,children:"generating"===s?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin h-4 w-4 mr-2 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Generating More Suggestions..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(h.Z,{className:"w-4 h-4 mr-2 text-primary-600"}),r.jsx("span",{className:"hidden xs:inline",children:"Generate More Suggestions"}),r.jsx("span",{className:"xs:hidden",children:"More Suggestions"})]})})})]}),D.length>0&&(0,r.jsxs)("div",{children:[(0,r.jsxs)("div",{className:"flex flex-wrap items-center mb-4",children:[(0,r.jsxs)("h3",{className:"text-base sm:text-lg font-medium text-gray-800 flex items-center",children:[r.jsx("span",{className:"bg-gradient-to-r from-red-500 to-rose-600 text-white rounded-full w-5 sm:w-6 h-5 sm:h-6 flex items-center justify-center text-xs mr-2",children:D.length}),"Unavailable Domains"]}),r.jsx("div",{className:"ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded bg-gradient-to-r from-red-100 to-rose-100 text-rose-800",children:"Already registered"})]}),"grouped"===v&&a&&a.length>0?r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:a.filter(e=>0===e.availableTlds.length&&e.domains.length>0).map((e,a)=>r.jsx(n.U,{groupedDomain:e,index:a+C.length},e.baseName))}):r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:D.map((e,a)=>r.jsx(i.w,{domain:e,index:a+C.length},e.fullDomain))})]}),0===C.length&&!t&&(0,r.jsxs)(r.Fragment,{children:[k&&N?(0,r.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mb-8 border-t-4 border-t-red-500",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[r.jsx("div",{className:"w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3",children:r.jsx(u.Z,{className:"w-5 h-5 text-red-600"})}),r.jsx("h2",{className:"text-xl font-bold text-gray-800",children:"Rate Limit Reached"})]}),(0,r.jsxs)("p",{className:"text-gray-600 mb-4",children:["You've reached the limit of ",N.total," searches per hour. Please try again ",(0,p.Q)(new Date(N.resetTime),{addSuffix:!0}),"."]}),r.jsx("div",{className:"bg-red-50 p-4 rounded-lg border border-red-100 mb-4",children:r.jsx("p",{className:"text-sm text-red-700",children:"This limit helps us prevent abuse and ensure the service remains available for everyone."})})]}):null,r.jsx(l.z,{onGenerateMore:f,searchStatus:s})]})]}):(0,r.jsxs)("div",{className:"text-center py-12",children:[r.jsx("div",{className:"animate-float-slow mb-4",children:r.jsx("svg",{className:"mx-auto w-24 h-24 text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2h2.5M15 11h3.5a2 2 0 012 2v1a2 2 0 01-2 2h-5.5M3 16.5h18M3 7.5h18"})})}),r.jsx("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No domains found"}),r.jsx("p",{className:"text-gray-500 max-w-md mx-auto mb-6",children:"Try searching with different keywords or adjusting your filters to find relevant domain suggestions."}),y&&(0,r.jsxs)(o.z,{onClick:y,className:"effect-3d",children:[r.jsx(d.Z,{className:"w-4 h-4 mr-2"}),"Try Again"]})]})}[i,n,l,o,g]=f.then?(await f)():f,s()}catch(e){s(e)}})},4640:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{b:()=>u});var r=t(997),i=t(6689),n=t(8408),l=t(6127),o=t(734),d=t(2020),c=t(8865),m=t(3432),h=e([n]);function u({isLoading:e,searchStatus:a,searchTerm:t=""}){let[s,h]=(0,i.useState)(0),[u,x]=(0,i.useState)([{id:"analyze",title:"Analyzing Input",description:"Processing your concept and extracting key themes",status:"pending",icon:r.jsx(l.Z,{className:"h-5 w-5"})},{id:"generate",title:"Generating Domains",description:"Creating unique domain name suggestions",status:"pending",icon:r.jsx(o.Z,{className:"h-5 w-5"})},{id:"check",title:"Checking Availability",description:"Verifying domain registration status",status:"pending",icon:r.jsx(d.Z,{className:"h-5 w-5"})}]);return(0,i.useRef)(0),(0,i.useRef)(0),(0,i.useRef)(!1),(0,i.useRef)(!1),(0,i.useRef)(null),(0,r.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6 border border-gray-100",children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[r.jsx("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,r.jsxs)("span",{className:"text-sm font-medium text-primary-600",children:[Math.round(s),"%"]})]}),r.jsx(n.E,{value:s,className:"h-2 bg-gray-100",indicatorClassName:"bg-gradient-to-r from-primary-500 via-secondary-500 to-primary-500 bg-size-200 animate-gradient",animated:!0})]}),r.jsx("div",{className:"space-y-4",children:u.map((e,a)=>(0,r.jsxs)("div",{className:"flex items-start gap-3",children:[r.jsx("div",{className:`flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ${"completed"===e.status?"bg-green-100 text-green-600":"active"===e.status?"bg-primary-100 text-primary-600 animate-pulse":"bg-gray-100 text-gray-400"}`,children:"completed"===e.status?r.jsx(c.Z,{className:"h-4 w-4"}):"active"===e.status?r.jsx(m.Z,{className:"h-4 w-4 animate-spin"}):e.icon}),(0,r.jsxs)("div",{className:"flex-grow",children:[r.jsx("h4",{className:`text-sm font-medium ${"completed"===e.status?"text-green-700":"active"===e.status?"text-primary-700":"text-gray-500"}`,children:e.title}),r.jsx("p",{className:"text-xs text-gray-500 mt-0.5",children:e.description}),"active"===e.status&&r.jsx("div",{className:"mt-2",children:r.jsx(n.E,{value:0===a?s/30*100:1===a?(s-30)/40*100:(s-70)/30*100,className:"h-1 bg-gray-100",indicatorClassName:"bg-gradient-to-r from-primary-400 to-secondary-400",animated:!0})})]})]},e.id))})]})}n=(h.then?(await h)():h)[0],s()}catch(e){s(e)}})},3118:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{$:()=>C});var r=t(997),i=t(6689),n=t(9232),l=t(5570),o=t(6263),d=t(9013),c=t(893),m=t(6194),h=t(8806),u=t(8453),x=t(1601),g=t(2629),p=t(341),f=t(1793),y=t(1475),v=t(6215),b=t(6127),j=t(8655),w=t(2125),N=t(3058),k=e([n,l,d]);function C({onSearch:e,isLoading:a}){let[t,s]=(0,i.useState)(""),[k,C]=(0,i.useState)(""),[D,M]=(0,i.useState)(!1),[S,Z]=(0,i.useState)(""),z=(0,i.useRef)(null),T=(0,o.CP)(),P={business:r.jsx(c.Z,{className:"w-4 h-4"}),ecommerce:r.jsx(m.Z,{className:"w-4 h-4"}),restaurant:r.jsx(h.Z,{className:"w-4 h-4"}),tech:r.jsx(u.Z,{className:"w-4 h-4"}),health:r.jsx(x.Z,{className:"w-4 h-4"}),fitness:r.jsx(g.Z,{className:"w-4 h-4"}),photography:r.jsx(p.Z,{className:"w-4 h-4"}),app:r.jsx(f.Z,{className:"w-4 h-4"}),creative:r.jsx(y.Z,{className:"w-4 h-4"}),realestate:r.jsx(v.Z,{className:"w-4 h-4"})},A=e=>e.trim()?e.trim().length<2?(C("Search term must be at least 2 characters"),!1):(C(""),!0):(C("Please enter a search term"),!1),R=e=>{let a=(0,o.Io)(e);s(a),Z(e),z.current&&z.current.focus(),C("")},L=Array.from({length:10},(e,a)=>r.jsx("div",{className:"absolute rounded-full bg-white/80 w-1 h-1 animate-particles",style:{left:`${50+35*Math.cos(a*Math.PI/5)}%`,top:"50%",animationDelay:`${.1*a}s`}},a));return(0,r.jsxs)("form",{className:"max-w-3xl mx-auto",onSubmit:a=>{a.preventDefault(),A(t)&&(e(t.trim()),z.current&&z.current.blur())},children:[(0,r.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,r.jsxs)("div",{className:"flex-grow relative",children:[r.jsx("div",{className:`absolute inset-0 -z-10 rounded-lg transition-all duration-300 ${D?"bg-gradient-to-r from-primary-100/50 to-secondary-100/50 blur-md":""}`}),(0,r.jsxs)("div",{className:"relative",children:[r.jsx(n.I,{ref:z,id:"ideaInput",value:t,onChange:e=>{s(e.target.value),k&&C(""),S&&Z("")},onFocus:()=>M(!0),onBlur:()=>M(!1),className:`w-full px-3 sm:px-5 py-5 sm:py-7 text-sm sm:text-base pr-12 transition-all duration-300 border-2 shadow-sm ${k?"border-red-500 focus-visible:ring-red-500":D?"border-primary-500 shadow-primary-200/40":""}`,placeholder:"e.g. sustainable fashion marketplace","aria-invalid":!!k,"aria-describedby":k?"search-error":void 0}),r.jsx(b.Z,{className:`absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-all duration-300 ${D?"text-primary-500":"text-gray-400"}`})]}),k&&(0,r.jsxs)(d.bZ,{variant:"destructive",className:"mt-2 py-2 animate-slide-up",children:[r.jsx(j.Z,{className:"h-4 w-4"}),r.jsx(d.X,{id:"search-error",className:"text-sm",children:k})]})]}),r.jsx("div",{className:"flex items-end",children:r.jsx(l.z,{type:"submit",className:`px-4 sm:px-6 py-5 sm:py-7 w-full sm:w-auto relative overflow-hidden transition-all duration-300 effect-3d ${a?"bg-gradient-to-r from-primary-600 to-secondary-600":"bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700"}`,disabled:!t.trim()||a,children:a?(0,r.jsxs)(r.Fragment,{children:[L,r.jsx(w.Z,{className:"w-5 h-5 mr-2 animate-spin"}),"Creating..."]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(N.Z,{className:"w-5 h-5 mr-2"}),r.jsx("span",{className:"hidden xs:inline",children:"Create Creative Domains"}),r.jsx("span",{className:"xs:hidden",children:"Create"})]})})})]}),(0,r.jsxs)("div",{className:"mt-4 sm:mt-6 flex flex-wrap gap-1.5 sm:gap-2",children:[r.jsx("span",{className:"text-xs sm:text-sm text-gray-600 mr-1 pt-1 w-full sm:w-auto mb-1 sm:mb-0",children:"Popular examples:"}),T.map(e=>(0,r.jsxs)(l.z,{type:"button",variant:"outline",size:"sm",className:`text-xs sm:text-sm h-auto px-2 sm:px-3 py-1 flex items-center gap-1 sm:gap-1.5 transition-all duration-300 ${S===e.name?"bg-gradient-to-r from-primary-100 to-primary-50 text-primary-700 border-primary-300 font-medium shadow-sm":"hover:border-primary-300 hover:text-primary-700"}`,onClick:()=>R(e.name),children:[P[e.name.toLowerCase()]||r.jsx(c.Z,{className:"w-3 h-3"}),e.displayName]},e.name))]})]})}[n,l,d]=k.then?(await k)():k,s()}catch(e){s(e)}})},7088:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{z:()=>x});var r=t(997),i=t(3749),n=t(5570),l=t(6263),o=t(8030),d=t(8655),c=t(3058),m=t(7908),h=t(3766),u=e([i,n,m]);function x({searchTerm:e="",searchStatus:a="idle",onGenerateMore:t}){let s=(0,l.tW)(e),{rateLimitInfo:u,isLimited:x}=(0,m.U)();return r.jsx(i.Zb,{className:"bg-white shadow-md mb-10 overflow-hidden border-t-4 border-t-primary-500",children:(0,r.jsxs)(i.aY,{className:"p-6",children:[(0,r.jsxs)("div",{className:"flex items-center mb-4",children:[r.jsx("div",{className:"w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3 animate-float-slow",children:r.jsx(o.Z,{className:"w-5 h-5 text-primary-600"})}),r.jsx("h2",{className:"text-xl font-bold text-gray-800",children:"Try these popular domain patterns"})]}),r.jsx("p",{className:"text-gray-600 mb-6 max-w-2xl",children:"While these common patterns can be useful, our AI excels at creating truly unique, creative domain names that go beyond conventional patterns to perfectly capture your ideas essence."}),r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5",children:s.map((e,a)=>(0,r.jsxs)("div",{className:"bg-gradient-to-br from-white to-gray-50 border border-gray-200 p-5 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1 effect-3d animate-scale-in",style:{animationDelay:`${.1*a}s`},children:[r.jsx("h3",{className:"font-medium text-lg mb-2 text-gradient",children:e.title}),r.jsx("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),r.jsx("div",{className:"flex flex-wrap gap-2 mt-2",children:e.examples.map((e,a)=>r.jsx("span",{className:"text-xs bg-white border border-primary-200 text-primary-700 px-2 py-1 rounded-full shadow-sm",children:e},a))})]},a))}),t&&(0,r.jsxs)("div",{className:"mt-8 flex flex-col items-center",children:[x&&u?(0,r.jsxs)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-center max-w-md",children:[(0,r.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[r.jsx(d.Z,{className:"h-5 w-5 text-red-500 mr-2"}),r.jsx("span",{className:"font-medium text-red-700",children:"Rate Limit Reached"})]}),(0,r.jsxs)("p",{className:"text-sm text-red-600 mb-2",children:["You've reached the limit of ",u.total," searches per hour."]}),(0,r.jsxs)("p",{className:"text-xs text-red-500",children:["Try again ",(0,h.Q)(new Date(u.resetTime),{addSuffix:!0})]})]}):null,r.jsx(n.z,{onClick:t,disabled:"generating"===a||x,className:`bg-gradient-to-r ${x?"from-gray-400 to-gray-500 cursor-not-allowed":"from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700"} shadow-md hover:shadow-lg transition-all`,children:"generating"===a?(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("svg",{className:"animate-spin h-4 w-4 mr-2 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[r.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),r.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Crafting Creative Domain Ideas..."]}):x?(0,r.jsxs)(r.Fragment,{children:[r.jsx(d.Z,{className:"w-4 h-4 mr-2"}),"Rate Limit Reached"]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(c.Z,{className:"w-4 h-4 mr-2"}),"Generate Creative & Unique Domains"]})})]})]})})}[i,n,m]=u.then?(await u)():u,s()}catch(e){s(e)}})},683:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{o:()=>o});var r=t(997),i=t(3749),n=t(6263),l=e([i]);function o(){let e=(0,n.ow)();return r.jsx("section",{id:"faq","aria-labelledby":"faq-heading",children:r.jsx(i.Zb,{className:"bg-white shadow-md",children:(0,r.jsxs)(i.aY,{className:"p-4 sm:p-6",children:[r.jsx("h2",{id:"faq-heading",className:"text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6",children:"Frequently Asked Questions About Our Domain Name Generator"}),r.jsx("div",{className:"space-y-6",children:e.map((a,t)=>(0,r.jsxs)("div",{className:t<e.length-1?"border-b pb-4 sm:pb-5":"",children:[r.jsx("h3",{className:"font-medium text-lg sm:text-xl mb-2 sm:mb-3 text-gray-700",children:a.question}),r.jsx("div",{className:"text-sm sm:text-base text-gray-600",dangerouslySetInnerHTML:{__html:a.answer}})]},t))})]})})})}i=(l.then?(await l)():l)[0],s()}catch(e){s(e)}})},7791:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{U:()=>f});var r=t(997),i=t(3749),n=t(5570),l=t(5121),o=t(6263),d=t(6689),c=t(1601),m=t(8865),h=t(7865),u=t(5432),x=t(8690),g=t(698),p=e([i,n,l,x]);function f({groupedDomain:e,index:a=0}){let[t,s]=(0,d.useState)(!1),[p,f]=(0,d.useState)(!1),y=(0,d.useRef)(null),{isFavorite:v,toggleFavorite:b}=(0,x.r)(),j=(()=>{let a=e.domains.find(e=>"com"===e.tld&&e.available);return a?a:e.domains.find(e=>e.available)||e.domains[0]})(),w=e.domains.length>1,N=e.availableTlds.length>0,k=e.domains.filter(e=>e.fullDomain!==j.fullDomain),C=N?"bg-gradient-to-r from-green-500 to-emerald-600 text-white":"bg-gradient-to-r from-red-500 to-rose-600 text-white",D=N?`${e.availableTlds.length} TLD${1!==e.availableTlds.length?"s":""} Available`:"Unavailable",M=N?"":"opacity-75",S=N?"Starting at":"Registered",Z=N?`${(0,o.T4)(void 0!==e.lowestPrice?e.lowestPrice:null)}/year`:"Unavailable",z=()=>{N&&s(!0)},T=()=>{f(!p)};return(0,r.jsxs)("div",{className:"relative transform transition-all duration-500 animate-scale-in",style:{animationDelay:`${.1*a}s`},ref:y,children:[r.jsx(g.G,{show:t&&N,onComplete:()=>s(!1)}),r.jsx("div",{className:"relative",children:r.jsx("div",{className:`${M} transition-all duration-300 relative overflow-hidden rounded-xl shadow-lg`,children:(0,r.jsxs)(i.Zb,{className:"border-0 bg-transparent overflow-hidden h-full",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center border-b p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[r.jsx(n.z,{variant:"ghost",size:"icon",className:`h-7 w-7 rounded-full ${v(j.fullDomain)?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-400"}`,onClick:()=>b(j,k),title:v(j.fullDomain)?"Remove from favorites":"Add to favorites",children:r.jsx(c.Z,{className:"h-4 w-4",fill:v(j.fullDomain)?"currentColor":"none"})}),(0,r.jsxs)("h3",{className:"font-medium text-base sm:text-lg transition-all duration-300 break-words",children:[j.fullDomain,w&&(0,r.jsxs)("span",{className:"text-gray-500 text-sm ml-1",children:["(+",e.domains.length-1," more)"]})]})]}),(0,r.jsxs)(l.C,{className:`${C} shadow transition-all duration-300`,children:[N&&r.jsx(m.Z,{className:"w-3 h-3 mr-1"}),D]})]}),(0,r.jsxs)(i.aY,{className:"p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center mb-3 sm:mb-4",children:[r.jsx("span",{className:"text-xs sm:text-sm text-gray-600 transition-all duration-300",children:S}),r.jsx("span",{className:"text-xs sm:text-sm font-medium transition-all duration-300",children:Z})]}),N&&(0,r.jsxs)("div",{className:"mb-3 sm:mb-4",children:[(0,r.jsxs)("div",{className:"flex flex-wrap gap-1.5 sm:gap-2 mb-2",children:[e.availableTlds.slice(0,p?void 0:3).map(a=>{let t=e.domains.find(e=>e.tld===a),s=t?.price!==void 0?(0,o.T4)(t.price):"N/A";return(0,r.jsxs)(n.z,{variant:"outline",size:"sm",className:"bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center gap-1 text-xs sm:text-sm py-1 h-auto",onClick:()=>{z(),window.open(`https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D${encodeURIComponent(t?t.fullDomain:`${e.baseName}.${a}`)}`)},children:[r.jsx(h.Z,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:[".",a]}),(0,r.jsxs)("span",{className:"text-xs opacity-75 ml-1",children:[s,"/yr"]})]},a)}),!p&&e.availableTlds.length>3&&(0,r.jsxs)(n.z,{variant:"ghost",size:"sm",className:"text-gray-600 hover:bg-gray-100 hover:text-gray-900",onClick:T,children:["+",e.availableTlds.length-3," more"]})]}),e.availableTlds.length>3&&p&&(0,r.jsxs)(n.z,{variant:"ghost",size:"sm",className:"w-full text-xs flex items-center justify-center py-1 text-gray-600 hover:bg-gray-100 hover:text-gray-900",onClick:T,children:[r.jsx(u.Z,{className:"w-3 h-3 mr-1"}),"Show fewer options"]})]}),!N&&r.jsx("div",{children:r.jsx(n.z,{className:"w-full transition-all duration-300 overflow-hidden relative",variant:"outline",disabled:!0,children:r.jsx("span",{className:"relative z-10",children:"Not Available"})})})]})]})})})]})}[i,n,l,x]=p.then?(await p)():p,s()}catch(e){s(e)}})},9799:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{G:()=>c});var r=t(997),i=t(7908),n=t(8655),l=t(3766),o=t(8408),d=e([i,o]);function c(){let{rateLimitInfo:e,isLimited:a,isLoading:t}=(0,i.U)();if(t||!e||!a)return null;let{remaining:s,total:d,resetTime:c}=e,m=new Date(c),h=(0,l.Q)(m,{addSuffix:!0}),u=Math.ceil((m.getTime()-new Date().getTime())/6e4);return(0,r.jsxs)("div",{className:"flex flex-col space-y-2 p-3 bg-white rounded-lg shadow-sm border border-red-200",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[r.jsx(n.Z,{className:"h-4 w-4 text-red-500"}),r.jsx("span",{className:"text-sm font-medium text-red-700",children:"Rate limit reached"})]}),(0,r.jsxs)("span",{className:"text-xs text-gray-600",children:["Resets in ",u," minutes"]})]}),r.jsx(o.E,{value:100,className:"h-2 bg-red-100",indicatorClassName:"bg-red-500"}),(0,r.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["You've reached the hourly search limit. Please try again ",h,"."]})]})}[i,o]=d.then?(await d)():d,s()}catch(e){s(e)}})},8490:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{x:()=>o});var r=t(997),i=t(5794),n=t(8690),l=e([i,n]);function o(){let{favorites:e}=(0,n.r)();return 0===e.length?null:r.jsx(i.V,{})}[i,n]=l.then?(await l)():l,s()}catch(e){s(e)}})},9063:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{W:()=>p});var r=t(997),i=t(1603),n=t(3835),l=t(8865),o=t(2190),d=t(5570),c=t(4522),m=t(3296),h=t(1239),u=t(6689),x=t(2485),g=e([d,c,m,h,x]);function p({tlds:e,selectedTlds:a,onChange:t}){let[s,g]=(0,u.useState)(!1),p=e=>{let s=Array.isArray(a)?a:[];s.includes(e)?t(s.filter(a=>a!==e)):t([...s,e])},f=()=>{t([]),g(!1)};return(0,r.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,r.jsxs)("div",{className:"flex justify-between items-center",children:[(0,r.jsxs)("label",{className:"text-sm font-medium flex items-center gap-1 text-gray-700",children:[r.jsx(i.Z,{className:"w-3.5 h-3.5 text-primary-500"}),"TLD Extensions"]}),(0,r.jsxs)("div",{className:"space-x-1",children:[r.jsx(d.z,{variant:"link",size:"sm",className:"text-xs h-auto p-0 text-primary-600 hover:text-primary-700",onClick:()=>{Array.isArray(e)&&e.length>0&&t([...e])},children:"All"}),r.jsx("span",{className:"text-xs text-gray-300",children:"|"}),r.jsx(d.z,{variant:"link",size:"sm",className:"text-xs h-auto p-0 text-primary-600 hover:text-primary-700",onClick:f,children:"Clear"})]})]}),(0,r.jsxs)(m.J2,{open:s,onOpenChange:g,children:[r.jsx(m.xo,{asChild:!0,children:(0,r.jsxs)(d.z,{variant:"outline",role:"combobox","aria-expanded":s,className:"justify-between bg-white border-gray-200 hover:border-primary-300 hover:bg-primary-50/50 h-auto py-2",children:[r.jsx("span",{className:"truncate",children:Array.isArray(a)&&0!==a.length?Array.isArray(e)&&0!==e.length&&a.length===e.length?"All TLDs":`${a.length} TLD${a.length>1?"s":""} selected`:"Select TLDs"}),r.jsx(n.Z,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),r.jsx(m.yk,{className:"p-0 w-[calc(100vw-2rem)] sm:w-full min-w-[240px] bg-white shadow-xl border-gray-200",children:(0,r.jsxs)(c.mY,{children:[r.jsx(c.sZ,{placeholder:"Search TLDs...",className:"h-9 border-b rounded-none"}),r.jsx(c.rb,{children:"No TLD found."}),r.jsx(c.fu,{className:"max-h-[40vh] sm:max-h-[200px] overflow-y-auto p-1.5",children:Array.isArray(e)&&e.length>0?e.map(e=>r.jsx(c.di,{value:e,onSelect:()=>p(e),className:`rounded-md mb-1 ${a.includes(e)?"bg-primary-50 text-primary-700 font-medium":""}`,children:(0,r.jsxs)("div",{className:"flex items-center w-full",children:[r.jsx(l.Z,{className:(0,h.cn)("mr-2 h-3.5 w-3.5",a.includes(e)?"opacity-100 text-primary-500":"opacity-0")}),r.jsx(x.O,{tld:e,selected:a.includes(e)})]})},e)):r.jsx(c.di,{value:"placeholder",disabled:!0,children:"No TLDs available"})}),(0,r.jsxs)("div",{className:"border-t p-2 flex justify-between",children:[(0,r.jsxs)("span",{className:"text-xs text-gray-500",children:[a.length," of ",e.length," selected"]}),(0,r.jsxs)("div",{className:"flex gap-2",children:[r.jsx(d.z,{variant:"ghost",size:"sm",className:"h-8 text-xs",onClick:f,children:"Clear"}),r.jsx(d.z,{size:"sm",className:"h-8 text-xs",onClick:()=>g(!1),children:"Apply"})]})]})]})})]}),Array.isArray(a)&&a.length>0&&r.jsx("div",{className:"flex flex-wrap gap-1 xs:gap-1.5 mt-2 animate-scale-in",children:a.map((e,a)=>(0,r.jsxs)("div",{className:"relative group animate-scale-in",style:{animationDelay:`${.05*a}s`},children:[r.jsx(x.O,{tld:e,interactive:!0,animation:"pulse",onClick:()=>p(e),className:"pr-6 text-xs sm:text-sm"}),r.jsx("button",{className:"absolute right-1.5 top-1/2 -translate-y-1/2 w-3.5 h-3.5 rounded-full bg-gray-200 group-hover:bg-gray-300 flex items-center justify-center text-gray-500 group-hover:text-gray-700 transition-colors",onClick:()=>p(e),"aria-label":`Remove ${e}`,children:r.jsx(o.Z,{className:"w-2 h-2"})})]},e))})]})}[d,c,m,h,x]=g.then?(await g)():g,s()}catch(e){s(e)}})},9013:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{Cd:()=>m,X:()=>h,bZ:()=>c});var r=t(997),i=t(6689),n=t(6926),l=t(1239),o=e([n,l]);[n,l]=o.then?(await o)():o;let d=(0,n.cva)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),c=i.forwardRef(({className:e,variant:a,...t},s)=>r.jsx("div",{ref:s,role:"alert",className:(0,l.cn)(d({variant:a}),e),...t}));c.displayName="Alert";let m=i.forwardRef(({className:e,...a},t)=>r.jsx("h5",{ref:t,className:(0,l.cn)("mb-1 font-medium leading-none tracking-tight",e),...a}));m.displayName="AlertTitle";let h=i.forwardRef(({className:e,...a},t)=>r.jsx("div",{ref:t,className:(0,l.cn)("text-sm [&_p]:leading-relaxed",e),...a}));h.displayName="AlertDescription",s()}catch(e){s(e)}})},4522:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{di:()=>g,fu:()=>x,mY:()=>m,rb:()=>u,sZ:()=>h});var r=t(997),i=t(6689),n=t(907),l=t(6127),o=t(1239),d=t(7616),c=e([n,o,d]);[n,o,d]=c.then?(await c)():c;let m=i.forwardRef(({className:e,...a},t)=>r.jsx(n.Command,{ref:t,className:(0,o.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",e),...a}));m.displayName=n.Command.displayName;let h=i.forwardRef(({className:e,...a},t)=>(0,r.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[r.jsx(l.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),r.jsx(n.Command.Input,{ref:t,className:(0,o.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",e),...a})]}));h.displayName=n.Command.Input.displayName,i.forwardRef(({className:e,...a},t)=>r.jsx(n.Command.List,{ref:t,className:(0,o.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",e),...a})).displayName=n.Command.List.displayName;let u=i.forwardRef((e,a)=>r.jsx(n.Command.Empty,{ref:a,className:"py-6 text-center text-sm",...e}));u.displayName=n.Command.Empty.displayName;let x=i.forwardRef(({className:e,...a},t)=>r.jsx(n.Command.Group,{ref:t,className:(0,o.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",e),...a}));x.displayName=n.Command.Group.displayName,i.forwardRef(({className:e,...a},t)=>r.jsx(n.Command.Separator,{ref:t,className:(0,o.cn)("-mx-1 h-px bg-border",e),...a})).displayName=n.Command.Separator.displayName;let g=i.forwardRef(({className:e,...a},t)=>r.jsx(n.Command.Item,{ref:t,className:(0,o.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",e),...a}));g.displayName=n.Command.Item.displayName,s()}catch(e){s(e)}})},7616:(e,a,t)=>{t.a(e,async(e,a)=>{try{var s=t(997),r=t(6689),i=t(7715),n=t(2190),l=t(1239),o=e([i,l]);[i,l]=o.then?(await o)():o,i.Root,i.Trigger;let d=i.Portal;i.Close;let c=r.forwardRef(({className:e,...a},t)=>s.jsx(i.Overlay,{ref:t,className:(0,l.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",e),...a}));c.displayName=i.Overlay.displayName,r.forwardRef(({className:e,children:a,...t},r)=>(0,s.jsxs)(d,{children:[s.jsx(c,{}),(0,s.jsxs)(i.Content,{ref:r,className:(0,l.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",e),...t,children:[a,(0,s.jsxs)(i.Close,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[s.jsx(n.Z,{className:"h-4 w-4"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]})).displayName=i.Content.displayName,r.forwardRef(({className:e,...a},t)=>s.jsx(i.Title,{ref:t,className:(0,l.cn)("text-lg font-semibold leading-none tracking-tight",e),...a})).displayName=i.Title.displayName,r.forwardRef(({className:e,...a},t)=>s.jsx(i.Description,{ref:t,className:(0,l.cn)("text-sm text-muted-foreground",e),...a})).displayName=i.Description.displayName,a()}catch(e){a(e)}})},3296:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{J2:()=>d,xo:()=>c,yk:()=>m});var r=t(997),i=t(6689),n=t(8680),l=t(1239),o=e([n,l]);[n,l]=o.then?(await o)():o;let d=n.Root,c=n.Trigger,m=i.forwardRef(({className:e,align:a="center",sideOffset:t=4,...s},i)=>r.jsx(n.Portal,{children:r.jsx(n.Content,{ref:i,align:a,sideOffset:t,className:(0,l.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...s})}));m.displayName=n.Content.displayName,s()}catch(e){s(e)}})},8408:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{E:()=>d});var r=t(997),i=t(6689),n=t(6005),l=t(1239),o=e([n,l]);[n,l]=o.then?(await o)():o;let d=i.forwardRef(({className:e,value:a,indicatorClassName:t,animated:s=!1,...i},o)=>r.jsx(n.Root,{ref:o,className:(0,l.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",e),...i,children:r.jsx(n.Indicator,{className:(0,l.cn)("h-full flex-1 bg-primary transition-all rounded-full",s&&"animate-progress-pulse",t),style:{width:`${a||0}%`}})}));d.displayName=n.Root.displayName,s()}catch(e){s(e)}})},2936:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{RM:()=>c,SC:()=>m,iA:()=>o,pj:()=>u,ss:()=>h,xD:()=>d});var r=t(997),i=t(6689),n=t(1239),l=e([n]);n=(l.then?(await l)():l)[0];let o=i.forwardRef(({className:e,...a},t)=>r.jsx("div",{className:"relative w-full overflow-auto",children:r.jsx("table",{ref:t,className:(0,n.cn)("w-full caption-bottom text-sm",e),...a})}));o.displayName="Table";let d=i.forwardRef(({className:e,...a},t)=>r.jsx("thead",{ref:t,className:(0,n.cn)("[&_tr]:border-b",e),...a}));d.displayName="TableHeader";let c=i.forwardRef(({className:e,...a},t)=>r.jsx("tbody",{ref:t,className:(0,n.cn)("[&_tr:last-child]:border-0",e),...a}));c.displayName="TableBody",i.forwardRef(({className:e,...a},t)=>r.jsx("tfoot",{ref:t,className:(0,n.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",e),...a})).displayName="TableFooter";let m=i.forwardRef(({className:e,...a},t)=>r.jsx("tr",{ref:t,className:(0,n.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",e),...a}));m.displayName="TableRow";let h=i.forwardRef(({className:e,...a},t)=>r.jsx("th",{ref:t,className:(0,n.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",e),...a}));h.displayName="TableHead";let u=i.forwardRef(({className:e,...a},t)=>r.jsx("td",{ref:t,className:(0,n.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",e),...a}));u.displayName="TableCell",i.forwardRef(({className:e,...a},t)=>r.jsx("caption",{ref:t,className:(0,n.cn)("mt-4 text-sm text-muted-foreground",e),...a})).displayName="TableCaption",s()}catch(e){s(e)}})},8133:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{SP:()=>m,dr:()=>c,mQ:()=>d,nU:()=>h});var r=t(997),i=t(6689),n=t(6860),l=t(1239),o=e([n,l]);[n,l]=o.then?(await o)():o;let d=n.Root,c=i.forwardRef(({className:e,...a},t)=>r.jsx(n.List,{ref:t,className:(0,l.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",e),...a}));c.displayName=n.List.displayName;let m=i.forwardRef(({className:e,...a},t)=>r.jsx(n.Trigger,{ref:t,className:(0,l.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",e),...a}));m.displayName=n.Trigger.displayName;let h=i.forwardRef(({className:e,...a},t)=>r.jsx(n.Content,{ref:t,className:(0,l.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",e),...a}));h.displayName=n.Content.displayName,s()}catch(e){s(e)}})},8322:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{g:()=>o});var r=t(997),i=t(6689),n=t(1239),l=e([n]);n=(l.then?(await l)():l)[0];let o=i.forwardRef(({className:e,...a},t)=>r.jsx("textarea",{className:(0,n.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",e),ref:t,...a}));o.displayName="Textarea",s()}catch(e){s(e)}})},2485:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{O:()=>d});var r=t(997);t(6689);var i=t(6926),n=t(1239),l=t(9757),o=e([i,n]);[i,n]=o.then?(await o)():o;let c=(0,i.cva)("inline-flex items-center justify-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200 border",{variants:{variant:{outline:"",filled:"",subtle:"border-transparent"},animation:{none:"",pulse:"hover:animate-pulse",bounce:"hover:animate-bounce",wiggle:"hover:animate-wiggle"},interactive:{true:"cursor-pointer transform transition-transform hover:scale-110",false:""}},defaultVariants:{variant:"filled",animation:"none",interactive:!1}});function d({className:e,tld:a,selected:t=!1,variant:s,animation:i,interactive:o,count:d,...m}){let h=(0,l.Co)(a),u=a.startsWith(".")?a:`.${a}`;return(0,r.jsxs)("span",{className:(0,n.cn)(c({variant:s,animation:i,interactive:o}),h,t?"ring-2 ring-offset-1 shadow-md":"",e),...m,children:[u,void 0!==d&&r.jsx("span",{className:"ml-1.5 inline-flex items-center justify-center rounded-full bg-white/30 px-1.5 text-[0.625rem]",children:d})]})}s()}catch(e){s(e)}})},2811:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{S:()=>m});var r=t(6689),i=t(9752),n=t(5079),l=t(7908),o=t(9527),d=t(2917),c=e([i,l]);function m(e){let[a,t]=(0,r.useState)([]),[s,c]=(0,r.useState)("idle");(0,i.useQueryClient)();let{toast:m}=(0,n.pm)(),{trackEvent:h}=(0,o.z)(),{authFetch:u}=(0,d.K)(),{rateLimitInfo:x,isLimited:g,refetch:p}=(0,l.U)(),f=(0,i.useMutation)({mutationFn:async a=>{if(!a||0===a.length)throw Error("No domains to check");return await u("/api/domains/check-bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({domains:a,tlds:e})})},onSuccess:a=>{t(a.domains||[]),c("complete"),h("bulk_domain_check",{domains_count:a.domains.length,tlds:e.join(","),num_tlds:e.length})},onError:e=>{c("error"),m({title:"Error checking domains",description:"There was an error checking domain availability. Please try again.",variant:"destructive"})}});return{checkDomains:(0,r.useCallback)(async e=>{if(0===e.length){m({title:"No domains to check",description:"Please enter at least one domain name.",variant:"destructive"});return}if(await p(),g){m({title:"Rate Limit Reached",description:`You've reached the limit of ${x.total} searches per hour. Please try again after ${new Date(x.resetTime).toLocaleTimeString()}.`,variant:"destructive"});return}c("checking");try{await f.mutateAsync(e),p()}catch(e){}},[f,g,x,p,e,m]),domains:a,isLoading:f.isPending,error:f.error?f.error.message:null,checkStatus:s}}[i,l]=c.then?(await c)():c,s()}catch(e){s(e)}})},9382:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{J:()=>c});var r=t(6689),i=t(9752),n=t(5079),l=t(7908),o=t(9527),d=e([i,l]);[i,l]=d.then?(await d)():d;let c=e=>{let[a,t]=(0,r.useState)(""),[s,d]=(0,r.useState)("idle");(0,i.useQueryClient)();let{toast:c}=(0,n.pm)(),{trackEvent:m}=(0,o.z)(),{rateLimitInfo:h,isLimited:u,refetch:x}=(0,l.U)(),{data:g,isLoading:p,error:f,refetch:y,isRefetching:v}=(0,i.useQuery)({queryKey:["/api/domains/check-custom",a,e],enabled:a.length>1,meta:{queryParams:{name:a,tlds:e.join(",")}}}),b=async a=>{if(u){c({title:"Rate limit reached",description:"You've reached the maximum number of searches per hour. Please try again later.",variant:"destructive"});return}if(a.length<2){c({title:"Invalid domain name",description:"Domain name must be at least 2 characters long.",variant:"destructive"});return}t(a),d("checking");try{await y(),d("complete"),m("custom_domain_check",{domain_name:a,tlds:e.join(","),num_tlds:e.length}),x()}catch(e){console.error("Error checking domain availability:",e),d("error"),c({title:"Error checking domain",description:"There was an error checking domain availability. Please try again.",variant:"destructive"})}};return{domainName:a,checkDomainAvailability:b,isLoading:p||v,error:f,data:g,availableDomains:g?.domains?g.domains.filter(e=>e.available):[],unavailableDomains:g?.domains?g.domains.filter(e=>!e.available):[],stats:(()=>{if(!g?.domains)return{available:0,unavailable:0};let e=g.domains.filter(e=>e.available).length,a=g.domains.length-e;return{available:e,unavailable:a}})(),checkStatus:s,hasChecked:a.length>0&&"complete"===s}};s()}catch(e){s(e)}})},8602:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{f:()=>u});var r=t(6689),i=t(9752),n=t(9757),l=t(5079),o=t(7908),d=t(9527),c=t(8486),m=t(3293),h=e([i,o]);[i,o]=h.then?(await h)():h;let u=()=>{let[e,a]=(0,r.useState)(""),[t,s]=(0,r.useState)("all"),[h,u]=(0,r.useState)([]),[x,g]=(0,r.useState)("idle"),p=(0,i.useQueryClient)(),{toast:f}=(0,l.pm)(),{trackEvent:y}=(0,d.z)(),{rateLimitInfo:v,isLimited:b,refetch:j}=(0,o.U)(),{data:w}=(0,i.useQuery)({queryKey:["/api/domains/tlds"],placeholderData:{tlds:(0,n.Ps)()}}),{data:N,isLoading:k,error:C,refetch:D}=(0,i.useQuery)({queryKey:["/api/domains/search",e,h],enabled:e.length>1,meta:{queryParams:{term:e,tlds:h.join(","),limit:m.y.toString()}}});(0,r.useEffect)(()=>{0===h.length&&w?.tlds&&w.tlds.length>0&&u(w.tlds)},[w,h.length]),(0,r.useEffect)(()=>{k&&e.length>1?g("generating"):e.length>1&&!k&&g("done")},[k,e]);let M=async e=>{if(e&&!(e.length<2)){if(await j(),b){f({title:"Rate Limit Reached",description:`You've reached the limit of ${v.total} searches per hour. Please try again after ${new Date(v.resetTime).toLocaleTimeString()}.`,variant:"destructive"});return}a(e),g("generating");try{0===h.length&&w?.tlds&&u(w.tlds),await p.invalidateQueries({queryKey:["/api/domains/search",e,h]}),await D(),y("domain_search",{search_term:e,tlds:h.join(","),num_tlds:h.length}),j()}catch(t){let a="Error searching domains. Please try again.";t instanceof Error&&(t.message.includes("400")?a="Your search description may be too long or contains invalid characters. Please try a shorter description.":t.message.includes("429")&&(a="Rate limit exceeded. Please try again later.",j())),p.setQueryData(["/api/domains/search",e,h],{error:a,domains:[],searchTerm:e,timestamp:new Date().toISOString()}),f({title:"Search Error",description:a,variant:"destructive"}),g("done")}}},S=async()=>{if(e&&!(e.length<2)){if(y("generate_more_domains",{search_term:e}),await j(),b){f({title:"Rate Limit Reached",description:`You've reached the limit of ${v.total} searches per hour. Please try again after ${new Date(v.resetTime).toLocaleTimeString()}.`,variant:"destructive"});return}g("generating");try{let a=await (0,c.SC)(`/api/domains/search?term=${encodeURIComponent(e)}&tlds=${encodeURIComponent(h.join(","))}&limit=${m.y}`);if(!a.ok){let t=await a.text(),s="Failed to generate more suggestions. Please try again.";400===a.status?s="Your search description may be too long or contains invalid characters. Please try a shorter description.":429===a.status?(s="Rate limit exceeded. Please try again later.",j()):500===a.status&&(s="Server error while generating suggestions. Please try again later.");let r=p.getQueryData(["/api/domains/search",e,h]);throw p.setQueryData(["/api/domains/search",e,h],{error:s,domains:r?.domains||[],searchTerm:e,timestamp:new Date().toISOString()}),f({title:"Error",description:s,variant:"destructive"}),Error(`API responded with ${a.status}: ${t||"Unknown error"}`)}let t=await a.json();console.log("Received new domain suggestions:",t);let s=p.getQueryData(["/api/domains/search",e,h]),r=s?.domains||[],i=t.domains,n=new Map;r.forEach(e=>{n.set(e.fullDomain,e)});let l=[...r];i.forEach(e=>{let a=n.get(e.fullDomain);if(a){if(e.available&&!a.available){let a=l.findIndex(a=>a.fullDomain===e.fullDomain);-1!==a&&(l[a]=e)}}else l.push(e)});let o={...t,domains:l};p.setQueryData(["/api/domains/search",e,h],o),j(),g("done")}catch(e){console.error("Error generating more domain suggestions:",e),g("done")}}},Z=()=>N?.domains?N.domains.filter(e=>{let a=e.available,t=!(h.length>0)||h.includes(e.tld);return a&&t}):[],z=e=>{let a=new Map;return e.forEach(e=>{a.has(e.name)||a.set(e.name,new Map);let t=a.get(e.name),s=t.get(e.tld);s&&(s.available||!e.available)||t.set(e.tld,e)}),Array.from(a.entries()).map(([e,a])=>{let t=Array.from(a.values()),s=t.filter(e=>e.available),r=t.filter(e=>!e.available),i=s.length>0?Math.min(...s.filter(e=>void 0!==e.price).map(e=>{let a=e.price;if("string"==typeof a){let e=parseFloat(a);return isNaN(e)?1/0:e}return"number"==typeof a?a:1/0})):void 0,n=t[0]?.type;return{baseName:e,domains:t,availableTlds:s.map(e=>e.tld),unavailableTlds:r.map(e=>e.tld),lowestPrice:i===1/0?void 0:i,type:n}})},T=()=>[...Z()].sort((e,a)=>e.available!==a.available?e.available?-1:1:e.fullDomain.localeCompare(a.fullDomain)),P=(0,r.useMemo)(()=>{let e=T();return z(e)},[h,t,N?.domains]),A=(0,r.useMemo)(()=>[...P].sort((e,a)=>e.availableTlds.length!==a.availableTlds.length?a.availableTlds.length-e.availableTlds.length:e.baseName.localeCompare(a.baseName)),[P]);return{searchTerm:e,setSearchTerm:a,searchDomains:M,isLoading:k,error:C,data:N,domains:T(),groupedDomains:A,filterBy:t,setFilterBy:s,stats:(()=>{if(!N?.domains)return{available:0,unavailable:0};let e=N.domains.filter(e=>e.available).length,a=N.domains.length-e;return{available:e,unavailable:a}})(),hasMadeSearch:e.length>0,popularTlds:w?.tlds||[],selectedTlds:h,setSelectedTlds:u,searchStatus:x,refetch:D,generateMoreSuggestions:S}};s()}catch(e){s(e)}})},7908:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.d(a,{U:()=>l});var r=t(9752),i=t(8486),n=e([r]);function l(){let{data:e,isLoading:a,error:t,refetch:s}=(0,r.useQuery)({queryKey:["/api/rate-limit"],refetchOnWindowFocus:!0,refetchInterval:6e4,queryFn:async({queryKey:e})=>{try{let a=await (0,i.SC)(e[0]),t=await a.json();return a.status,t}catch(e){throw e}}}),n="rateLimited"in(e||{})?e.rateLimitInfo:e?.rateLimitInfo;return{rateLimitInfo:n,isLimited:"rateLimited"in(e||{})||n?.isLimited||!1,isLoading:a,error:t,refetch:s}}r=(n.then?(await n)():n)[0],s()}catch(e){s(e)}})},893:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},341:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},3835:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},3050:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7695:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2629:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Dumbbell",[["path",{d:"M14.4 14.4 9.6 9.6",key:"ic80wn"}],["path",{d:"M18.657 21.485a2 2 0 1 1-2.829-2.828l-1.767 1.768a2 2 0 1 1-2.829-2.829l6.364-6.364a2 2 0 1 1 2.829 2.829l-1.768 1.767a2 2 0 1 1 2.828 2.829z",key:"nnl7wr"}],["path",{d:"m21.5 21.5-1.4-1.4",key:"1f1ice"}],["path",{d:"M3.9 3.9 2.5 2.5",key:"1evmna"}],["path",{d:"M6.404 12.768a2 2 0 1 1-2.829-2.829l1.768-1.767a2 2 0 1 1-2.828-2.829l2.828-2.828a2 2 0 1 1 2.829 2.828l1.767-1.768a2 2 0 1 1 2.829 2.829z",key:"yhosts"}]])},2020:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},6215:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8453:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Laptop",[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16",key:"tarvll"}]])},8178:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]])},8030:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},4184:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("ListChecks",[["path",{d:"m3 17 2 2 4-4",key:"1jhpwq"}],["path",{d:"m3 7 2 2 4-4",key:"1obspn"}],["path",{d:"M13 6h8",key:"15sg57"}],["path",{d:"M13 12h8",key:"h98zly"}],["path",{d:"M13 18h8",key:"oe0vm4"}]])},3243:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},1475:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("PenTool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]])},4976:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},2125:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]])},734:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},6194:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},3058:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},1793:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]])},1603:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},8806:(e,a,t)=>{t.d(a,{Z:()=>s});let s=(0,t(1134).Z)("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])},1854:(e,a,t)=>{t.a(e,async(e,s)=>{try{t.r(a),t.d(a,{default:()=>S,getStaticProps:()=>z});var r=t(997),i=t(968),n=t.n(i),l=t(3749),o=t(3118),d=t(2304),c=t(9063),m=t(683),h=t(2498),u=t(7304),x=t(5570),g=t(5432),p=t(6127),f=t(4184),y=t(4640),v=t(8602),b=t(6689),j=t(9799),w=t(8490),N=t(892),k=t(3558),C=t(9382),D=t(3465),M=e([l,o,d,c,m,h,u,x,y,v,j,w,N,k,C,D]);function S({}){let{searchTerm:e,searchDomains:a,isLoading:t,domains:s,groupedDomains:i,hasMadeSearch:M,popularTlds:S,selectedTlds:z,setSelectedTlds:T,searchStatus:P,refetch:A,generateMoreSuggestions:R}=(0,v.f)(),{domainName:L,checkDomainAvailability:$,isLoading:F,availableDomains:W,unavailableDomains:q,hasChecked:_}=(0,C.J)(z),I=t||F,[E,U]=(0,b.useState)(!1),[Y,G]=(0,b.useState)("single");return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(n(),{children:[r.jsx("title",{children:"Domain Name Generator & Business Name Maker | DomainMate"}),r.jsx("meta",{name:"description",content:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."}),r.jsx("meta",{name:"keywords",content:"domain name generator, domain name maker, business name generator for free, business name generator ai, ai domain generator, domain availability checker, brand name ideas, domain suggestions, available domains"}),r.jsx("meta",{property:"og:title",content:"Domain Name Generator & Business Name Maker | DomainMate"}),r.jsx("meta",{property:"og:description",content:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."}),r.jsx("meta",{property:"og:url",content:"https://domainmate.net"}),r.jsx("meta",{property:"og:type",content:"website"}),r.jsx("meta",{name:"twitter:card",content:"summary_large_image"}),r.jsx("meta",{name:"twitter:title",content:"Domain Name Generator & Business Name Maker | DomainMate"}),r.jsx("meta",{name:"twitter:description",content:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."}),r.jsx("link",{rel:"canonical",href:"https://domainmate.net/"}),r.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"WebApplication",name:"Domain Name Generator & Business Name Maker | DomainMate",url:"https://domainmate.net",description:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly.",applicationCategory:"BusinessApplication",operatingSystem:"All",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},creator:{"@type":"Organization",name:"DomainMate",url:"https://domainmate.net"}})}})]}),r.jsx("div",{className:"font-sans text-gray-700 min-h-screen",children:(0,r.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[r.jsx("header",{className:"mb-10",children:r.jsx(h.w,{})}),(0,r.jsxs)("main",{children:[(0,r.jsxs)("div",{className:"mb-6",children:[(0,r.jsxs)(l.Zb,{className:"bg-white shadow-xl mb-8 border-0 overflow-hidden",children:[r.jsx("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-teal-500"}),(0,r.jsxs)(l.aY,{className:"p-6 sm:p-8",children:[(0,r.jsxs)("div",{className:"text-center mb-8",children:[r.jsx("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 mb-4",children:"AI Domain Name Generator"}),r.jsx("h2",{className:"text-xl sm:text-2xl font-semibold text-gray-700 mb-3",children:"Find the perfect available domain name instantly"}),r.jsx("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-2",id:"main-description",style:{contentVisibility:"auto"},children:"Our advanced AI domain name generator creates highly creative, distinctive name suggestions that perfectly capture your business idea's essence."}),r.jsx("p",{className:"text-md text-gray-600 max-w-3xl mx-auto",children:"We prioritize creativity and relevance, then verify domain availability in real-time so you'll only see domains you can register immediately."})]}),(0,r.jsxs)("div",{className:"border-t border-gray-100 pt-8",children:[r.jsx(o.$,{onSearch:e=>{U(!1),a(e)},isLoading:I}),r.jsx("div",{className:"mt-4 max-w-md mx-auto",children:r.jsx(x.z,{variant:"outline",size:"sm",className:"w-full flex items-center justify-center gap-2",onClick:()=>U(!E),children:E?(0,r.jsxs)(r.Fragment,{children:[r.jsx(g.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Hide custom domain check"})]}):(0,r.jsxs)(r.Fragment,{children:[r.jsx(p.Z,{className:"w-4 h-4"}),r.jsx("span",{children:"Check domain availability or bulk check"})]})})}),E&&(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)("div",{className:"mt-4 max-w-md mx-auto p-4 bg-white rounded-lg shadow-sm border border-gray-100",children:[r.jsx(N.B,{onCheck:$,isLoading:F}),r.jsx("div",{className:"mt-4 pt-4 border-t border-gray-100",children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("div",{className:"text-sm text-gray-600",children:"Want to check multiple domains at once?"}),(0,r.jsxs)(x.z,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>G("bulk"),children:[r.jsx(f.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Bulk Check"})]})]})})]}),r.jsx("div",{className:"mt-4",children:r.jsx(k.p,{domainName:L,availableDomains:W,unavailableDomains:q,isLoading:F,hasChecked:_})})]}),"bulk"===Y&&(0,r.jsxs)("div",{className:"mt-6",children:[r.jsx("div",{className:"mb-4",children:(0,r.jsxs)(x.z,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>G("single"),children:[r.jsx(p.Z,{className:"h-4 w-4"}),r.jsx("span",{children:"Back to Single Domain Search"})]})}),r.jsx(D.A,{selectedTlds:z})]})]})]})]}),(0,r.jsxs)("div",{className:"mt-4 max-w-md mx-auto",children:[r.jsx(c.W,{tlds:S||[],selectedTlds:z,onChange:a=>{T(a),M&&e&&setTimeout(()=>{A()},100)}}),r.jsx("div",{className:"mt-4",children:r.jsx(j.G,{})})]}),r.jsx("div",{className:"mt-6",children:r.jsx(w.x,{})})]}),r.jsx(l.Zb,{className:"bg-white shadow-md mb-10",children:(0,r.jsxs)(l.aY,{className:"p-6",children:[!M&&!I&&(0,r.jsxs)("div",{className:"text-center py-10",children:[r.jsx(Z,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),r.jsx("p",{className:"text-lg text-gray-500",children:"Enter your idea above to get domain suggestions"})]}),I&&r.jsx("div",{className:"relative py-8 overflow-hidden bg-white rounded-lg",children:r.jsx("div",{className:"relative z-10",children:r.jsx(y.b,{isLoading:I,searchStatus:P,searchTerm:e})})}),M&&!I&&(0,r.jsxs)("div",{children:[r.jsx("div",{className:"mb-6",children:(0,r.jsxs)("h2",{className:"text-xl font-bold text-gray-800",children:["Domain suggestions for ",r.jsx("span",{className:"text-blue-600",children:e})]})}),r.jsx(d._,{domains:s,groupedDomains:i,isLoading:I,searchStatus:P,onGenerateMore:R})]})]})}),r.jsx(m.o,{})]}),r.jsx(u.$,{})]})})]})}function Z(e){return r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,...e,children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 019-9"})})}[l,o,d,c,m,h,u,x,y,v,j,w,N,k,C,D]=M.then?(await M)():M;let z=async()=>({props:{}});s()}catch(e){s(e)}})},3293:(e,a,t)=>{t.d(a,{y:()=>s});let s=100},9757:(e,a,t)=>{t.d(a,{Co:()=>i,Ps:()=>r});let s=[{name:"com",price:6.49,style:"bg-primary-100 text-primary-700 border-primary-200",category:"generic"},{name:"net",price:10.98,style:"bg-teal-100 text-teal-700 border-teal-200",category:"generic"},{name:"org",price:7.48,style:"bg-green-100 text-green-700 border-green-200",category:"generic"},{name:"io",price:34.98,style:"bg-violet-100 text-violet-700 border-violet-200",category:"generic"},{name:"app",price:12.98,style:"bg-fuchsia-100 text-fuchsia-700 border-fuchsia-200",category:"new"},{name:"dev",price:10.98,style:"bg-emerald-100 text-emerald-700 border-emerald-200",category:"new"},{name:"me",price:9.98,style:"bg-orange-100 text-orange-700 border-orange-200",category:"new"},{name:"shop",price:1.28,style:"bg-amber-100 text-amber-700 border-amber-200",category:"new"},{name:"online",price:.98,style:"bg-indigo-100 text-indigo-700 border-indigo-200",category:"new"}];function r(){return s.map(e=>e.name)}function i(e){let a=e.startsWith(".")?e.substring(1):e,t=s.find(e=>e.name===a.toLowerCase());return t?.style||"bg-gray-100 text-gray-700 border-gray-200"}},6988:e=>{e.exports=require("@auth0/auth0-react")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},5458:e=>{e.exports=import("@radix-ui/react-avatar")},7715:e=>{e.exports=import("@radix-ui/react-dialog")},1481:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},8680:e=>{e.exports=import("@radix-ui/react-popover")},6005:e=>{e.exports=import("@radix-ui/react-progress")},4338:e=>{e.exports=import("@radix-ui/react-slot")},6860:e=>{e.exports=import("@radix-ui/react-tabs")},1329:e=>{e.exports=import("@radix-ui/react-toast")},9752:e=>{e.exports=import("@tanstack/react-query")},6926:e=>{e.exports=import("class-variance-authority")},6593:e=>{e.exports=import("clsx")},907:e=>{e.exports=import("cmdk")},8097:e=>{e.exports=import("tailwind-merge")},3766:(e,a,t)=>{function s(e){let a=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===a?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===a||"string"==typeof e||"[object String]"===a?e:NaN)}function r(e,a){let t=s(e),r=s(a),i=t.getTime()-r.getTime();return i<0?-1:i>0?1:i}t.d(a,{Q:()=>x});let i={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function n(e){return (a={})=>{let t=a.width?String(a.width):e.defaultWidth;return e.formats[t]||e.formats[e.defaultWidth]}}let l={date:n({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:n({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:n({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},o={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function d(e){return(a,t)=>{let s;if("formatting"===(t?.context?String(t.context):"standalone")&&e.formattingValues){let a=e.defaultFormattingWidth||e.defaultWidth,r=t?.width?String(t.width):a;s=e.formattingValues[r]||e.formattingValues[a]}else{let a=e.defaultWidth,r=t?.width?String(t.width):e.defaultWidth;s=e.values[r]||e.values[a]}return s[e.argumentCallback?e.argumentCallback(a):a]}}function c(e){return(a,t={})=>{let s;let r=t.width,i=r&&e.matchPatterns[r]||e.matchPatterns[e.defaultMatchWidth],n=a.match(i);if(!n)return null;let l=n[0],o=r&&e.parsePatterns[r]||e.parsePatterns[e.defaultParseWidth],d=Array.isArray(o)?function(e,a){for(let t=0;t<e.length;t++)if(a(e[t]))return t}(o,e=>e.test(l)):function(e,a){for(let t in e)if(Object.prototype.hasOwnProperty.call(e,t)&&a(e[t]))return t}(o,e=>e.test(l));return s=e.valueCallback?e.valueCallback(d):d,{value:s=t.valueCallback?t.valueCallback(s):s,rest:a.slice(l.length)}}}let m={code:"en-US",formatDistance:(e,a,t)=>{let s;let r=i[e];return(s="string"==typeof r?r:1===a?r.one:r.other.replace("{{count}}",a.toString()),t?.addSuffix)?t.comparison&&t.comparison>0?"in "+s:s+" ago":s},formatLong:l,formatRelative:(e,a,t,s)=>o[e],localize:{ordinalNumber:(e,a)=>{let t=Number(e),s=t%100;if(s>20||s<10)switch(s%10){case 1:return t+"st";case 2:return t+"nd";case 3:return t+"rd"}return t+"th"},era:d({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:d({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:d({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:d({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:d({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:function(e){return(a,t={})=>{let s=a.match(e.matchPattern);if(!s)return null;let r=s[0],i=a.match(e.parsePattern);if(!i)return null;let n=e.valueCallback?e.valueCallback(i[0]):i[0];return{value:n=t.valueCallback?t.valueCallback(n):n,rest:a.slice(r.length)}}}({matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)}),era:c({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:c({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:c({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:c({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:c({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},h={};function u(e){let a=s(e),t=new Date(Date.UTC(a.getFullYear(),a.getMonth(),a.getDate(),a.getHours(),a.getMinutes(),a.getSeconds(),a.getMilliseconds()));return t.setUTCFullYear(a.getFullYear()),+e-+t}function x(e,a){var t;return function(e,a,t){var i,n,l,o;let d,c,x;let g=t?.locale??h.locale??m,p=r(e,a);if(isNaN(p))throw RangeError("Invalid time value");let f=Object.assign({},t,{addSuffix:t?.addSuffix,comparison:p});p>0?(d=s(a),c=s(e)):(d=s(e),c=s(a));let y=(i=c,n=d,(o=void 0,e=>{let a=(o?Math[o]:Math.trunc)(e);return 0===a?0:a})((+s(i)-+s(n))/1e3)),v=Math.round((y-(u(c)-u(d))/1e3)/60);if(v<2){if(t?.includeSeconds){if(y<5)return g.formatDistance("lessThanXSeconds",5,f);if(y<10)return g.formatDistance("lessThanXSeconds",10,f);if(y<20)return g.formatDistance("lessThanXSeconds",20,f);if(y<40)return g.formatDistance("halfAMinute",0,f);else if(y<60)return g.formatDistance("lessThanXMinutes",1,f);else return g.formatDistance("xMinutes",1,f)}return 0===v?g.formatDistance("lessThanXMinutes",1,f):g.formatDistance("xMinutes",v,f)}if(v<45)return g.formatDistance("xMinutes",v,f);if(v<90)return g.formatDistance("aboutXHours",1,f);if(v<1440)return g.formatDistance("aboutXHours",Math.round(v/60),f);if(v<2520)return g.formatDistance("xDays",1,f);if(v<43200)return g.formatDistance("xDays",Math.round(v/1440),f);if(v<86400)return x=Math.round(v/43200),g.formatDistance("aboutXMonths",x,f);if((x=function(e,a){let t;let i=s(e),n=s(a),l=r(i,n),o=Math.abs(function(e,a){let t=s(e),r=s(a);return 12*(t.getFullYear()-r.getFullYear())+(t.getMonth()-r.getMonth())}(i,n));if(o<1)t=0;else{1===i.getMonth()&&i.getDate()>27&&i.setDate(30),i.setMonth(i.getMonth()-l*o);let a=r(i,n)===-l;(function(e){let a=s(e);return+function(e){let a=s(e);return a.setHours(23,59,59,999),a}(a)==+function(e){let a=s(e),t=a.getMonth();return a.setFullYear(a.getFullYear(),t+1,0),a.setHours(23,59,59,999),a}(a)})(s(e))&&1===o&&1===r(e,n)&&(a=!1),t=l*(o-Number(a))}return 0===t?0:t}(c,d))<12)return g.formatDistance("xMonths",Math.round(v/43200),f);{let e=x%12,a=Math.trunc(x/12);return e<3?g.formatDistance("aboutXYears",a,f):e<9?g.formatDistance("overXYears",a,f):g.formatDistance("almostXYears",a+1,f)}}(e,(t=Date.now(),e instanceof Date?new e.constructor(t):new Date(t)),a)}}};var a=require("../webpack-runtime.js");a.C(e);var t=e=>a(a.s=e),s=a.X(0,[859,959,645,556,989],()=>t(1496));module.exports=s})();