"use strict";(()=>{var e={};e.id=277,e.ids=[277],e.modules={2951:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{config:()=>h,default:()=>p,getServerSideProps:()=>m,getStaticPaths:()=>u,getStaticProps:()=>x,reportWebVitals:()=>g,routeModule:()=>S,unstable_getServerProps:()=>b,unstable_getServerSideProps:()=>P,unstable_getStaticParams:()=>v,unstable_getStaticPaths:()=>f,unstable_getStaticProps:()=>j});var s=r(7093),i=r(5244),o=r(1323),n=r(3756),l=r(9645),c=r(1109),d=e([l,c]);[l,c]=d.then?(await d)():d;let p=(0,o.l)(c,"default"),x=(0,o.l)(c,"getStaticProps"),u=(0,o.l)(c,"getStaticPaths"),m=(0,o.l)(c,"getServerSideProps"),h=(0,o.l)(c,"config"),g=(0,o.l)(c,"reportWebVitals"),j=(0,o.l)(c,"unstable_getStaticProps"),f=(0,o.l)(c,"unstable_getStaticPaths"),v=(0,o.l)(c,"unstable_getStaticParams"),b=(0,o.l)(c,"unstable_getServerProps"),P=(0,o.l)(c,"unstable_getServerSideProps"),S=new s.PagesRouteModule({definition:{kind:i.x.PAGES,page:"/profile",pathname:"/profile",bundlePath:"",filename:""},components:{App:l.default,Document:n.default},userland:c});a()}catch(e){a(e)}})},4167:(e,t,r)=>{r.d(t,{i:()=>o});var a=r(997),s=r(873),i=r(1163);function o({children:e}){let{isAuthenticated:t,isLoading:r}=(0,s.a)();return((0,i.useRouter)(),r)?a.jsx("div",{className:"flex items-center justify-center h-screen",children:a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"})}):t?a.jsx(a.Fragment,{children:e}):null}},1109:(e,t,r)=>{r.a(e,async(e,a)=>{try{r.r(t),r.d(t,{default:()=>u,getStaticProps:()=>m});var s=r(997),i=r(968),o=r.n(i),n=r(2498),l=r(7304),c=r(4167),d=r(3489),p=r(5794),x=e([n,l,d,p]);function u({}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(o(),{children:[s.jsx("title",{children:"My Profile | DomainMate"}),s.jsx("meta",{name:"description",content:"Manage your DomainMate profile, view your favorite domains, and track your domain search history."}),s.jsx("meta",{name:"robots",content:"noindex, nofollow"}),s.jsx("link",{rel:"canonical",href:"https://domainmate.net/profile"})]}),s.jsx("div",{className:"font-sans text-gray-700 min-h-screen",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("header",{className:"mb-10",children:s.jsx(n.w,{})}),s.jsx("main",{children:(0,s.jsxs)(c.i,{children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 mb-4",children:"My Profile"}),s.jsx("p",{className:"text-lg text-gray-600",children:"Manage your account and view your favorite domains."})]}),(0,s.jsxs)("div",{className:"grid gap-8 lg:grid-cols-3",children:[s.jsx("div",{className:"lg:col-span-1",children:s.jsx(d.I,{})}),s.jsx("div",{className:"lg:col-span-2",children:s.jsx(p.V,{})})]})]})}),s.jsx(l.$,{})]})})]})}[n,l,d,p]=x.then?(await x)():x;let m=async()=>({props:{}});a()}catch(e){a(e)}})},6988:e=>{e.exports=require("@auth0/auth0-react")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},5458:e=>{e.exports=import("@radix-ui/react-avatar")},1481:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},4338:e=>{e.exports=import("@radix-ui/react-slot")},1329:e=>{e.exports=import("@radix-ui/react-toast")},9752:e=>{e.exports=import("@tanstack/react-query")},6926:e=>{e.exports=import("class-variance-authority")},6593:e=>{e.exports=import("clsx")},8097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[859,959,645,556,989],()=>r(2951));module.exports=a})();