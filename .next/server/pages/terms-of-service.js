"use strict";(()=>{var e={};e.id=507,e.ids=[507],e.modules={7433:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{config:()=>x,default:()=>u,getServerSideProps:()=>h,getStaticPaths:()=>p,getStaticProps:()=>m,reportWebVitals:()=>g,routeModule:()=>S,unstable_getServerProps:()=>y,unstable_getServerSideProps:()=>b,unstable_getStaticParams:()=>j,unstable_getStaticPaths:()=>v,unstable_getStaticProps:()=>f});var s=r(7093),a=r(5244),n=r(1323),o=r(3756),c=r(9645),l=r(4034),d=e([c,l]);[c,l]=d.then?(await d)():d;let u=(0,n.l)(l,"default"),m=(0,n.l)(l,"getStaticProps"),p=(0,n.l)(l,"getStaticPaths"),h=(0,n.l)(l,"getServerSideProps"),x=(0,n.l)(l,"config"),g=(0,n.l)(l,"reportWebVitals"),f=(0,n.l)(l,"unstable_getStaticProps"),v=(0,n.l)(l,"unstable_getStaticPaths"),j=(0,n.l)(l,"unstable_getStaticParams"),y=(0,n.l)(l,"unstable_getServerProps"),b=(0,n.l)(l,"unstable_getServerSideProps"),S=new s.PagesRouteModule({definition:{kind:a.x.PAGES,page:"/terms-of-service",pathname:"/terms-of-service",bundlePath:"",filename:""},components:{App:c.default,Document:o.default},userland:l});i()}catch(e){i(e)}})},4034:(e,t,r)=>{r.a(e,async(e,i)=>{try{r.r(t),r.d(t,{default:()=>u,getStaticProps:()=>m});var s=r(997),a=r(968),n=r.n(a),o=r(2498),c=r(7304),l=r(3749),d=e([o,c,l]);function u({lastUpdated:e}){return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(n(),{children:[s.jsx("title",{children:"Terms of Service | DomainMate"}),s.jsx("meta",{name:"description",content:"Read DomainMate's terms of service to understand the rules and guidelines for using our domain name generator service."}),s.jsx("meta",{name:"robots",content:"index, follow"}),s.jsx("meta",{property:"og:title",content:"Terms of Service | DomainMate"}),s.jsx("meta",{property:"og:description",content:"Read DomainMate's terms of service to understand the rules and guidelines for using our domain name generator service."}),s.jsx("meta",{property:"og:url",content:"https://domainmate.net/terms-of-service"}),s.jsx("meta",{property:"og:type",content:"website"}),s.jsx("link",{rel:"canonical",href:"https://domainmate.net/terms-of-service"}),s.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:"Terms of Service",description:"DomainMate's terms of service explaining the rules and guidelines for using our service",url:"https://domainmate.net/terms-of-service",publisher:{"@type":"Organization",name:"DomainMate",url:"https://domainmate.net"},dateModified:e})}})]}),s.jsx("div",{className:"font-sans text-gray-700 min-h-screen",children:(0,s.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[s.jsx("header",{className:"mb-10",children:s.jsx(o.w,{})}),(0,s.jsxs)("main",{children:[(0,s.jsxs)("div",{className:"mb-8",children:[s.jsx("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 mb-4",children:"Terms of Service"}),(0,s.jsxs)("p",{className:"text-lg text-gray-600",children:["Last updated: ",new Date(e).toLocaleDateString()]})]}),s.jsx(l.Zb,{children:s.jsx(l.aY,{className:"p-8",children:(0,s.jsxs)("div",{className:"prose prose-lg max-w-none",children:[s.jsx("h2",{children:"Acceptance of Terms"}),s.jsx("p",{children:"By accessing and using DomainMate, you accept and agree to be bound by the terms and provision of this agreement."}),s.jsx("h2",{children:"Description of Service"}),s.jsx("p",{children:"DomainMate is an AI-powered domain name generator that helps users find available domain names for their businesses and projects. Our service includes:"}),(0,s.jsxs)("ul",{children:[s.jsx("li",{children:"AI-generated domain name suggestions"}),s.jsx("li",{children:"Domain availability checking"}),s.jsx("li",{children:"Bulk domain checking"}),s.jsx("li",{children:"Favorites management"}),s.jsx("li",{children:"User accounts and profiles"})]}),s.jsx("h2",{children:"User Accounts"}),s.jsx("p",{children:"To access certain features of our service, you may be required to create an account. You are responsible for maintaining the confidentiality of your account information."}),s.jsx("h2",{children:"Acceptable Use"}),s.jsx("p",{children:"You agree to use our service only for lawful purposes and in accordance with these Terms. You agree not to:"}),(0,s.jsxs)("ul",{children:[s.jsx("li",{children:"Use the service for any illegal or unauthorized purpose"}),s.jsx("li",{children:"Attempt to gain unauthorized access to our systems"}),s.jsx("li",{children:"Interfere with or disrupt the service"}),s.jsx("li",{children:"Use automated tools to access the service excessively"})]}),s.jsx("h2",{children:"Intellectual Property"}),s.jsx("p",{children:"The service and its original content, features, and functionality are and will remain the exclusive property of DomainMate and its licensors."}),s.jsx("h2",{children:"Domain Registration"}),s.jsx("p",{children:"DomainMate does not register domains on behalf of users. We provide suggestions and availability information, but domain registration must be completed through authorized domain registrars."}),s.jsx("h2",{children:"Disclaimer of Warranties"}),s.jsx("p",{children:"The information, software, products, and services included in or available through the service may include inaccuracies or typographical errors. We make no warranty that the service will be uninterrupted or error-free."}),s.jsx("h2",{children:"Limitation of Liability"}),s.jsx("p",{children:"In no event shall DomainMate be liable for any indirect, incidental, special, consequential, or punitive damages arising out of your use of the service."}),s.jsx("h2",{children:"Rate Limiting"}),s.jsx("p",{children:"To ensure fair usage and maintain service quality, we implement rate limiting on our API endpoints. Excessive usage may result in temporary restrictions."}),s.jsx("h2",{children:"Privacy"}),s.jsx("p",{children:"Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the service."}),s.jsx("h2",{children:"Termination"}),s.jsx("p",{children:"We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever."}),s.jsx("h2",{children:"Changes to Terms"}),s.jsx("p",{children:"We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect."}),s.jsx("h2",{children:"Contact Information"}),s.jsx("p",{children:"If you have any questions about these Terms of Service, please contact us through our website."})]})})})]}),s.jsx(c.$,{})]})})]})}[o,c,l]=d.then?(await d)():d;let m=async()=>({props:{lastUpdated:new Date().toISOString()}});i()}catch(e){i(e)}})},6988:e=>{e.exports=require("@auth0/auth0-react")},2785:e=>{e.exports=require("next/dist/compiled/next-server/pages.runtime.prod.js")},968:e=>{e.exports=require("next/head")},6689:e=>{e.exports=require("react")},6405:e=>{e.exports=require("react-dom")},997:e=>{e.exports=require("react/jsx-runtime")},2048:e=>{e.exports=require("fs")},5315:e=>{e.exports=require("path")},6162:e=>{e.exports=require("stream")},1568:e=>{e.exports=require("zlib")},5458:e=>{e.exports=import("@radix-ui/react-avatar")},1481:e=>{e.exports=import("@radix-ui/react-dropdown-menu")},4338:e=>{e.exports=import("@radix-ui/react-slot")},1329:e=>{e.exports=import("@radix-ui/react-toast")},9752:e=>{e.exports=import("@tanstack/react-query")},6926:e=>{e.exports=import("class-variance-authority")},6593:e=>{e.exports=import("clsx")},8097:e=>{e.exports=import("tailwind-merge")}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),i=t.X(0,[859,959,645,556],()=>r(7433));module.exports=i})();