"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[809],{893:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Briefcase",[["path",{d:"M16 20V4a2 2 0 0 0-2-2h-4a2 2 0 0 0-2 2v16",key:"jecpp"}],["rect",{width:"20",height:"14",x:"2",y:"6",rx:"2",key:"i6l2r4"}]])},341:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Camera",[["path",{d:"M14.5 4h-5L7 7H4a2 2 0 0 0-2 2v9a2 2 0 0 0 2 2h16a2 2 0 0 0 2-2V9a2 2 0 0 0-2-2h-3l-2.5-3z",key:"1tc9qg"}],["circle",{cx:"12",cy:"13",r:"3",key:"1vg3eu"}]])},2171:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ChevronDown",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]])},5432:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ChevronUp",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]])},3835:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ChevronsUpDown",[["path",{d:"m7 15 5 5 5-5",key:"1hf1tw"}],["path",{d:"m7 9 5-5 5 5",key:"sgt6xg"}]])},3050:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("CircleCheckBig",[["path",{d:"M21.801 10A10 10 0 1 1 17 3.335",key:"yps3ct"}],["path",{d:"m9 11 3 3L22 4",key:"1pflzl"}]])},7695:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("CircleX",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]])},2629:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Dumbbell",[["path",{d:"M14.4 14.4 9.6 9.6",key:"ic80wn"}],["path",{d:"M18.657 21.485a2 2 0 1 1-2.829-2.828l-1.767 1.768a2 2 0 1 1-2.829-2.829l6.364-6.364a2 2 0 1 1 2.829 2.829l-1.768 1.767a2 2 0 1 1 2.828 2.829z",key:"nnl7wr"}],["path",{d:"m21.5 21.5-1.4-1.4",key:"1f1ice"}],["path",{d:"M3.9 3.9 2.5 2.5",key:"1evmna"}],["path",{d:"M6.404 12.768a2 2 0 1 1-2.829-2.829l1.768-1.767a2 2 0 1 1-2.828-2.829l2.828-2.828a2 2 0 1 1 2.829 2.828l1.767-1.768a2 2 0 1 1 2.829 2.829z",key:"yhosts"}]])},2020:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]])},1601:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]])},6215:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("House",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},8453:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Laptop",[["path",{d:"M20 16V7a2 2 0 0 0-2-2H6a2 2 0 0 0-2 2v9m16 0H4m16 0 1.28 2.55a1 1 0 0 1-.9 1.45H3.62a1 1 0 0 1-.9-1.45L4 16",key:"tarvll"}]])},8178:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("LayoutGrid",[["rect",{width:"7",height:"7",x:"3",y:"3",rx:"1",key:"1g98yp"}],["rect",{width:"7",height:"7",x:"14",y:"3",rx:"1",key:"6d4xhi"}],["rect",{width:"7",height:"7",x:"14",y:"14",rx:"1",key:"nxv5o0"}],["rect",{width:"7",height:"7",x:"3",y:"14",rx:"1",key:"1bb6yr"}]])},8030:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Lightbulb",[["path",{d:"M15 14c.2-1 .7-1.7 1.5-2.5 1-.9 1.5-2.2 1.5-3.5A6 6 0 0 0 6 8c0 1 .2 2.2 1.5 3.5.7.7 1.3 1.5 1.5 2.5",key:"1gvzjb"}],["path",{d:"M9 18h6",key:"x1upvd"}],["path",{d:"M10 22h4",key:"ceow96"}]])},4184:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ListChecks",[["path",{d:"m3 17 2 2 4-4",key:"1jhpwq"}],["path",{d:"m3 7 2 2 4-4",key:"1obspn"}],["path",{d:"M13 6h8",key:"15sg57"}],["path",{d:"M13 12h8",key:"h98zly"}],["path",{d:"M13 18h8",key:"oe0vm4"}]])},3243:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("List",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]])},1475:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("PenTool",[["path",{d:"M15.707 21.293a1 1 0 0 1-1.414 0l-1.586-1.586a1 1 0 0 1 0-1.414l5.586-5.586a1 1 0 0 1 1.414 0l1.586 1.586a1 1 0 0 1 0 1.414z",key:"nt11vn"}],["path",{d:"m18 13-1.375-6.874a1 1 0 0 0-.746-.776L3.235 2.028a1 1 0 0 0-1.207 1.207L5.35 15.879a1 1 0 0 0 .776.746L13 18",key:"15qc1e"}],["path",{d:"m2.3 2.3 7.286 7.286",key:"1wuzzi"}],["circle",{cx:"11",cy:"11",r:"2",key:"xmgehs"}]])},4976:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("RefreshCw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]])},2125:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("RotateCw",[["path",{d:"M21 12a9 9 0 1 1-9-9c2.52 0 4.93 1 6.74 2.74L21 8",key:"1p45f6"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}]])},734:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Server",[["rect",{width:"20",height:"8",x:"2",y:"2",rx:"2",ry:"2",key:"ngkwjq"}],["rect",{width:"20",height:"8",x:"2",y:"14",rx:"2",ry:"2",key:"iecqi9"}],["line",{x1:"6",x2:"6.01",y1:"6",y2:"6",key:"16zg32"}],["line",{x1:"6",x2:"6.01",y1:"18",y2:"18",key:"nzw8ys"}]])},6194:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ShoppingBag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]])},7865:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ShoppingCart",[["circle",{cx:"8",cy:"21",r:"1",key:"jimo8o"}],["circle",{cx:"19",cy:"21",r:"1",key:"13723u"}],["path",{d:"M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57l1.65-7.43H5.12",key:"9zh506"}]])},3058:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Sparkles",[["path",{d:"M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.581a.5.5 0 0 1 0 .964L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z",key:"4pj2yx"}],["path",{d:"M20 3v4",key:"1olli1"}],["path",{d:"M22 5h-4",key:"1gvqau"}],["path",{d:"M4 17v2",key:"vumght"}],["path",{d:"M5 18H3",key:"zchphs"}]])},1793:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Tablet",[["rect",{width:"16",height:"20",x:"4",y:"2",rx:"2",ry:"2",key:"76otgf"}],["line",{x1:"12",x2:"12.01",y1:"18",y2:"18",key:"1dp563"}]])},1603:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Tag",[["path",{d:"M12.586 2.586A2 2 0 0 0 11.172 2H4a2 2 0 0 0-2 2v7.172a2 2 0 0 0 .586 1.414l8.704 8.704a2.426 2.426 0 0 0 3.42 0l6.58-6.58a2.426 2.426 0 0 0 0-3.42z",key:"vktsd0"}],["circle",{cx:"7.5",cy:"7.5",r:".5",fill:"currentColor",key:"kqv944"}]])},8806:function(e,t,n){n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Utensils",[["path",{d:"M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2",key:"cjf0a3"}],["path",{d:"M7 2v20",key:"1473qp"}],["path",{d:"M21 15V2a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7",key:"j28e5"}]])},3250:function(e,t,n){var r=n(7294),a="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},o=r.useState,i=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!a(e,n)}catch(e){return!0}}var d="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=o({inst:{value:n,getSnapshot:t}}),a=r[0].inst,d=r[1];return l(function(){a.value=n,a.getSnapshot=t,c(a)&&d({inst:a})},[e,n,t]),i(function(){return c(a)&&d({inst:a}),e(function(){c(a)&&d({inst:a})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:d},1688:function(e,t,n){e.exports=n(3250)},2854:function(e,t,n){n.d(t,{Dx:function(){return en},VY:function(){return et},aV:function(){return ee},dk:function(){return er},fC:function(){return J},h_:function(){return G},x8:function(){return ea},xz:function(){return Q}});var r=n(7294),a=n(6206),o=n(8771),i=n(5360),l=n(1276),u=n(7342),c=n(6063),d=n(5420),s=n(2651),f=n(9115),h=n(5320),p=n(7552),m=n(6223),v=n(3541),g=n(8426),y=n(5893),b="Dialog",[w,x]=(0,i.b)(b),[M,k]=w(b),C=e=>{let{__scopeDialog:t,children:n,open:a,defaultOpen:o,onOpenChange:i,modal:c=!0}=e,d=r.useRef(null),s=r.useRef(null),[f=!1,h]=(0,u.T)({prop:a,defaultProp:o,onChange:i});return(0,y.jsx)(M,{scope:t,triggerRef:d,contentRef:s,contentId:(0,l.M)(),titleId:(0,l.M)(),descriptionId:(0,l.M)(),open:f,onOpenChange:h,onOpenToggle:r.useCallback(()=>h(e=>!e),[h]),modal:c,children:n})};C.displayName=b;var D="DialogTrigger",j=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=k(D,n),l=(0,o.e)(t,i.triggerRef);return(0,y.jsx)(h.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":X(i.open),...r,ref:l,onClick:(0,a.M)(e.onClick,i.onOpenToggle)})});j.displayName=D;var E="DialogPortal",[S,P]=w(E,{forceMount:void 0}),R=e=>{let{__scopeDialog:t,forceMount:n,children:a,container:o}=e,i=k(E,t);return(0,y.jsx)(S,{scope:t,forceMount:n,children:r.Children.map(a,e=>(0,y.jsx)(f.z,{present:n||i.open,children:(0,y.jsx)(s.h,{asChild:!0,container:o,children:e})}))})};R.displayName=E;var A="DialogOverlay",W=r.forwardRef((e,t)=>{let n=P(A,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=k(A,e.__scopeDialog);return o.modal?(0,y.jsx)(f.z,{present:r||o.open,children:(0,y.jsx)(Z,{...a,ref:t})}):null});W.displayName=A;var Z=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=k(A,n);return(0,y.jsx)(m.Z,{as:g.g7,allowPinchZoom:!0,shards:[a.contentRef],children:(0,y.jsx)(h.WV.div,{"data-state":X(a.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),N="DialogContent",I=r.forwardRef((e,t)=>{let n=P(N,e.__scopeDialog),{forceMount:r=n.forceMount,...a}=e,o=k(N,e.__scopeDialog);return(0,y.jsx)(f.z,{present:r||o.open,children:o.modal?(0,y.jsx)(F,{...a,ref:t}):(0,y.jsx)(V,{...a,ref:t})})});I.displayName=N;var F=r.forwardRef((e,t)=>{let n=k(N,e.__scopeDialog),i=r.useRef(null),l=(0,o.e)(t,n.contentRef,i);return r.useEffect(()=>{let e=i.current;if(e)return(0,v.Ry)(e)},[]),(0,y.jsx)(O,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),n.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault())})}),V=r.forwardRef((e,t)=>{let n=k(N,e.__scopeDialog),a=r.useRef(!1),o=r.useRef(!1);return(0,y.jsx)(O,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||n.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),O=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:a,onOpenAutoFocus:i,onCloseAutoFocus:l,...u}=e,s=k(N,n),f=r.useRef(null),h=(0,o.e)(t,f);return(0,p.EW)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(d.M,{asChild:!0,loop:!0,trapped:a,onMountAutoFocus:i,onUnmountAutoFocus:l,children:(0,y.jsx)(c.XB,{role:"dialog",id:s.contentId,"aria-describedby":s.descriptionId,"aria-labelledby":s.titleId,"data-state":X(s.open),...u,ref:h,onDismiss:()=>s.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(B,{titleId:s.titleId}),(0,y.jsx)(U,{contentRef:f,descriptionId:s.descriptionId})]})]})}),z="DialogTitle",T=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=k(z,n);return(0,y.jsx)(h.WV.h2,{id:a.titleId,...r,ref:t})});T.displayName=z;var _="DialogDescription",$=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=k(_,n);return(0,y.jsx)(h.WV.p,{id:a.descriptionId,...r,ref:t})});$.displayName=_;var q="DialogClose",L=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=k(q,n);return(0,y.jsx)(h.WV.button,{type:"button",...r,ref:t,onClick:(0,a.M)(e.onClick,()=>o.onOpenChange(!1))})});function X(e){return e?"open":"closed"}L.displayName=q;var H="DialogTitleWarning",[Y,K]=(0,i.k)(H,{contentName:N,titleName:z,docsSlug:"dialog"}),B=({titleId:e})=>{let t=K(H),n=`\`${t.contentName}\` requires a \`${t.titleName}\` for the component to be accessible for screen reader users.

If you want to hide the \`${t.titleName}\`, you can wrap it with our VisuallyHidden component.

For more information, see https://radix-ui.com/primitives/docs/components/${t.docsSlug}`;return r.useEffect(()=>{e&&!document.getElementById(e)&&console.error(n)},[n,e]),null},U=({contentRef:e,descriptionId:t})=>{let n=K("DialogDescriptionWarning"),a=`Warning: Missing \`Description\` or \`aria-describedby={undefined}\` for {${n.contentName}}.`;return r.useEffect(()=>{let n=e.current?.getAttribute("aria-describedby");t&&n&&!document.getElementById(t)&&console.warn(a)},[a,e,t]),null},J=C,Q=j,G=R,ee=W,et=I,en=T,er=$,ea=L},6419:function(e,t,n){n.d(t,{VY:function(){return L},fC:function(){return _},h_:function(){return q},xz:function(){return $}});var r=n(7294),a=n(6206),o=n(8771),i=n(5360),l=n(6063),u=n(7552),c=n(5420),d=n(1276),s=n(2593),f=n(2651),h=n(9115),p=n(5320),m=n(8426),v=n(7342),g=n(3541),y=n(6223),b=n(5893),w="Popover",[x,M]=(0,i.b)(w,[s.D7]),k=(0,s.D7)(),[C,D]=x(w),j=e=>{let{__scopePopover:t,children:n,open:a,defaultOpen:o,onOpenChange:i,modal:l=!1}=e,u=k(t),c=r.useRef(null),[f,h]=r.useState(!1),[p=!1,m]=(0,v.T)({prop:a,defaultProp:o,onChange:i});return(0,b.jsx)(s.fC,{...u,children:(0,b.jsx)(C,{scope:t,contentId:(0,d.M)(),triggerRef:c,open:p,onOpenChange:m,onOpenToggle:r.useCallback(()=>m(e=>!e),[m]),hasCustomAnchor:f,onCustomAnchorAdd:r.useCallback(()=>h(!0),[]),onCustomAnchorRemove:r.useCallback(()=>h(!1),[]),modal:l,children:n})})};j.displayName=w;var E="PopoverAnchor";r.forwardRef((e,t)=>{let{__scopePopover:n,...a}=e,o=D(E,n),i=k(n),{onCustomAnchorAdd:l,onCustomAnchorRemove:u}=o;return r.useEffect(()=>(l(),()=>u()),[l,u]),(0,b.jsx)(s.ee,{...i,...a,ref:t})}).displayName=E;var S="PopoverTrigger",P=r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,i=D(S,n),l=k(n),u=(0,o.e)(t,i.triggerRef),c=(0,b.jsx)(p.WV.button,{type:"button","aria-haspopup":"dialog","aria-expanded":i.open,"aria-controls":i.contentId,"data-state":T(i.open),...r,ref:u,onClick:(0,a.M)(e.onClick,i.onOpenToggle)});return i.hasCustomAnchor?c:(0,b.jsx)(s.ee,{asChild:!0,...l,children:c})});P.displayName=S;var R="PopoverPortal",[A,W]=x(R,{forceMount:void 0}),Z=e=>{let{__scopePopover:t,forceMount:n,children:r,container:a}=e,o=D(R,t);return(0,b.jsx)(A,{scope:t,forceMount:n,children:(0,b.jsx)(h.z,{present:n||o.open,children:(0,b.jsx)(f.h,{asChild:!0,container:a,children:r})})})};Z.displayName=R;var N="PopoverContent",I=r.forwardRef((e,t)=>{let n=W(N,e.__scopePopover),{forceMount:r=n.forceMount,...a}=e,o=D(N,e.__scopePopover);return(0,b.jsx)(h.z,{present:r||o.open,children:o.modal?(0,b.jsx)(F,{...a,ref:t}):(0,b.jsx)(V,{...a,ref:t})})});I.displayName=N;var F=r.forwardRef((e,t)=>{let n=D(N,e.__scopePopover),i=r.useRef(null),l=(0,o.e)(t,i),u=r.useRef(!1);return r.useEffect(()=>{let e=i.current;if(e)return(0,g.Ry)(e)},[]),(0,b.jsx)(y.Z,{as:m.g7,allowPinchZoom:!0,children:(0,b.jsx)(O,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,a.M)(e.onCloseAutoFocus,e=>{e.preventDefault(),u.current||n.triggerRef.current?.focus()}),onPointerDownOutside:(0,a.M)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;u.current=r},{checkForDefaultPrevented:!1}),onFocusOutside:(0,a.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1})})})}),V=r.forwardRef((e,t)=>{let n=D(N,e.__scopePopover),a=r.useRef(!1),o=r.useRef(!1);return(0,b.jsx)(O,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{e.onCloseAutoFocus?.(t),t.defaultPrevented||(a.current||n.triggerRef.current?.focus(),t.preventDefault()),a.current=!1,o.current=!1},onInteractOutside:t=>{e.onInteractOutside?.(t),t.defaultPrevented||(a.current=!0,"pointerdown"!==t.detail.originalEvent.type||(o.current=!0));let r=t.target;n.triggerRef.current?.contains(r)&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&o.current&&t.preventDefault()}})}),O=r.forwardRef((e,t)=>{let{__scopePopover:n,trapFocus:r,onOpenAutoFocus:a,onCloseAutoFocus:o,disableOutsidePointerEvents:i,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onInteractOutside:p,...m}=e,v=D(N,n),g=k(n);return(0,u.EW)(),(0,b.jsx)(c.M,{asChild:!0,loop:!0,trapped:r,onMountAutoFocus:a,onUnmountAutoFocus:o,children:(0,b.jsx)(l.XB,{asChild:!0,disableOutsidePointerEvents:i,onInteractOutside:p,onEscapeKeyDown:d,onPointerDownOutside:f,onFocusOutside:h,onDismiss:()=>v.onOpenChange(!1),children:(0,b.jsx)(s.VY,{"data-state":T(v.open),role:"dialog",id:v.contentId,...g,...m,ref:t,style:{...m.style,"--radix-popover-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-popover-content-available-width":"var(--radix-popper-available-width)","--radix-popover-content-available-height":"var(--radix-popper-available-height)","--radix-popover-trigger-width":"var(--radix-popper-anchor-width)","--radix-popover-trigger-height":"var(--radix-popper-anchor-height)"}})})})}),z="PopoverClose";function T(e){return e?"open":"closed"}r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,o=D(z,n);return(0,b.jsx)(p.WV.button,{type:"button",...r,ref:t,onClick:(0,a.M)(e.onClick,()=>o.onOpenChange(!1))})}).displayName=z,r.forwardRef((e,t)=>{let{__scopePopover:n,...r}=e,a=k(n);return(0,b.jsx)(s.Eh,{...a,...r,ref:t})}).displayName="PopoverArrow";var _=j,$=P,q=Z,L=I},2309:function(e,t,n){n.d(t,{z$:function(){return w},fC:function(){return b}});var r=n(7294),a=n(5893),o=n(5320),i="Progress",[l,u]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let a=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:a}}),[n,a])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),l=n.length;function u(t){let{scope:n,children:o,...u}=t,c=n?.[e][l]||i,d=r.useMemo(()=>u,Object.values(u));return(0,a.jsx)(c.Provider,{value:d,children:o})}return n=[...n,o],u.displayName=t+"Provider",[u,function(n,a){let u=a?.[e][l]||i,c=r.useContext(u);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let a=n.reduce((t,{useScope:n,scopeName:r})=>{let a=n(e)[`__scope${r}`];return{...t,...a}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:a}),[a])}};return n.scopeName=t.scopeName,n}(o,...t)]}(i),[c,d]=l(i),s=r.forwardRef((e,t)=>{var n,r;let{__scopeProgress:i,value:l=null,max:u,getValueLabel:d=p,...s}=e;(u||0===u)&&!g(u)&&console.error((n=`${u}`,`Invalid prop \`max\` of value \`${n}\` supplied to \`Progress\`. Only numbers greater than 0 are valid max values. Defaulting to \`100\`.`));let f=g(u)?u:100;null===l||y(l,f)||console.error((r=`${l}`,`Invalid prop \`value\` of value \`${r}\` supplied to \`Progress\`. The \`value\` prop must be:
  - a positive number
  - less than the value passed to \`max\` (or 100 if no \`max\` prop is set)
  - \`null\` or \`undefined\` if the progress is indeterminate.

Defaulting to \`null\`.`));let h=y(l,f)?l:null,b=v(h)?d(h,f):void 0;return(0,a.jsx)(c,{scope:i,value:h,max:f,children:(0,a.jsx)(o.WV.div,{"aria-valuemax":f,"aria-valuemin":0,"aria-valuenow":v(h)?h:void 0,"aria-valuetext":b,role:"progressbar","data-state":m(h,f),"data-value":h??void 0,"data-max":f,...s,ref:t})})});s.displayName=i;var f="ProgressIndicator",h=r.forwardRef((e,t)=>{let{__scopeProgress:n,...r}=e,i=d(f,n);return(0,a.jsx)(o.WV.div,{"data-state":m(i.value,i.max),"data-value":i.value??void 0,"data-max":i.max,...r,ref:t})});function p(e,t){return`${Math.round(e/t*100)}%`}function m(e,t){return null==e?"indeterminate":e===t?"complete":"loading"}function v(e){return"number"==typeof e}function g(e){return v(e)&&!isNaN(e)&&e>0}function y(e,t){return v(e)&&!isNaN(e)&&e<=t&&e>=0}h.displayName=f;var b=s,w=h},434:function(e,t,n){n.d(t,{VY:function(){return A},aV:function(){return P},fC:function(){return S},xz:function(){return R}});var r=n(7294),a=n(6206),o=n(5360),i=n(650),l=n(9115),u=n(5320),c=n(8990),d=n(7342),s=n(1276),f=n(5893),h="Tabs",[p,m]=(0,o.b)(h,[i.Pc]),v=(0,i.Pc)(),[g,y]=p(h),b=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,onValueChange:a,defaultValue:o,orientation:i="horizontal",dir:l,activationMode:h="automatic",...p}=e,m=(0,c.gm)(l),[v,y]=(0,d.T)({prop:r,onChange:a,defaultProp:o});return(0,f.jsx)(g,{scope:n,baseId:(0,s.M)(),value:v,onValueChange:y,orientation:i,dir:m,activationMode:h,children:(0,f.jsx)(u.WV.div,{dir:m,"data-orientation":i,...p,ref:t})})});b.displayName=h;var w="TabsList",x=r.forwardRef((e,t)=>{let{__scopeTabs:n,loop:r=!0,...a}=e,o=y(w,n),l=v(n);return(0,f.jsx)(i.fC,{asChild:!0,...l,orientation:o.orientation,dir:o.dir,loop:r,children:(0,f.jsx)(u.WV.div,{role:"tablist","aria-orientation":o.orientation,...a,ref:t})})});x.displayName=w;var M="TabsTrigger",k=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:r,disabled:o=!1,...l}=e,c=y(M,n),d=v(n),s=j(c.baseId,r),h=E(c.baseId,r),p=r===c.value;return(0,f.jsx)(i.ck,{asChild:!0,...d,focusable:!o,active:p,children:(0,f.jsx)(u.WV.button,{type:"button",role:"tab","aria-selected":p,"aria-controls":h,"data-state":p?"active":"inactive","data-disabled":o?"":void 0,disabled:o,id:s,...l,ref:t,onMouseDown:(0,a.M)(e.onMouseDown,e=>{o||0!==e.button||!1!==e.ctrlKey?e.preventDefault():c.onValueChange(r)}),onKeyDown:(0,a.M)(e.onKeyDown,e=>{[" ","Enter"].includes(e.key)&&c.onValueChange(r)}),onFocus:(0,a.M)(e.onFocus,()=>{let e="manual"!==c.activationMode;p||o||!e||c.onValueChange(r)})})})});k.displayName=M;var C="TabsContent",D=r.forwardRef((e,t)=>{let{__scopeTabs:n,value:a,forceMount:o,children:i,...c}=e,d=y(C,n),s=j(d.baseId,a),h=E(d.baseId,a),p=a===d.value,m=r.useRef(p);return r.useEffect(()=>{let e=requestAnimationFrame(()=>m.current=!1);return()=>cancelAnimationFrame(e)},[]),(0,f.jsx)(l.z,{present:o||p,children:({present:n})=>(0,f.jsx)(u.WV.div,{"data-state":p?"active":"inactive","data-orientation":d.orientation,role:"tabpanel","aria-labelledby":s,hidden:!n,id:h,tabIndex:0,...c,ref:t,style:{...e.style,animationDuration:m.current?"0s":void 0},children:n&&i})})});function j(e,t){return`${e}-trigger-${t}`}function E(e,t){return`${e}-content-${t}`}D.displayName=C;var S=b,P=x,R=k,A=D},3879:function(e,t,n){n.d(t,{mY:function(){return W}});var r=/[\\\/_+.#"@\[\(\{&]/,a=/[\\\/_+.#"@\[\(\{&]/g,o=/[\s-]/,i=/[\s-]/g;function l(e){return e.toLowerCase().replace(i," ")}var u=n(2854),c=n(7294),d=n(5320),s=n(1276),f=n(1688),h='[cmdk-group=""]',p='[cmdk-group-items=""]',m='[cmdk-item=""]',v=`${m}:not([aria-disabled="true"])`,g="cmdk-item-select",y="data-value",b=(e,t,n)=>{var u;return u=e,function e(t,n,l,u,c,d,s){if(d===n.length)return c===t.length?1:.99;var f=`${c},${d}`;if(void 0!==s[f])return s[f];for(var h,p,m,v,g=u.charAt(d),y=l.indexOf(g,c),b=0;y>=0;)(h=e(t,n,l,u,y+1,d+1,s))>b&&(y===c?h*=1:r.test(t.charAt(y-1))?(h*=.8,(m=t.slice(c,y-1).match(a))&&c>0&&(h*=Math.pow(.999,m.length))):o.test(t.charAt(y-1))?(h*=.9,(v=t.slice(c,y-1).match(i))&&c>0&&(h*=Math.pow(.999,v.length))):(h*=.17,c>0&&(h*=Math.pow(.999,y-c))),t.charAt(y)!==n.charAt(d)&&(h*=.9999)),(h<.1&&l.charAt(y-1)===u.charAt(d+1)||u.charAt(d+1)===u.charAt(d)&&l.charAt(y-1)!==u.charAt(d))&&.1*(p=e(t,n,l,u,y+1,d+2,s))>h&&(h=.1*p),h>b&&(b=h),y=l.indexOf(g,y+1);return s[f]=b,b}(u=n&&n.length>0?`${u+" "+n.join(" ")}`:u,t,l(u),l(t),0,0,{})},w=c.createContext(void 0),x=()=>c.useContext(w),M=c.createContext(void 0),k=()=>c.useContext(M),C=c.createContext(void 0),D=c.forwardRef((e,t)=>{let n=I(()=>{var t,n;return{search:"",value:null!=(n=null!=(t=e.value)?t:e.defaultValue)?n:"",filtered:{count:0,items:new Map,groups:new Set}}}),r=I(()=>new Set),a=I(()=>new Map),o=I(()=>new Map),i=I(()=>new Set),l=Z(e),{label:u,children:f,value:x,onValueChange:k,filter:C,shouldFilter:D,loop:j,disablePointerSelection:E=!1,vimBindings:S=!0,...P}=e,R=(0,s.M)(),A=(0,s.M)(),W=(0,s.M)(),F=c.useRef(null),V=z();N(()=>{if(void 0!==x){let e=x.trim();n.current.value=e,O.emit()}},[x]),N(()=>{V(6,Y)},[]);let O=c.useMemo(()=>({subscribe:e=>(i.current.add(e),()=>i.current.delete(e)),snapshot:()=>n.current,setState:(e,t,r)=>{var a,o,i;if(!Object.is(n.current[e],t)){if(n.current[e]=t,"search"===e)H(),L(),V(1,X);else if("value"===e&&(r||V(5,Y),(null==(a=l.current)?void 0:a.value)!==void 0)){null==(i=(o=l.current).onValueChange)||i.call(o,null!=t?t:"");return}O.emit()}},emit:()=>{i.current.forEach(e=>e())}}),[]),$=c.useMemo(()=>({value:(e,t,r)=>{var a;t!==(null==(a=o.current.get(e))?void 0:a.value)&&(o.current.set(e,{value:t,keywords:r}),n.current.filtered.items.set(e,q(t,r)),V(2,()=>{L(),O.emit()}))},item:(e,t)=>(r.current.add(e),t&&(a.current.has(t)?a.current.get(t).add(e):a.current.set(t,new Set([e]))),V(3,()=>{H(),L(),n.current.value||X(),O.emit()}),()=>{o.current.delete(e),r.current.delete(e),n.current.filtered.items.delete(e);let t=K();V(4,()=>{H(),(null==t?void 0:t.getAttribute("id"))===e&&X(),O.emit()})}),group:e=>(a.current.has(e)||a.current.set(e,new Set),()=>{o.current.delete(e),a.current.delete(e)}),filter:()=>l.current.shouldFilter,label:u||e["aria-label"],getDisablePointerSelection:()=>l.current.disablePointerSelection,listId:R,inputId:W,labelId:A,listInnerRef:F}),[]);function q(e,t){var r,a;let o=null!=(a=null==(r=l.current)?void 0:r.filter)?a:b;return e?o(e,n.current.search,t):0}function L(){if(!n.current.search||!1===l.current.shouldFilter)return;let e=n.current.filtered.items,t=[];n.current.filtered.groups.forEach(n=>{let r=a.current.get(n),o=0;r.forEach(t=>{o=Math.max(e.get(t),o)}),t.push([n,o])});let r=F.current;B().sort((t,n)=>{var r,a;let o=t.getAttribute("id"),i=n.getAttribute("id");return(null!=(r=e.get(i))?r:0)-(null!=(a=e.get(o))?a:0)}).forEach(e=>{let t=e.closest(p);t?t.appendChild(e.parentElement===t?e:e.closest(`${p} > *`)):r.appendChild(e.parentElement===r?e:e.closest(`${p} > *`))}),t.sort((e,t)=>t[1]-e[1]).forEach(e=>{var t;let n=null==(t=F.current)?void 0:t.querySelector(`${h}[${y}="${encodeURIComponent(e[0])}"]`);null==n||n.parentElement.appendChild(n)})}function X(){let e=B().find(e=>"true"!==e.getAttribute("aria-disabled")),t=null==e?void 0:e.getAttribute(y);O.setState("value",t||void 0)}function H(){var e,t,i,u;if(!n.current.search||!1===l.current.shouldFilter){n.current.filtered.count=r.current.size;return}n.current.filtered.groups=new Set;let c=0;for(let a of r.current){let r=q(null!=(t=null==(e=o.current.get(a))?void 0:e.value)?t:"",null!=(u=null==(i=o.current.get(a))?void 0:i.keywords)?u:[]);n.current.filtered.items.set(a,r),r>0&&c++}for(let[e,t]of a.current)for(let r of t)if(n.current.filtered.items.get(r)>0){n.current.filtered.groups.add(e);break}n.current.filtered.count=c}function Y(){var e,t,n;let r=K();r&&((null==(e=r.parentElement)?void 0:e.firstChild)===r&&(null==(n=null==(t=r.closest(h))?void 0:t.querySelector('[cmdk-group-heading=""]'))||n.scrollIntoView({block:"nearest"})),r.scrollIntoView({block:"nearest"}))}function K(){var e;return null==(e=F.current)?void 0:e.querySelector(`${m}[aria-selected="true"]`)}function B(){var e;return Array.from((null==(e=F.current)?void 0:e.querySelectorAll(v))||[])}function U(e){let t=B()[e];t&&O.setState("value",t.getAttribute(y))}function J(e){var t;let n=K(),r=B(),a=r.findIndex(e=>e===n),o=r[a+e];null!=(t=l.current)&&t.loop&&(o=a+e<0?r[r.length-1]:a+e===r.length?r[0]:r[a+e]),o&&O.setState("value",o.getAttribute(y))}function Q(e){let t=K(),n=null==t?void 0:t.closest(h),r;for(;n&&!r;)r=null==(n=e>0?function(e,t){let n=e.nextElementSibling;for(;n;){if(n.matches(t))return n;n=n.nextElementSibling}}(n,h):function(e,t){let n=e.previousElementSibling;for(;n;){if(n.matches(t))return n;n=n.previousElementSibling}}(n,h))?void 0:n.querySelector(v);r?O.setState("value",r.getAttribute(y)):J(e)}let G=()=>U(B().length-1),ee=e=>{e.preventDefault(),e.metaKey?G():e.altKey?Q(1):J(1)},et=e=>{e.preventDefault(),e.metaKey?U(0):e.altKey?Q(-1):J(-1)};return c.createElement(d.WV.div,{ref:t,tabIndex:-1,...P,"cmdk-root":"",onKeyDown:e=>{var t;if(null==(t=P.onKeyDown)||t.call(P,e),!e.defaultPrevented)switch(e.key){case"n":case"j":S&&e.ctrlKey&&ee(e);break;case"ArrowDown":ee(e);break;case"p":case"k":S&&e.ctrlKey&&et(e);break;case"ArrowUp":et(e);break;case"Home":e.preventDefault(),U(0);break;case"End":e.preventDefault(),G();break;case"Enter":if(!e.nativeEvent.isComposing&&229!==e.keyCode){e.preventDefault();let t=K();if(t){let e=new Event(g);t.dispatchEvent(e)}}}}},c.createElement("label",{"cmdk-label":"",htmlFor:$.inputId,id:$.labelId,style:_},u),T(e,e=>c.createElement(M.Provider,{value:O},c.createElement(w.Provider,{value:$},e))))}),j=c.forwardRef((e,t)=>{var n,r;let a=(0,s.M)(),o=c.useRef(null),i=c.useContext(C),l=x(),u=Z(e),f=null!=(r=null==(n=u.current)?void 0:n.forceMount)?r:null==i?void 0:i.forceMount;N(()=>{if(!f)return l.item(a,null==i?void 0:i.id)},[f]);let h=O(a,o,[e.value,e.children,o],e.keywords),p=k(),m=V(e=>e.value&&e.value===h.current),v=V(e=>!!f||!1===l.filter()||!e.search||e.filtered.items.get(a)>0);function y(){var e,t;b(),null==(t=(e=u.current).onSelect)||t.call(e,h.current)}function b(){p.setState("value",h.current,!0)}if(c.useEffect(()=>{let t=o.current;if(!(!t||e.disabled))return t.addEventListener(g,y),()=>t.removeEventListener(g,y)},[v,e.onSelect,e.disabled]),!v)return null;let{disabled:w,value:M,onSelect:D,forceMount:j,keywords:E,...S}=e;return c.createElement(d.WV.div,{ref:F([o,t]),...S,id:a,"cmdk-item":"",role:"option","aria-disabled":!!w,"aria-selected":!!m,"data-disabled":!!w,"data-selected":!!m,onPointerMove:w||l.getDisablePointerSelection()?void 0:b,onClick:w?void 0:y},e.children)}),E=c.forwardRef((e,t)=>{let{heading:n,children:r,forceMount:a,...o}=e,i=(0,s.M)(),l=c.useRef(null),u=c.useRef(null),f=(0,s.M)(),h=x(),p=V(e=>!!a||!1===h.filter()||!e.search||e.filtered.groups.has(i));N(()=>h.group(i),[]),O(i,l,[e.value,e.heading,u]);let m=c.useMemo(()=>({id:i,forceMount:a}),[a]);return c.createElement(d.WV.div,{ref:F([l,t]),...o,"cmdk-group":"",role:"presentation",hidden:!p||void 0},n&&c.createElement("div",{ref:u,"cmdk-group-heading":"","aria-hidden":!0,id:f},n),T(e,e=>c.createElement("div",{"cmdk-group-items":"",role:"group","aria-labelledby":n?f:void 0},c.createElement(C.Provider,{value:m},e))))}),S=c.forwardRef((e,t)=>{let{alwaysRender:n,...r}=e,a=c.useRef(null),o=V(e=>!e.search);return n||o?c.createElement(d.WV.div,{ref:F([a,t]),...r,"cmdk-separator":"",role:"separator"}):null}),P=c.forwardRef((e,t)=>{let{onValueChange:n,...r}=e,a=null!=e.value,o=k(),i=V(e=>e.search),l=V(e=>e.value),u=x(),s=c.useMemo(()=>{var e;let t=null==(e=u.listInnerRef.current)?void 0:e.querySelector(`${m}[${y}="${encodeURIComponent(l)}"]`);return null==t?void 0:t.getAttribute("id")},[]);return c.useEffect(()=>{null!=e.value&&o.setState("search",e.value)},[e.value]),c.createElement(d.WV.input,{ref:t,...r,"cmdk-input":"",autoComplete:"off",autoCorrect:"off",spellCheck:!1,"aria-autocomplete":"list",role:"combobox","aria-expanded":!0,"aria-controls":u.listId,"aria-labelledby":u.labelId,"aria-activedescendant":s,id:u.inputId,type:"text",value:a?e.value:i,onChange:e=>{a||o.setState("search",e.target.value),null==n||n(e.target.value)}})}),R=c.forwardRef((e,t)=>{let{children:n,label:r="Suggestions",...a}=e,o=c.useRef(null),i=c.useRef(null),l=x();return c.useEffect(()=>{if(i.current&&o.current){let e=i.current,t=o.current,n,r=new ResizeObserver(()=>{n=requestAnimationFrame(()=>{let n=e.offsetHeight;t.style.setProperty("--cmdk-list-height",n.toFixed(1)+"px")})});return r.observe(e),()=>{cancelAnimationFrame(n),r.unobserve(e)}}},[]),c.createElement(d.WV.div,{ref:F([o,t]),...a,"cmdk-list":"",role:"listbox","aria-label":r,id:l.listId},T(e,e=>c.createElement("div",{ref:F([i,l.listInnerRef]),"cmdk-list-sizer":""},e)))}),A=c.forwardRef((e,t)=>{let{open:n,onOpenChange:r,overlayClassName:a,contentClassName:o,container:i,...l}=e;return c.createElement(u.fC,{open:n,onOpenChange:r},c.createElement(u.h_,{container:i},c.createElement(u.aV,{"cmdk-overlay":"",className:a}),c.createElement(u.VY,{"aria-label":e.label,"cmdk-dialog":"",className:o},c.createElement(D,{ref:t,...l}))))}),W=Object.assign(D,{List:R,Item:j,Input:P,Group:E,Separator:S,Dialog:A,Empty:c.forwardRef((e,t)=>V(e=>0===e.filtered.count)?c.createElement(d.WV.div,{ref:t,...e,"cmdk-empty":"",role:"presentation"}):null),Loading:c.forwardRef((e,t)=>{let{progress:n,children:r,label:a="Loading...",...o}=e;return c.createElement(d.WV.div,{ref:t,...o,"cmdk-loading":"",role:"progressbar","aria-valuenow":n,"aria-valuemin":0,"aria-valuemax":100,"aria-label":a},T(e,e=>c.createElement("div",{"aria-hidden":!0},e)))})});function Z(e){let t=c.useRef(e);return N(()=>{t.current=e}),t}var N="undefined"==typeof window?c.useEffect:c.useLayoutEffect;function I(e){let t=c.useRef();return void 0===t.current&&(t.current=e()),t}function F(e){return t=>{e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}}function V(e){let t=k(),n=()=>e(t.snapshot());return(0,f.useSyncExternalStore)(t.subscribe,n,n)}function O(e,t,n,r=[]){let a=c.useRef(),o=x();return N(()=>{var i;let l=(()=>{var e;for(let t of n){if("string"==typeof t)return t.trim();if("object"==typeof t&&"current"in t)return t.current?null==(e=t.current.textContent)?void 0:e.trim():a.current}})(),u=r.map(e=>e.trim());o.value(e,l,u),null==(i=t.current)||i.setAttribute(y,l),a.current=l}),a}var z=()=>{let[e,t]=c.useState(),n=I(()=>new Map);return N(()=>{n.current.forEach(e=>e()),n.current=new Map},[e]),(e,r)=>{n.current.set(e,r),t({})}};function T({asChild:e,children:t},n){let r;return e&&c.isValidElement(t)?c.cloneElement("function"==typeof(r=t.type)?r(t.props):"render"in r?r.render(t.props):t,{ref:t.ref},n(t.props.children)):n(t)}var _={position:"absolute",width:"1px",height:"1px",padding:"0",margin:"-1px",overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",borderWidth:"0"}},3766:function(e,t,n){var r;function a(e){let t=Object.prototype.toString.call(e);return e instanceof Date||"object"==typeof e&&"[object Date]"===t?new e.constructor(+e):new Date("number"==typeof e||"[object Number]"===t||"string"==typeof e||"[object String]"===t?e:NaN)}function o(e,t){let n=a(e),r=a(t),o=n.getTime()-r.getTime();return o<0?-1:o>0?1:o}n.d(t,{Q:function(){return m}});let i={lessThanXSeconds:{one:"less than a second",other:"less than {{count}} seconds"},xSeconds:{one:"1 second",other:"{{count}} seconds"},halfAMinute:"half a minute",lessThanXMinutes:{one:"less than a minute",other:"less than {{count}} minutes"},xMinutes:{one:"1 minute",other:"{{count}} minutes"},aboutXHours:{one:"about 1 hour",other:"about {{count}} hours"},xHours:{one:"1 hour",other:"{{count}} hours"},xDays:{one:"1 day",other:"{{count}} days"},aboutXWeeks:{one:"about 1 week",other:"about {{count}} weeks"},xWeeks:{one:"1 week",other:"{{count}} weeks"},aboutXMonths:{one:"about 1 month",other:"about {{count}} months"},xMonths:{one:"1 month",other:"{{count}} months"},aboutXYears:{one:"about 1 year",other:"about {{count}} years"},xYears:{one:"1 year",other:"{{count}} years"},overXYears:{one:"over 1 year",other:"over {{count}} years"},almostXYears:{one:"almost 1 year",other:"almost {{count}} years"}};function l(e){return function(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n=t.width?String(t.width):e.defaultWidth;return e.formats[n]||e.formats[e.defaultWidth]}}let u={date:l({formats:{full:"EEEE, MMMM do, y",long:"MMMM do, y",medium:"MMM d, y",short:"MM/dd/yyyy"},defaultWidth:"full"}),time:l({formats:{full:"h:mm:ss a zzzz",long:"h:mm:ss a z",medium:"h:mm:ss a",short:"h:mm a"},defaultWidth:"full"}),dateTime:l({formats:{full:"{{date}} 'at' {{time}}",long:"{{date}} 'at' {{time}}",medium:"{{date}}, {{time}}",short:"{{date}}, {{time}}"},defaultWidth:"full"})},c={lastWeek:"'last' eeee 'at' p",yesterday:"'yesterday at' p",today:"'today at' p",tomorrow:"'tomorrow at' p",nextWeek:"eeee 'at' p",other:"P"};function d(e){return(t,n)=>{let r;if("formatting"===((null==n?void 0:n.context)?String(n.context):"standalone")&&e.formattingValues){let t=e.defaultFormattingWidth||e.defaultWidth,a=(null==n?void 0:n.width)?String(n.width):t;r=e.formattingValues[a]||e.formattingValues[t]}else{let t=e.defaultWidth,a=(null==n?void 0:n.width)?String(n.width):e.defaultWidth;r=e.values[a]||e.values[t]}return r[e.argumentCallback?e.argumentCallback(t):t]}}function s(e){return function(t){let n,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},a=r.width,o=a&&e.matchPatterns[a]||e.matchPatterns[e.defaultMatchWidth],i=t.match(o);if(!i)return null;let l=i[0],u=a&&e.parsePatterns[a]||e.parsePatterns[e.defaultParseWidth],c=Array.isArray(u)?function(e,t){for(let n=0;n<e.length;n++)if(t(e[n]))return n}(u,e=>e.test(l)):function(e,t){for(let n in e)if(Object.prototype.hasOwnProperty.call(e,n)&&t(e[n]))return n}(u,e=>e.test(l));return n=e.valueCallback?e.valueCallback(c):c,{value:n=r.valueCallback?r.valueCallback(n):n,rest:t.slice(l.length)}}}let f={code:"en-US",formatDistance:(e,t,n)=>{let r;let a=i[e];return(r="string"==typeof a?a:1===t?a.one:a.other.replace("{{count}}",t.toString()),null==n?void 0:n.addSuffix)?n.comparison&&n.comparison>0?"in "+r:r+" ago":r},formatLong:u,formatRelative:(e,t,n,r)=>c[e],localize:{ordinalNumber:(e,t)=>{let n=Number(e),r=n%100;if(r>20||r<10)switch(r%10){case 1:return n+"st";case 2:return n+"nd";case 3:return n+"rd"}return n+"th"},era:d({values:{narrow:["B","A"],abbreviated:["BC","AD"],wide:["Before Christ","Anno Domini"]},defaultWidth:"wide"}),quarter:d({values:{narrow:["1","2","3","4"],abbreviated:["Q1","Q2","Q3","Q4"],wide:["1st quarter","2nd quarter","3rd quarter","4th quarter"]},defaultWidth:"wide",argumentCallback:e=>e-1}),month:d({values:{narrow:["J","F","M","A","M","J","J","A","S","O","N","D"],abbreviated:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"],wide:["January","February","March","April","May","June","July","August","September","October","November","December"]},defaultWidth:"wide"}),day:d({values:{narrow:["S","M","T","W","T","F","S"],short:["Su","Mo","Tu","We","Th","Fr","Sa"],abbreviated:["Sun","Mon","Tue","Wed","Thu","Fri","Sat"],wide:["Sunday","Monday","Tuesday","Wednesday","Thursday","Friday","Saturday"]},defaultWidth:"wide"}),dayPeriod:d({values:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"morning",afternoon:"afternoon",evening:"evening",night:"night"}},defaultWidth:"wide",formattingValues:{narrow:{am:"a",pm:"p",midnight:"mi",noon:"n",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},abbreviated:{am:"AM",pm:"PM",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"},wide:{am:"a.m.",pm:"p.m.",midnight:"midnight",noon:"noon",morning:"in the morning",afternoon:"in the afternoon",evening:"in the evening",night:"at night"}},defaultFormattingWidth:"wide"})},match:{ordinalNumber:(r={matchPattern:/^(\d+)(th|st|nd|rd)?/i,parsePattern:/\d+/i,valueCallback:e=>parseInt(e,10)},function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.match(r.matchPattern);if(!n)return null;let a=n[0],o=e.match(r.parsePattern);if(!o)return null;let i=r.valueCallback?r.valueCallback(o[0]):o[0];return{value:i=t.valueCallback?t.valueCallback(i):i,rest:e.slice(a.length)}}),era:s({matchPatterns:{narrow:/^(b|a)/i,abbreviated:/^(b\.?\s?c\.?|b\.?\s?c\.?\s?e\.?|a\.?\s?d\.?|c\.?\s?e\.?)/i,wide:/^(before christ|before common era|anno domini|common era)/i},defaultMatchWidth:"wide",parsePatterns:{any:[/^b/i,/^(a|c)/i]},defaultParseWidth:"any"}),quarter:s({matchPatterns:{narrow:/^[1234]/i,abbreviated:/^q[1234]/i,wide:/^[1234](th|st|nd|rd)? quarter/i},defaultMatchWidth:"wide",parsePatterns:{any:[/1/i,/2/i,/3/i,/4/i]},defaultParseWidth:"any",valueCallback:e=>e+1}),month:s({matchPatterns:{narrow:/^[jfmasond]/i,abbreviated:/^(jan|feb|mar|apr|may|jun|jul|aug|sep|oct|nov|dec)/i,wide:/^(january|february|march|april|may|june|july|august|september|october|november|december)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^j/i,/^f/i,/^m/i,/^a/i,/^m/i,/^j/i,/^j/i,/^a/i,/^s/i,/^o/i,/^n/i,/^d/i],any:[/^ja/i,/^f/i,/^mar/i,/^ap/i,/^may/i,/^jun/i,/^jul/i,/^au/i,/^s/i,/^o/i,/^n/i,/^d/i]},defaultParseWidth:"any"}),day:s({matchPatterns:{narrow:/^[smtwf]/i,short:/^(su|mo|tu|we|th|fr|sa)/i,abbreviated:/^(sun|mon|tue|wed|thu|fri|sat)/i,wide:/^(sunday|monday|tuesday|wednesday|thursday|friday|saturday)/i},defaultMatchWidth:"wide",parsePatterns:{narrow:[/^s/i,/^m/i,/^t/i,/^w/i,/^t/i,/^f/i,/^s/i],any:[/^su/i,/^m/i,/^tu/i,/^w/i,/^th/i,/^f/i,/^sa/i]},defaultParseWidth:"any"}),dayPeriod:s({matchPatterns:{narrow:/^(a|p|mi|n|(in the|at) (morning|afternoon|evening|night))/i,any:/^([ap]\.?\s?m\.?|midnight|noon|(in the|at) (morning|afternoon|evening|night))/i},defaultMatchWidth:"any",parsePatterns:{any:{am:/^a/i,pm:/^p/i,midnight:/^mi/i,noon:/^no/i,morning:/morning/i,afternoon:/afternoon/i,evening:/evening/i,night:/night/i}},defaultParseWidth:"any"})},options:{weekStartsOn:0,firstWeekContainsDate:1}},h={};function p(e){let t=a(e),n=new Date(Date.UTC(t.getFullYear(),t.getMonth(),t.getDate(),t.getHours(),t.getMinutes(),t.getSeconds(),t.getMilliseconds()));return n.setUTCFullYear(t.getFullYear()),+e-+n}function m(e,t){var n;return function(e,t,n){var r,i,l,u,c,d;let s,m,v;let g=null!==(i=null!==(r=null==n?void 0:n.locale)&&void 0!==r?r:h.locale)&&void 0!==i?i:f,y=o(e,t);if(isNaN(y))throw RangeError("Invalid time value");let b=Object.assign({},n,{addSuffix:null==n?void 0:n.addSuffix,comparison:y});y>0?(s=a(t),m=a(e)):(s=a(e),m=a(t));let w=(l=m,u=s,(d=null==void 0?void 0:(void 0).roundingMethod,e=>{let t=(d?Math[d]:Math.trunc)(e);return 0===t?0:t})((+a(l)-+a(u))/1e3)),x=Math.round((w-(p(m)-p(s))/1e3)/60);if(x<2){if(null==n?void 0:n.includeSeconds){if(w<5)return g.formatDistance("lessThanXSeconds",5,b);if(w<10)return g.formatDistance("lessThanXSeconds",10,b);if(w<20)return g.formatDistance("lessThanXSeconds",20,b);if(w<40)return g.formatDistance("halfAMinute",0,b);else if(w<60)return g.formatDistance("lessThanXMinutes",1,b);else return g.formatDistance("xMinutes",1,b)}return 0===x?g.formatDistance("lessThanXMinutes",1,b):g.formatDistance("xMinutes",x,b)}if(x<45)return g.formatDistance("xMinutes",x,b);if(x<90)return g.formatDistance("aboutXHours",1,b);if(x<1440)return g.formatDistance("aboutXHours",Math.round(x/60),b);if(x<2520)return g.formatDistance("xDays",1,b);if(x<43200)return g.formatDistance("xDays",Math.round(x/1440),b);if(x<86400)return v=Math.round(x/43200),g.formatDistance("aboutXMonths",v,b);if((v=function(e,t){let n;let r=a(e),i=a(t),l=o(r,i),u=Math.abs(function(e,t){let n=a(e),r=a(t);return 12*(n.getFullYear()-r.getFullYear())+(n.getMonth()-r.getMonth())}(r,i));if(u<1)n=0;else{1===r.getMonth()&&r.getDate()>27&&r.setDate(30),r.setMonth(r.getMonth()-l*u);let t=o(r,i)===-l;(function(e){let t=a(e);return+function(e){let t=a(e);return t.setHours(23,59,59,999),t}(t)==+function(e){let t=a(e),n=t.getMonth();return t.setFullYear(t.getFullYear(),n+1,0),t.setHours(23,59,59,999),t}(t)})(a(e))&&1===u&&1===o(e,i)&&(t=!1),n=l*(u-Number(t))}return 0===n?0:n}(m,s))<12)return g.formatDistance("xMonths",Math.round(x/43200),b);{let e=v%12,t=Math.trunc(v/12);return e<3?g.formatDistance("aboutXYears",t,b):e<9?g.formatDistance("overXYears",t,b):g.formatDistance("almostXYears",t+1,b)}}(e,(n=Date.now(),e instanceof Date?new e.constructor(n):new Date(n)),t)}}}]);