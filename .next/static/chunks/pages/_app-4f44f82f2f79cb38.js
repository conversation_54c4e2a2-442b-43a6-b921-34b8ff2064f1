(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[888],{8100:function(e,t,r){"use strict";r.d(t,{D3:function(){return ek},tw:function(){return ex}});var n=r(7294),i=function(e,t){return(i=Object.setPrototypeOf||({__proto__:[]})instanceof Array&&function(e,t){e.__proto__=t}||function(e,t){for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])})(e,t)},o=function(){return(o=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function s(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(e){o(e)}}function a(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){var t;e.done?i(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(s,a)}c((n=n.apply(e,t||[])).next())})}function a(e,t){var r,n,i,o={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]},s=Object.create(("function"==typeof Iterator?Iterator:Object).prototype);return s.next=a(0),s.throw=a(1),s.return=a(2),"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function a(a){return function(c){return function(a){if(r)throw TypeError("Generator is already executing.");for(;s&&(s=0,a[0]&&(o=0)),o;)try{if(r=1,n&&(i=2&a[0]?n.return:a[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,a[1])).done)return i;switch(n=0,i&&(a=[2&a[0],i.value]),a[0]){case 0:case 1:i=a;break;case 4:return o.label++,{value:a[1],done:!1};case 5:o.label++,n=a[1],a=[0];continue;case 7:a=o.ops.pop(),o.trys.pop();continue;default:if(!(i=(i=o.trys).length>0&&i[i.length-1])&&(6===a[0]||2===a[0])){o=0;continue}if(3===a[0]&&(!i||a[1]>i[0]&&a[1]<i[3])){o.label=a[1];break}if(6===a[0]&&o.label<i[1]){o.label=i[1],i=a;break}if(i&&o.label<i[2]){o.label=i[2],o.ops.push(a);break}i[2]&&o.ops.pop(),o.trys.pop();continue}a=t.call(e,o)}catch(e){a=[6,e],n=0}finally{r=i=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,c])}}}function c(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]])}return r}"function"==typeof SuppressedError&&SuppressedError,"function"==typeof SuppressedError&&SuppressedError;var u="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==r.g?r.g:"undefined"!=typeof self?self:{};function l(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}function d(e,t){return e(t={exports:{}},t.exports),t.exports}var h=d(function(e,t){Object.defineProperty(t,"__esModule",{value:!0});var r=function(){function e(){var e=this;this.locked=new Map,this.addToLocked=function(t,r){var n=e.locked.get(t);void 0===n?void 0===r?e.locked.set(t,[]):e.locked.set(t,[r]):void 0!==r&&(n.unshift(r),e.locked.set(t,n))},this.isLocked=function(t){return e.locked.has(t)},this.lock=function(t){return new Promise(function(r,n){e.isLocked(t)?e.addToLocked(t,r):(e.addToLocked(t),r())})},this.unlock=function(t){var r=e.locked.get(t);if(void 0!==r&&0!==r.length){var n=r.pop();e.locked.set(t,r),void 0!==n&&setTimeout(n,0)}else e.locked.delete(t)}}return e.getInstance=function(){return void 0===e.instance&&(e.instance=new e),e.instance},e}();t.default=function(){return r.getInstance()}});l(h);var p=l(d(function(e,t){var r=u&&u.__awaiter||function(e,t,r,n){return new(r||(r=Promise))(function(i,o){function s(e){try{c(n.next(e))}catch(e){o(e)}}function a(e){try{c(n.throw(e))}catch(e){o(e)}}function c(e){e.done?i(e.value):new r(function(t){t(e.value)}).then(s,a)}c((n=n.apply(e,t||[])).next())})},n=u&&u.__generator||function(e,t){var r,n,i,o,s={label:0,sent:function(){if(1&i[0])throw i[1];return i[1]},trys:[],ops:[]};return o={next:a(0),throw:a(1),return:a(2)},"function"==typeof Symbol&&(o[Symbol.iterator]=function(){return this}),o;function a(o){return function(a){return function(o){if(r)throw TypeError("Generator is already executing.");for(;s;)try{if(r=1,n&&(i=2&o[0]?n.return:o[0]?n.throw||((i=n.return)&&i.call(n),0):n.next)&&!(i=i.call(n,o[1])).done)return i;switch(n=0,i&&(o=[2&o[0],i.value]),o[0]){case 0:case 1:i=o;break;case 4:return s.label++,{value:o[1],done:!1};case 5:s.label++,n=o[1],o=[0];continue;case 7:o=s.ops.pop(),s.trys.pop();continue;default:if(!(i=(i=s.trys).length>0&&i[i.length-1])&&(6===o[0]||2===o[0])){s=0;continue}if(3===o[0]&&(!i||o[1]>i[0]&&o[1]<i[3])){s.label=o[1];break}if(6===o[0]&&s.label<i[1]){s.label=i[1],i=o;break}if(i&&s.label<i[2]){s.label=i[2],s.ops.push(o);break}i[2]&&s.ops.pop(),s.trys.pop();continue}o=t.call(e,s)}catch(e){o=[6,e],n=0}finally{r=i=0}if(5&o[0])throw o[1];return{value:o[0]?o[1]:void 0,done:!0}}([o,a])}}};Object.defineProperty(t,"__esModule",{value:!0});var i="browser-tabs-lock-key",o={key:function(e){return r(u,void 0,void 0,function(){return n(this,function(e){throw Error("Unsupported")})})},getItem:function(e){return r(u,void 0,void 0,function(){return n(this,function(e){throw Error("Unsupported")})})},clear:function(){return r(u,void 0,void 0,function(){return n(this,function(e){return[2,window.localStorage.clear()]})})},removeItem:function(e){return r(u,void 0,void 0,function(){return n(this,function(e){throw Error("Unsupported")})})},setItem:function(e,t){return r(u,void 0,void 0,function(){return n(this,function(e){throw Error("Unsupported")})})},keySync:function(e){return window.localStorage.key(e)},getItemSync:function(e){return window.localStorage.getItem(e)},clearSync:function(){return window.localStorage.clear()},removeItemSync:function(e){return window.localStorage.removeItem(e)},setItemSync:function(e,t){return window.localStorage.setItem(e,t)}};function s(e){return new Promise(function(t){return setTimeout(t,e)})}function a(e){for(var t="0123456789ABCDEFGHIJKLMNOPQRSTUVWXTZabcdefghiklmnopqrstuvwxyz",r="",n=0;n<e;n++)r+=t[Math.floor(Math.random()*t.length)];return r}var c=function(){function e(t){this.acquiredIatSet=new Set,this.storageHandler=void 0,this.id=Date.now().toString()+a(15),this.acquireLock=this.acquireLock.bind(this),this.releaseLock=this.releaseLock.bind(this),this.releaseLock__private__=this.releaseLock__private__.bind(this),this.waitForSomethingToChange=this.waitForSomethingToChange.bind(this),this.refreshLockWhileAcquired=this.refreshLockWhileAcquired.bind(this),this.storageHandler=t,void 0===e.waiters&&(e.waiters=[])}return e.prototype.acquireLock=function(t,c){return void 0===c&&(c=5e3),r(this,void 0,void 0,function(){var r,u,l,d,h,p,f;return n(this,function(n){switch(n.label){case 0:r=Date.now()+a(4),u=Date.now()+c,l=i+"-"+t,d=void 0===this.storageHandler?o:this.storageHandler,n.label=1;case 1:return Date.now()<u?[4,s(30)]:[3,8];case 2:return n.sent(),null!==d.getItemSync(l)?[3,5]:(h=this.id+"-"+t+"-"+r,[4,s(Math.floor(25*Math.random()))]);case 3:return n.sent(),d.setItemSync(l,JSON.stringify({id:this.id,iat:r,timeoutKey:h,timeAcquired:Date.now(),timeRefreshed:Date.now()})),[4,s(30)];case 4:return n.sent(),null!==(p=d.getItemSync(l))&&(f=JSON.parse(p)).id===this.id&&f.iat===r?(this.acquiredIatSet.add(r),this.refreshLockWhileAcquired(l,r),[2,!0]):[3,7];case 5:return e.lockCorrector(void 0===this.storageHandler?o:this.storageHandler),[4,this.waitForSomethingToChange(u)];case 6:n.sent(),n.label=7;case 7:return r=Date.now()+a(4),[3,1];case 8:return[2,!1]}})})},e.prototype.refreshLockWhileAcquired=function(e,t){return r(this,void 0,void 0,function(){var i=this;return n(this,function(s){return setTimeout(function(){return r(i,void 0,void 0,function(){var r,i,s;return n(this,function(n){switch(n.label){case 0:return[4,h.default().lock(t)];case 1:return n.sent(),this.acquiredIatSet.has(t)?null===(i=(r=void 0===this.storageHandler?o:this.storageHandler).getItemSync(e))?h.default().unlock(t):((s=JSON.parse(i)).timeRefreshed=Date.now(),r.setItemSync(e,JSON.stringify(s)),h.default().unlock(t),this.refreshLockWhileAcquired(e,t)):h.default().unlock(t),[2]}})})},1e3),[2]})})},e.prototype.waitForSomethingToChange=function(t){return r(this,void 0,void 0,function(){return n(this,function(r){switch(r.label){case 0:return[4,new Promise(function(r){var n=!1,i=Date.now(),o=!1;function s(){if(o||(window.removeEventListener("storage",s),e.removeFromWaiting(s),clearTimeout(a),o=!0),!n){n=!0;var t=50-(Date.now()-i);t>0?setTimeout(r,t):r(null)}}window.addEventListener("storage",s),e.addToWaiting(s);var a=setTimeout(s,Math.max(0,t-Date.now()))})];case 1:return r.sent(),[2]}})})},e.addToWaiting=function(t){this.removeFromWaiting(t),void 0!==e.waiters&&e.waiters.push(t)},e.removeFromWaiting=function(t){void 0!==e.waiters&&(e.waiters=e.waiters.filter(function(e){return e!==t}))},e.notifyWaiters=function(){void 0!==e.waiters&&e.waiters.slice().forEach(function(e){return e()})},e.prototype.releaseLock=function(e){return r(this,void 0,void 0,function(){return n(this,function(t){switch(t.label){case 0:return[4,this.releaseLock__private__(e)];case 1:return[2,t.sent()]}})})},e.prototype.releaseLock__private__=function(t){return r(this,void 0,void 0,function(){var r,s,a,c;return n(this,function(n){switch(n.label){case 0:return r=void 0===this.storageHandler?o:this.storageHandler,s=i+"-"+t,null===(a=r.getItemSync(s))?[2]:(c=JSON.parse(a)).id!==this.id?[3,2]:[4,h.default().lock(c.iat)];case 1:n.sent(),this.acquiredIatSet.delete(c.iat),r.removeItemSync(s),h.default().unlock(c.iat),e.notifyWaiters(),n.label=2;case 2:return[2]}})})},e.lockCorrector=function(t){for(var r=Date.now()-5e3,n=[],o=0;;){var s=t.keySync(o);if(null===s)break;n.push(s),o++}for(var a=!1,c=0;c<n.length;c++){var u=n[c];if(u.includes(i)){var l=t.getItemSync(u);if(null!==l){var d=JSON.parse(l);(void 0===d.timeRefreshed&&d.timeAcquired<r||void 0!==d.timeRefreshed&&d.timeRefreshed<r)&&(t.removeItemSync(u),a=!0)}}}a&&e.notifyWaiters()},e.waiters=void 0,e}();t.default=c}));let f={timeoutInSeconds:60},m={name:"auth0-spa-js",version:"2.1.3"},y=()=>Date.now();class v extends Error{constructor(e,t){super(t),this.error=e,this.error_description=t,Object.setPrototypeOf(this,v.prototype)}static fromPayload({error:e,error_description:t}){return new v(e,t)}}class g extends v{constructor(e,t,r,n=null){super(e,t),this.state=r,this.appState=n,Object.setPrototypeOf(this,g.prototype)}}class b extends v{constructor(){super("timeout","Timeout"),Object.setPrototypeOf(this,b.prototype)}}class w extends b{constructor(e){super(),this.popup=e,Object.setPrototypeOf(this,w.prototype)}}class x extends v{constructor(e){super("cancelled","Popup closed"),this.popup=e,Object.setPrototypeOf(this,x.prototype)}}class k extends v{constructor(e,t,r){super(e,t),this.mfa_token=r,Object.setPrototypeOf(this,k.prototype)}}class C extends v{constructor(e,t){super("missing_refresh_token",`Missing Refresh Token (audience: '${E(e,["default"])}', scope: '${E(t)}')`),this.audience=e,this.scope=t,Object.setPrototypeOf(this,C.prototype)}}function E(e,t=[]){return e&&!t.includes(e)?e:""}let T=()=>window.crypto,O=()=>{let e="0123456789ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz-_~.",t="";return Array.from(T().getRandomValues(new Uint8Array(43))).forEach(r=>t+=e[r%e.length]),t},S=e=>btoa(e),_=e=>{let t;var{clientId:r}=e;return new URLSearchParams(Object.keys(t=Object.assign({client_id:r},c(e,["clientId"]))).filter(e=>void 0!==t[e]).reduce((e,r)=>Object.assign(Object.assign({},e),{[r]:t[r]}),{})).toString()},P=e=>decodeURIComponent(atob(e.replace(/_/g,"/").replace(/-/g,"+")).split("").map(e=>"%"+("00"+e.charCodeAt(0).toString(16)).slice(-2)).join("")),I=async(e,t)=>{let r=await fetch(e,t);return{ok:r.ok,json:await r.json()}},R=async(e,t,r)=>{let n;let i=new AbortController;return t.signal=i.signal,Promise.race([I(e,t),new Promise((e,t)=>{n=setTimeout(()=>{i.abort(),t(Error("Timeout when executing 'fetch'"))},r)})]).finally(()=>{clearTimeout(n)})},j=async(e,t,r,n,i,o,s)=>{var a;return a={auth:{audience:t,scope:r},timeout:i,fetchUrl:e,fetchOptions:n,useFormData:s},new Promise(function(e,t){let r=new MessageChannel;r.port1.onmessage=function(n){n.data.error?t(Error(n.data.error)):e(n.data),r.port1.close()},o.postMessage(a,[r.port2])})},N=async(e,t,r,n,i,o,s=1e4)=>i?j(e,t,r,n,s,i,o):R(e,n,s);async function D(e,t){var{baseUrl:r,timeout:n,audience:i,scope:o,auth0Client:s,useFormData:a}=e,u=c(e,["baseUrl","timeout","audience","scope","auth0Client","useFormData"]);let l=a?_(u):JSON.stringify(u);return await async function(e,t,r,n,i,o,s){let a,u=null;for(let c=0;c<3;c++)try{a=await N(e,r,n,i,o,s,t),u=null;break}catch(e){u=e}if(u)throw u;let l=a.json,{error:d,error_description:h}=l,p=c(l,["error","error_description"]),{ok:f}=a;if(!f){let t=h||`HTTP error. Unable to fetch ${e}`;if("mfa_required"===d)throw new k(d,t,p.mfa_token);if("missing_refresh_token"===d)throw new C(r,n);throw new v(d||"request_error",t)}return p}(`${r}/oauth/token`,n,i||"default",o,{method:"POST",body:l,headers:{"Content-Type":a?"application/x-www-form-urlencoded":"application/json","Auth0-Client":btoa(JSON.stringify(s||m))}},t,a)}let L=(...e)=>Array.from(new Set(e.filter(Boolean).join(" ").trim().split(/\s+/))).join(" ");class A{constructor(e,t="@@auth0spajs@@",r){this.prefix=t,this.suffix=r,this.clientId=e.clientId,this.scope=e.scope,this.audience=e.audience}toKey(){return[this.prefix,this.clientId,this.audience,this.scope,this.suffix].filter(Boolean).join("::")}static fromKey(e){let[t,r,n,i]=e.split("::");return new A({clientId:r,scope:i,audience:n},t)}static fromCacheEntry(e){let{scope:t,audience:r,client_id:n}=e;return new A({scope:t,audience:r,clientId:n})}}class M{set(e,t){localStorage.setItem(e,JSON.stringify(t))}get(e){let t=window.localStorage.getItem(e);if(t)try{return JSON.parse(t)}catch(e){return}}remove(e){localStorage.removeItem(e)}allKeys(){return Object.keys(window.localStorage).filter(e=>e.startsWith("@@auth0spajs@@"))}}class z{constructor(){let e;this.enclosedCache=(e={},{set(t,r){e[t]=r},get(t){let r=e[t];if(r)return r},remove(t){delete e[t]},allKeys:()=>Object.keys(e)})}}class F{constructor(e,t,r){this.cache=e,this.keyManifest=t,this.nowProvider=r||y}async setIdToken(e,t,r){var n;let i=this.getIdTokenCacheKey(e);await this.cache.set(i,{id_token:t,decodedToken:r}),await (null===(n=this.keyManifest)||void 0===n?void 0:n.add(i))}async getIdToken(e){let t=await this.cache.get(this.getIdTokenCacheKey(e.clientId));if(!t&&e.scope&&e.audience){let t=await this.get(e);if(!t||!t.id_token||!t.decodedToken)return;return{id_token:t.id_token,decodedToken:t.decodedToken}}if(t)return{id_token:t.id_token,decodedToken:t.decodedToken}}async get(e,t=0){var r;let n=await this.cache.get(e.toKey());if(!n){let t=await this.getCacheKeys();if(!t)return;let r=this.matchExistingCacheKey(e,t);r&&(n=await this.cache.get(r))}if(!n)return;let i=await this.nowProvider();return n.expiresAt-t<Math.floor(i/1e3)?n.body.refresh_token?(n.body={refresh_token:n.body.refresh_token},await this.cache.set(e.toKey(),n),n.body):(await this.cache.remove(e.toKey()),void await (null===(r=this.keyManifest)||void 0===r?void 0:r.remove(e.toKey()))):n.body}async set(e){var t;let r=new A({clientId:e.client_id,scope:e.scope,audience:e.audience}),n=await this.wrapCacheEntry(e);await this.cache.set(r.toKey(),n),await (null===(t=this.keyManifest)||void 0===t?void 0:t.add(r.toKey()))}async clear(e){var t;let r=await this.getCacheKeys();r&&(await r.filter(t=>!e||t.includes(e)).reduce(async(e,t)=>{await e,await this.cache.remove(t)},Promise.resolve()),await (null===(t=this.keyManifest)||void 0===t?void 0:t.clear()))}async wrapCacheEntry(e){return{body:e,expiresAt:Math.floor(await this.nowProvider()/1e3)+e.expires_in}}async getCacheKeys(){var e;return this.keyManifest?null===(e=await this.keyManifest.get())||void 0===e?void 0:e.keys:this.cache.allKeys?this.cache.allKeys():void 0}getIdTokenCacheKey(e){return new A({clientId:e},"@@auth0spajs@@","@@user@@").toKey()}matchExistingCacheKey(e,t){return t.filter(t=>{var r;let n=A.fromKey(t),i=new Set(n.scope&&n.scope.split(" ")),o=(null===(r=e.scope)||void 0===r?void 0:r.split(" "))||[],s=n.scope&&o.reduce((e,t)=>e&&i.has(t),!0);return"@@auth0spajs@@"===n.prefix&&n.clientId===e.clientId&&n.audience===e.audience&&s})[0]}}class U{constructor(e,t,r){this.storage=e,this.clientId=t,this.cookieDomain=r,this.storageKey=`a0.spajs.txs.${this.clientId}`}create(e){this.storage.save(this.storageKey,e,{daysUntilExpire:1,cookieDomain:this.cookieDomain})}get(){return this.storage.get(this.storageKey)}remove(){this.storage.remove(this.storageKey,{cookieDomain:this.cookieDomain})}}let K=e=>"number"==typeof e,W=["iss","aud","exp","nbf","iat","jti","azp","nonce","auth_time","at_hash","c_hash","acr","amr","sub_jwk","cnf","sip_from_tag","sip_date","sip_callid","sip_cseq_num","sip_via_branch","orig","dest","mky","events","toe","txn","rph","sid","vot","vtm"],q=e=>{if(!e.id_token)throw Error("ID token is required but missing");let t=(e=>{let t=e.split("."),[r,n,i]=t;if(3!==t.length||!r||!n||!i)throw Error("ID token could not be decoded");let o=JSON.parse(P(n)),s={__raw:e},a={};return Object.keys(o).forEach(e=>{s[e]=o[e],W.includes(e)||(a[e]=o[e])}),{encoded:{header:r,payload:n,signature:i},header:JSON.parse(P(r)),claims:s,user:a}})(e.id_token);if(!t.claims.iss)throw Error("Issuer (iss) claim must be a string present in the ID token");if(t.claims.iss!==e.iss)throw Error(`Issuer (iss) claim mismatch in the ID token; expected "${e.iss}", found "${t.claims.iss}"`);if(!t.user.sub)throw Error("Subject (sub) claim must be a string present in the ID token");if("RS256"!==t.header.alg)throw Error(`Signature algorithm of "${t.header.alg}" is not supported. Expected the ID token to be signed with "RS256".`);if(!t.claims.aud||"string"!=typeof t.claims.aud&&!Array.isArray(t.claims.aud))throw Error("Audience (aud) claim must be a string or array of strings present in the ID token");if(Array.isArray(t.claims.aud)){if(!t.claims.aud.includes(e.aud))throw Error(`Audience (aud) claim mismatch in the ID token; expected "${e.aud}" but was not one of "${t.claims.aud.join(", ")}"`);if(t.claims.aud.length>1){if(!t.claims.azp)throw Error("Authorized Party (azp) claim must be a string present in the ID token when Audience (aud) claim has multiple values");if(t.claims.azp!==e.aud)throw Error(`Authorized Party (azp) claim mismatch in the ID token; expected "${e.aud}", found "${t.claims.azp}"`)}}else if(t.claims.aud!==e.aud)throw Error(`Audience (aud) claim mismatch in the ID token; expected "${e.aud}" but found "${t.claims.aud}"`);if(e.nonce){if(!t.claims.nonce)throw Error("Nonce (nonce) claim must be a string present in the ID token");if(t.claims.nonce!==e.nonce)throw Error(`Nonce (nonce) claim mismatch in the ID token; expected "${e.nonce}", found "${t.claims.nonce}"`)}if(e.max_age&&!K(t.claims.auth_time))throw Error("Authentication Time (auth_time) claim must be a number present in the ID token when Max Age (max_age) is specified");if(null==t.claims.exp||!K(t.claims.exp))throw Error("Expiration Time (exp) claim must be a number present in the ID token");if(!K(t.claims.iat))throw Error("Issued At (iat) claim must be a number present in the ID token");let r=e.leeway||60,n=new Date(e.now||Date.now()),i=new Date(0);if(i.setUTCSeconds(t.claims.exp+r),n>i)throw Error(`Expiration Time (exp) claim error in the ID token; current time (${n}) is after expiration time (${i})`);if(null!=t.claims.nbf&&K(t.claims.nbf)){let e=new Date(0);if(e.setUTCSeconds(t.claims.nbf-r),n<e)throw Error(`Not Before time (nbf) claim in the ID token indicates that this token can't be used just yet. Current time (${n}) is before ${e}`)}if(null!=t.claims.auth_time&&K(t.claims.auth_time)){let i=new Date(0);if(i.setUTCSeconds(parseInt(t.claims.auth_time)+e.max_age+r),n>i)throw Error(`Authentication Time (auth_time) claim in the ID token indicates that too much time has passed since the last end-user authentication. Current time (${n}) is after last auth at ${i}`)}if(e.organization){let r=e.organization.trim();if(r.startsWith("org_")){if(!t.claims.org_id)throw Error("Organization ID (org_id) claim must be a string present in the ID token");if(r!==t.claims.org_id)throw Error(`Organization ID (org_id) claim mismatch in the ID token; expected "${r}", found "${t.claims.org_id}"`)}else{let e=r.toLowerCase();if(!t.claims.org_name)throw Error("Organization Name (org_name) claim must be a string present in the ID token");if(e!==t.claims.org_name)throw Error(`Organization Name (org_name) claim mismatch in the ID token; expected "${e}", found "${t.claims.org_name}"`)}}return t};var Z=d(function(e,t){var r=u&&u.__assign||function(){return(r=Object.assign||function(e){for(var t,r=1,n=arguments.length;r<n;r++)for(var i in t=arguments[r])Object.prototype.hasOwnProperty.call(t,i)&&(e[i]=t[i]);return e}).apply(this,arguments)};function n(e,t){if(!t)return"";var r="; "+e;return!0===t?r:r+"="+t}function i(e,t,r){return encodeURIComponent(e).replace(/%(23|24|26|2B|5E|60|7C)/g,decodeURIComponent).replace(/\(/g,"%28").replace(/\)/g,"%29")+"="+encodeURIComponent(t).replace(/%(23|24|26|2B|3A|3C|3E|3D|2F|3F|40|5B|5D|5E|60|7B|7D|7C)/g,decodeURIComponent)+function(e){if("number"==typeof e.expires){var t=new Date;t.setMilliseconds(t.getMilliseconds()+864e5*e.expires),e.expires=t}return n("Expires",e.expires?e.expires.toUTCString():"")+n("Domain",e.domain)+n("Path",e.path)+n("Secure",e.secure)+n("SameSite",e.sameSite)}(r)}function o(e){for(var t={},r=e?e.split("; "):[],n=/(%[\dA-F]{2})+/gi,i=0;i<r.length;i++){var o=r[i].split("="),s=o.slice(1).join("=");'"'===s.charAt(0)&&(s=s.slice(1,-1));try{t[o[0].replace(n,decodeURIComponent)]=s.replace(n,decodeURIComponent)}catch(e){}}return t}function s(){return o(document.cookie)}function a(e,t,n){document.cookie=i(e,t,r({path:"/"},n))}t.__esModule=!0,t.encode=i,t.parse=o,t.getAll=s,t.get=function(e){return s()[e]},t.set=a,t.remove=function(e,t){a(e,"",r(r({},t),{expires:-1}))}});l(Z),Z.encode,Z.parse,Z.getAll;var Q=Z.get,V=Z.set,G=Z.remove;let H={get(e){let t=Q(e);if(void 0!==t)return JSON.parse(t)},save(e,t,r){let n={};"https:"===window.location.protocol&&(n={secure:!0,sameSite:"none"}),(null==r?void 0:r.daysUntilExpire)&&(n.expires=r.daysUntilExpire),(null==r?void 0:r.cookieDomain)&&(n.domain=r.cookieDomain),V(e,JSON.stringify(t),n)},remove(e,t){let r={};(null==t?void 0:t.cookieDomain)&&(r.domain=t.cookieDomain),G(e,r)}},X={get:e=>H.get(e)||H.get(`_legacy_${e}`),save(e,t,r){let n={};"https:"===window.location.protocol&&(n={secure:!0}),(null==r?void 0:r.daysUntilExpire)&&(n.expires=r.daysUntilExpire),(null==r?void 0:r.cookieDomain)&&(n.domain=r.cookieDomain),V(`_legacy_${e}`,JSON.stringify(t),n),H.save(e,t,r)},remove(e,t){let r={};(null==t?void 0:t.cookieDomain)&&(r.domain=t.cookieDomain),G(e,r),H.remove(e,t),H.remove(`_legacy_${e}`,t)}},Y={get(e){if("undefined"==typeof sessionStorage)return;let t=sessionStorage.getItem(e);return null!=t?JSON.parse(t):void 0},save(e,t){sessionStorage.setItem(e,JSON.stringify(t))},remove(e){sessionStorage.removeItem(e)}};var J,$,B=(J="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",function(e){var t,r,n;return new Worker($=$||(r=(t=function(e,t){var r=atob(e);if(t){for(var n=new Uint8Array(r.length),i=0,o=r.length;i<o;++i)n[i]=r.charCodeAt(i);return String.fromCharCode.apply(null,new Uint16Array(n.buffer))}return r}(J,!1)).indexOf("\n",10)+1,n=new Blob([t.substring(r)+""],{type:"application/javascript"}),URL.createObjectURL(n)),e)});let ee={};class et{constructor(e,t){this.cache=e,this.clientId=t,this.manifestKey=this.createManifestKeyFrom(this.clientId)}async add(e){var t;let r=new Set((null===(t=await this.cache.get(this.manifestKey))||void 0===t?void 0:t.keys)||[]);r.add(e),await this.cache.set(this.manifestKey,{keys:[...r]})}async remove(e){let t=await this.cache.get(this.manifestKey);if(t){let r=new Set(t.keys);return r.delete(e),r.size>0?await this.cache.set(this.manifestKey,{keys:[...r]}):await this.cache.remove(this.manifestKey)}}get(){return this.cache.get(this.manifestKey)}clear(){return this.cache.remove(this.manifestKey)}createManifestKeyFrom(e){return`@@auth0spajs@@::${e}`}}let er={memory:()=>(new z).enclosedCache,localstorage:()=>new M},en=e=>er[e],ei=e=>{let{openUrl:t,onRedirect:r}=e;return Object.assign(Object.assign({},c(e,["openUrl","onRedirect"])),{openUrl:!1===t||t?t:r})},eo=new p;class es{constructor(e){var t;let r,n,i,o,s;if(this.userCache=(new z).enclosedCache,this.defaultOptions={authorizationParams:{scope:"openid profile email"},useRefreshTokensFallback:!1,useFormData:!0},this._releaseLockOnPageHide=async()=>{await eo.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)},this.options=Object.assign(Object.assign(Object.assign({},this.defaultOptions),e),{authorizationParams:Object.assign(Object.assign({},this.defaultOptions.authorizationParams),e.authorizationParams)}),"undefined"!=typeof window&&(()=>{if(!T())throw Error("For security reasons, `window.crypto` is required to run `auth0-spa-js`.");if(void 0===T().subtle)throw Error("\n      auth0-spa-js must run on a secure origin. See https://github.com/auth0/auth0-spa-js/blob/main/FAQ.md#why-do-i-get-auth0-spa-js-must-run-on-a-secure-origin for more information.\n    ")})(),e.cache&&e.cacheLocation&&console.warn("Both `cache` and `cacheLocation` options have been specified in the Auth0Client configuration; ignoring `cacheLocation` and using `cache`."),e.cache)n=e.cache;else{if(!en(r=e.cacheLocation||"memory"))throw Error(`Invalid cache location "${r}"`);n=en(r)()}this.httpTimeoutMs=e.httpTimeoutInSeconds?1e3*e.httpTimeoutInSeconds:1e4,this.cookieStorage=!1===e.legacySameSiteCookie?H:X,this.orgHintCookieName=`auth0.${this.options.clientId}.organization_hint`,this.isAuthenticatedCookieName=(i=this.options.clientId,`auth0.${i}.is.authenticated`),this.sessionCheckExpiryDays=e.sessionCheckExpiryDays||1;let a=e.useCookiesForTransactions?this.cookieStorage:Y;this.scope=L("openid",this.options.authorizationParams.scope,this.options.useRefreshTokens?"offline_access":""),this.transactionManager=new U(a,this.options.clientId,this.options.cookieDomain),this.nowProvider=this.options.nowProvider||y,this.cacheManager=new F(n,n.allKeys?void 0:new et(n,this.options.clientId),this.nowProvider),this.domainUrl=(t=this.options.domain,/^https?:\/\//.test(t)?t:`https://${t}`),this.tokenIssuer=(o=this.options.issuer,s=this.domainUrl,o?o.startsWith("https://")?o:`https://${o}/`:`${s}/`),"undefined"!=typeof window&&window.Worker&&this.options.useRefreshTokens&&"memory"===r&&(this.options.workerUrl?this.worker=new Worker(this.options.workerUrl):this.worker=new B)}_url(e){let t=encodeURIComponent(btoa(JSON.stringify(this.options.auth0Client||m)));return`${this.domainUrl}${e}&auth0Client=${t}`}_authorizeUrl(e){return this._url(`/authorize?${_(e)}`)}async _verifyIdToken(e,t,r){var n;let i=await this.nowProvider();return q({iss:this.tokenIssuer,aud:this.options.clientId,id_token:e,nonce:t,organization:r,leeway:this.options.leeway,max_age:"string"!=typeof(n=this.options.authorizationParams.max_age)?n:parseInt(n,10)||void 0,now:i})}_processOrgHint(e){e?this.cookieStorage.save(this.orgHintCookieName,e,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}):this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain})}async _prepareAuthorizeUrl(e,t,r){let n,i,o,s;let a=S(O()),c=S(O()),u=O(),l=(e=>{let t=new Uint8Array(e);return(e=>{let t={"+":"-","/":"_","=":""};return e.replace(/[+/=]/g,e=>t[e])})(window.btoa(String.fromCharCode(...Array.from(t))))})(await (async e=>{let t=T().subtle.digest({name:"SHA-256"},(new TextEncoder).encode(e));return await t})(u)),d=(n=this.options,i=this.scope,o=e.redirect_uri||this.options.authorizationParams.redirect_uri||r,s=null==t?void 0:t.response_mode,Object.assign(Object.assign(Object.assign({client_id:n.clientId},n.authorizationParams),e),{scope:L(i,e.scope),response_type:"code",response_mode:s||"query",state:a,nonce:c,redirect_uri:o||n.authorizationParams.redirect_uri,code_challenge:l,code_challenge_method:"S256"})),h=this._authorizeUrl(d);return{nonce:c,code_verifier:u,scope:d.scope,audience:d.audience||"default",redirect_uri:d.redirect_uri,state:a,url:h}}async loginWithPopup(e,t){var r;let n;if(e=e||{},!(t=t||{}).popup&&(t.popup=(e=>{let t=window.screenX+(window.innerWidth-400)/2,r=window.screenY+(window.innerHeight-600)/2;return window.open(e,"auth0:authorize:popup",`left=${t},top=${r},width=400,height=600,resizable,scrollbars=yes,status=1`)})(""),!t.popup))throw Error("Unable to open a popup for loginWithPopup - window.open returned `null`");let i=await this._prepareAuthorizeUrl(e.authorizationParams||{},{response_mode:"web_message"},window.location.origin);t.popup.location.href=i.url;let o=await (n=Object.assign(Object.assign({},t),{timeoutInSeconds:t.timeoutInSeconds||this.options.authorizeTimeoutInSeconds||60}),new Promise((e,t)=>{let r;let i=setInterval(()=>{n.popup&&n.popup.closed&&(clearInterval(i),clearTimeout(o),window.removeEventListener("message",r,!1),t(new x(n.popup)))},1e3),o=setTimeout(()=>{clearInterval(i),t(new w(n.popup)),window.removeEventListener("message",r,!1)},1e3*(n.timeoutInSeconds||60));r=function(s){if(s.data&&"authorization_response"===s.data.type){if(clearTimeout(o),clearInterval(i),window.removeEventListener("message",r,!1),n.popup.close(),s.data.response.error)return t(v.fromPayload(s.data.response));e(s.data.response)}},window.addEventListener("message",r)}));if(i.state!==o.state)throw new v("state_mismatch","Invalid state");let s=(null===(r=e.authorizationParams)||void 0===r?void 0:r.organization)||this.options.authorizationParams.organization;await this._requestToken({audience:i.audience,scope:i.scope,code_verifier:i.code_verifier,grant_type:"authorization_code",code:o.code,redirect_uri:i.redirect_uri},{nonceIn:i.nonce,organization:s})}async getUser(){var e;let t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.user}async getIdTokenClaims(){var e;let t=await this._getIdTokenFromCache();return null===(e=null==t?void 0:t.decodedToken)||void 0===e?void 0:e.claims}async loginWithRedirect(e={}){var t;let r=ei(e),{openUrl:n,fragment:i,appState:o}=r,s=c(r,["openUrl","fragment","appState"]),a=(null===(t=s.authorizationParams)||void 0===t?void 0:t.organization)||this.options.authorizationParams.organization,u=await this._prepareAuthorizeUrl(s.authorizationParams||{}),{url:l}=u,d=c(u,["url"]);this.transactionManager.create(Object.assign(Object.assign(Object.assign({},d),{appState:o}),a&&{organization:a}));let h=i?`${l}#${i}`:l;n?await n(h):window.location.assign(h)}async handleRedirectCallback(e=window.location.href){let t=e.split("?").slice(1);if(0===t.length)throw Error("There are no query params available for parsing.");let{state:r,code:n,error:i,error_description:o}=(e=>{e.indexOf("#")>-1&&(e=e.substring(0,e.indexOf("#")));let t=new URLSearchParams(e);return{state:t.get("state"),code:t.get("code")||void 0,error:t.get("error")||void 0,error_description:t.get("error_description")||void 0}})(t.join("")),s=this.transactionManager.get();if(!s)throw new v("missing_transaction","Invalid state");if(this.transactionManager.remove(),i)throw new g(i,o||i,r,s.appState);if(!s.code_verifier||s.state&&s.state!==r)throw new v("state_mismatch","Invalid state");let a=s.organization,c=s.nonce,u=s.redirect_uri;return await this._requestToken(Object.assign({audience:s.audience,scope:s.scope,code_verifier:s.code_verifier,grant_type:"authorization_code",code:n},u?{redirect_uri:u}:{}),{nonceIn:c,organization:a}),{appState:s.appState}}async checkSession(e){if(!this.cookieStorage.get(this.isAuthenticatedCookieName)){if(!this.cookieStorage.get("auth0.is.authenticated"))return;this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove("auth0.is.authenticated")}try{await this.getTokenSilently(e)}catch(e){}}async getTokenSilently(e={}){var t,r,n;let i;let o=Object.assign(Object.assign({cacheMode:"on"},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:L(this.scope,null===(t=e.authorizationParams)||void 0===t?void 0:t.scope)})}),s=await (r=()=>this._getTokenSilently(o),(i=ee[n=`${this.options.clientId}::${o.authorizationParams.audience}::${o.authorizationParams.scope}`])||(i=r().finally(()=>{delete ee[n],i=null}),ee[n]=i),i);return e.detailedResponse?s:null==s?void 0:s.access_token}async _getTokenSilently(e){let{cacheMode:t}=e,r=c(e,["cacheMode"]);if("off"!==t){let e=await this._getEntryFromCache({scope:r.authorizationParams.scope,audience:r.authorizationParams.audience||"default",clientId:this.options.clientId});if(e)return e}if("cache-only"!==t){if(!await (async(e,t=3)=>{for(let r=0;r<t;r++)if(await e())return!0;return!1})(()=>eo.acquireLock("auth0.lock.getTokenSilently",5e3),10))throw new b;try{if(window.addEventListener("pagehide",this._releaseLockOnPageHide),"off"!==t){let e=await this._getEntryFromCache({scope:r.authorizationParams.scope,audience:r.authorizationParams.audience||"default",clientId:this.options.clientId});if(e)return e}let{id_token:e,access_token:n,oauthTokenScope:i,expires_in:o}=this.options.useRefreshTokens?await this._getTokenUsingRefreshToken(r):await this._getTokenFromIFrame(r);return Object.assign(Object.assign({id_token:e,access_token:n},i?{scope:i}:null),{expires_in:o})}finally{await eo.releaseLock("auth0.lock.getTokenSilently"),window.removeEventListener("pagehide",this._releaseLockOnPageHide)}}}async getTokenWithPopup(e={},t={}){var r;let n=Object.assign(Object.assign({},e),{authorizationParams:Object.assign(Object.assign(Object.assign({},this.options.authorizationParams),e.authorizationParams),{scope:L(this.scope,null===(r=e.authorizationParams)||void 0===r?void 0:r.scope)})});return t=Object.assign(Object.assign({},f),t),await this.loginWithPopup(n,t),(await this.cacheManager.get(new A({scope:n.authorizationParams.scope,audience:n.authorizationParams.audience||"default",clientId:this.options.clientId}))).access_token}async isAuthenticated(){return!!await this.getUser()}_buildLogoutUrl(e){null!==e.clientId?e.clientId=e.clientId||this.options.clientId:delete e.clientId;let t=e.logoutParams||{},{federated:r}=t,n=c(t,["federated"]);return this._url(`/v2/logout?${_(Object.assign({clientId:e.clientId},n))}`)+(r?"&federated":"")}async logout(e={}){let t=ei(e),{openUrl:r}=t,n=c(t,["openUrl"]);null===e.clientId?await this.cacheManager.clear():await this.cacheManager.clear(e.clientId||this.options.clientId),this.cookieStorage.remove(this.orgHintCookieName,{cookieDomain:this.options.cookieDomain}),this.cookieStorage.remove(this.isAuthenticatedCookieName,{cookieDomain:this.options.cookieDomain}),this.userCache.remove("@@user@@");let i=this._buildLogoutUrl(n);r?await r(i):!1!==r&&window.location.assign(i)}async _getTokenFromIFrame(e){let t=Object.assign(Object.assign({},e.authorizationParams),{prompt:"none"}),r=this.cookieStorage.get(this.orgHintCookieName);r&&!t.organization&&(t.organization=r);let{url:n,state:i,nonce:o,code_verifier:s,redirect_uri:a,scope:c,audience:u}=await this._prepareAuthorizeUrl(t,{response_mode:"web_message"},window.location.origin);try{if(window.crossOriginIsolated)throw new v("login_required","The application is running in a Cross-Origin Isolated context, silently retrieving a token without refresh token is not possible.");let r=e.timeoutInSeconds||this.options.authorizeTimeoutInSeconds,l=await ((e,t,r=60)=>new Promise((n,i)=>{let o;let s=window.document.createElement("iframe");s.setAttribute("width","0"),s.setAttribute("height","0"),s.style.display="none";let a=()=>{window.document.body.contains(s)&&(window.document.body.removeChild(s),window.removeEventListener("message",o,!1))},c=setTimeout(()=>{i(new b),a()},1e3*r);o=function(e){if(e.origin!=t||!e.data||"authorization_response"!==e.data.type)return;let r=e.source;r&&r.close(),e.data.response.error?i(v.fromPayload(e.data.response)):n(e.data.response),clearTimeout(c),window.removeEventListener("message",o,!1),setTimeout(a,2e3)},window.addEventListener("message",o,!1),window.document.body.appendChild(s),s.setAttribute("src",e)}))(n,this.domainUrl,r);if(i!==l.state)throw new v("state_mismatch","Invalid state");let d=await this._requestToken(Object.assign(Object.assign({},e.authorizationParams),{code_verifier:s,code:l.code,grant_type:"authorization_code",redirect_uri:a,timeout:e.authorizationParams.timeout||this.httpTimeoutMs}),{nonceIn:o,organization:t.organization});return Object.assign(Object.assign({},d),{scope:c,oauthTokenScope:d.scope,audience:u})}catch(e){throw"login_required"===e.error&&this.logout({openUrl:!1}),e}}async _getTokenUsingRefreshToken(e){let t=await this.cacheManager.get(new A({scope:e.authorizationParams.scope,audience:e.authorizationParams.audience||"default",clientId:this.options.clientId}));if(!(t&&t.refresh_token||this.worker)){if(this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw new C(e.authorizationParams.audience||"default",e.authorizationParams.scope)}let r=e.authorizationParams.redirect_uri||this.options.authorizationParams.redirect_uri||window.location.origin,n="number"==typeof e.timeoutInSeconds?1e3*e.timeoutInSeconds:null;try{let i=await this._requestToken(Object.assign(Object.assign(Object.assign({},e.authorizationParams),{grant_type:"refresh_token",refresh_token:t&&t.refresh_token,redirect_uri:r}),n&&{timeout:n}));return Object.assign(Object.assign({},i),{scope:e.authorizationParams.scope,oauthTokenScope:i.scope,audience:e.authorizationParams.audience||"default"})}catch(t){if((t.message.indexOf("Missing Refresh Token")>-1||t.message&&t.message.indexOf("invalid refresh token")>-1)&&this.options.useRefreshTokensFallback)return await this._getTokenFromIFrame(e);throw t}}async _saveEntryInCache(e){let{id_token:t,decodedToken:r}=e,n=c(e,["id_token","decodedToken"]);this.userCache.set("@@user@@",{id_token:t,decodedToken:r}),await this.cacheManager.setIdToken(this.options.clientId,e.id_token,e.decodedToken),await this.cacheManager.set(n)}async _getIdTokenFromCache(){let e=this.options.authorizationParams.audience||"default",t=await this.cacheManager.getIdToken(new A({clientId:this.options.clientId,audience:e,scope:this.scope})),r=this.userCache.get("@@user@@");return t&&t.id_token===(null==r?void 0:r.id_token)?r:(this.userCache.set("@@user@@",t),t)}async _getEntryFromCache({scope:e,audience:t,clientId:r}){let n=await this.cacheManager.get(new A({scope:e,audience:t,clientId:r}),60);if(n&&n.access_token){let{access_token:e,oauthTokenScope:t,expires_in:r}=n,i=await this._getIdTokenFromCache();return i&&Object.assign(Object.assign({id_token:i.id_token,access_token:e},t?{scope:t}:null),{expires_in:r})}}async _requestToken(e,t){let{nonceIn:r,organization:n}=t||{},i=await D(Object.assign({baseUrl:this.domainUrl,client_id:this.options.clientId,auth0Client:this.options.auth0Client,useFormData:this.options.useFormData,timeout:this.httpTimeoutMs},e),this.worker),o=await this._verifyIdToken(i.id_token,r,n);return await this._saveEntryInCache(Object.assign(Object.assign(Object.assign(Object.assign({},i),{decodedToken:o,scope:e.scope,audience:e.audience||"default"}),i.scope?{oauthTokenScope:i.scope}:null),{client_id:this.options.clientId})),this.cookieStorage.save(this.isAuthenticatedCookieName,!0,{daysUntilExpire:this.sessionCheckExpiryDays,cookieDomain:this.options.cookieDomain}),this._processOrgHint(n||o.claims.org_id),Object.assign(Object.assign({},i),{decodedToken:o})}}var ea={isAuthenticated:!1,isLoading:!0},ec=function(){throw Error("You forgot to wrap your component in <Auth0Provider>.")},eu=o(o({},ea),{buildAuthorizeUrl:ec,buildLogoutUrl:ec,getAccessTokenSilently:ec,getAccessTokenWithPopup:ec,getIdTokenClaims:ec,loginWithRedirect:ec,loginWithPopup:ec,logout:ec,handleRedirectCallback:ec}),el=(0,n.createContext)(eu),ed=function(e){function t(r,n){var i=e.call(this,n||r)||this;return i.error=r,i.error_description=n,Object.setPrototypeOf(i,t.prototype),i}return!function(e,t){if("function"!=typeof t&&null!==t)throw TypeError("Class extends value "+String(t)+" is not a constructor or null");function r(){this.constructor=e}i(e,t),e.prototype=null===t?Object.create(t):(r.prototype=t.prototype,new r)}(t,e),t}(Error),eh=/[?&]code=[^&]+/,ep=/[?&]state=[^&]+/,ef=/[?&]error=[^&]+/,em=function(e){return function(t){return t instanceof Error?t:null!==t&&"object"==typeof t&&"error"in t&&"string"==typeof t.error?"error_description"in t&&"string"==typeof t.error_description?new ed(t.error,t.error_description):new ed(t.error):Error(e)}},ey=em("Login failed"),ev=em("Get access token failed"),eg=function(e){var t;(null==e?void 0:e.redirectUri)&&(console.warn("Using `redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `redirectUri` will be no longer supported in a future version"),e.authorizationParams=e.authorizationParams||{},e.authorizationParams.redirect_uri=e.redirectUri,delete e.redirectUri),(null===(t=null==e?void 0:e.authorizationParams)||void 0===t?void 0:t.redirectUri)&&(console.warn("Using `authorizationParams.redirectUri` has been deprecated, please use `authorizationParams.redirect_uri` instead as `authorizationParams.redirectUri` will be removed in a future version"),e.authorizationParams.redirect_uri=e.authorizationParams.redirectUri,delete e.authorizationParams.redirectUri)},eb=function(e,t){switch(t.type){case"LOGIN_POPUP_STARTED":return o(o({},e),{isLoading:!0});case"LOGIN_POPUP_COMPLETE":case"INITIALISED":return o(o({},e),{isAuthenticated:!!t.user,user:t.user,isLoading:!1,error:void 0});case"HANDLE_REDIRECT_COMPLETE":case"GET_ACCESS_TOKEN_COMPLETE":if(e.user===t.user)return e;return o(o({},e),{isAuthenticated:!!t.user,user:t.user});case"LOGOUT":return o(o({},e),{isAuthenticated:!1,user:void 0});case"ERROR":return o(o({},e),{isLoading:!1,error:t.error})}},ew=function(e){window.history.replaceState({},document.title,(null==e?void 0:e.returnTo)||window.location.pathname)},ex=function(e){var t=e.children,r=e.skipRedirectCallback,i=e.onRedirectCallback,c=void 0===i?ew:i,u=e.context,l=void 0===u?el:u,d=function(e,t){var r={};for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&0>t.indexOf(n)&&(r[n]=e[n]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var i=0,n=Object.getOwnPropertySymbols(e);i<n.length;i++)0>t.indexOf(n[i])&&Object.prototype.propertyIsEnumerable.call(e,n[i])&&(r[n[i]]=e[n[i]]);return r}(e,["children","skipRedirectCallback","onRedirectCallback","context"]),h=(0,n.useState)(function(){return new es((eg(d),o(o({},d),{auth0Client:{name:"auth0-react",version:"2.3.0"}})))})[0],p=(0,n.useReducer)(eb,ea),f=p[0],m=p[1],y=(0,n.useRef)(!1),v=(0,n.useCallback)(function(e){return m({type:"ERROR",error:e}),e},[]);(0,n.useEffect)(function(){y.current||(y.current=!0,s(void 0,void 0,void 0,function(){var e,t;return a(this,function(n){switch(n.label){case 0:var i;if(n.trys.push([0,7,,8]),e=void 0,void 0===i&&(i=window.location.search),!((eh.test(i)||ef.test(i))&&ep.test(i))||r)return[3,3];return[4,h.handleRedirectCallback()];case 1:return t=n.sent().appState,[4,h.getUser()];case 2:return e=n.sent(),c(t,e),[3,6];case 3:return[4,h.checkSession()];case 4:return n.sent(),[4,h.getUser()];case 5:e=n.sent(),n.label=6;case 6:return m({type:"INITIALISED",user:e}),[3,8];case 7:return v(ey(n.sent())),[3,8];case 8:return[2]}})}))},[h,c,r,v]);var g=(0,n.useCallback)(function(e){return eg(e),h.loginWithRedirect(e)},[h]),b=(0,n.useCallback)(function(e,t){return s(void 0,void 0,void 0,function(){return a(this,function(r){switch(r.label){case 0:m({type:"LOGIN_POPUP_STARTED"}),r.label=1;case 1:return r.trys.push([1,3,,4]),[4,h.loginWithPopup(e,t)];case 2:return r.sent(),[3,4];case 3:return v(ey(r.sent())),[2];case 4:return[4,h.getUser()];case 5:return m({type:"LOGIN_POPUP_COMPLETE",user:r.sent()}),[2]}})})},[h]),w=(0,n.useCallback)(function(e){return void 0===e&&(e={}),s(void 0,void 0,void 0,function(){return a(this,function(t){switch(t.label){case 0:return[4,h.logout(e)];case 1:return t.sent(),(e.openUrl||!1===e.openUrl)&&m({type:"LOGOUT"}),[2]}})})},[h]),x=(0,n.useCallback)(function(e){return s(void 0,void 0,void 0,function(){var t,r,n;return a(this,function(i){switch(i.label){case 0:return i.trys.push([0,2,3,5]),[4,h.getTokenSilently(e)];case 1:return t=i.sent(),[3,5];case 2:throw ev(i.sent());case 3:return r=m,n={type:"GET_ACCESS_TOKEN_COMPLETE"},[4,h.getUser()];case 4:return r.apply(void 0,[(n.user=i.sent(),n)]),[7];case 5:return[2,t]}})})},[h]),k=(0,n.useCallback)(function(e,t){return s(void 0,void 0,void 0,function(){var r,n,i;return a(this,function(o){switch(o.label){case 0:return o.trys.push([0,2,3,5]),[4,h.getTokenWithPopup(e,t)];case 1:return r=o.sent(),[3,5];case 2:throw ev(o.sent());case 3:return n=m,i={type:"GET_ACCESS_TOKEN_COMPLETE"},[4,h.getUser()];case 4:return n.apply(void 0,[(i.user=o.sent(),i)]),[7];case 5:return[2,r]}})})},[h]),C=(0,n.useCallback)(function(){return h.getIdTokenClaims()},[h]),E=(0,n.useCallback)(function(e){return s(void 0,void 0,void 0,function(){var t,r;return a(this,function(n){switch(n.label){case 0:return n.trys.push([0,2,3,5]),[4,h.handleRedirectCallback(e)];case 1:return[2,n.sent()];case 2:throw ev(n.sent());case 3:return t=m,r={type:"HANDLE_REDIRECT_COMPLETE"},[4,h.getUser()];case 4:return t.apply(void 0,[(r.user=n.sent(),r)]),[7];case 5:return[2]}})})},[h]),T=(0,n.useMemo)(function(){return o(o({},f),{getAccessTokenSilently:x,getAccessTokenWithPopup:k,getIdTokenClaims:C,loginWithRedirect:g,loginWithPopup:b,logout:w,handleRedirectCallback:E})},[f,x,k,C,g,b,w,E]);return n.createElement(l.Provider,{value:T},t)},ek=function(e){return void 0===e&&(e=el),(0,n.useContext)(e)}},3454:function(e,t,r){"use strict";var n,i;e.exports=(null==(n=r.g.process)?void 0:n.env)&&"object"==typeof(null==(i=r.g.process)?void 0:i.env)?r.g.process:r(7663)},6840:function(e,t,r){(window.__NEXT_P=window.__NEXT_P||[]).push(["/_app",function(){return r(5926)}])},6944:function(e,t,r){"use strict";r.d(t,{c:function(){return p},r:function(){return f}});var n=r(5893),i=r(7294),o=r(5079),s=r(202),a=r(777),c=r(8029),u=r(2917),l=r(873),d=r(9527);let h=(0,i.createContext)(void 0);function p(e){let{children:t}=e,{toast:r}=(0,o.pm)(),{trackEvent:i}=(0,d.z)(),{favorites:p,addFavorite:f,deleteFavorite:m,isAuthenticated:y}=function(){let e=(0,s.NL)(),{authFetch:t}=(0,u.K)(),{isAuthenticated:r}=(0,l.a)(),{data:n,isLoading:i,error:o,refetch:d}=(0,a.a)({queryKey:["/api/favorites"],queryFn:async()=>await t("/api/favorites"),enabled:r,refetchOnWindowFocus:!1,staleTime:3e4,gcTime:3e5}),h=(0,c.D)({mutationFn:async e=>{if(!r)throw Error("Authentication required");try{let r={...e,price:e.price?Number(e.price):void 0,relatedTlds:e.relatedTlds?e.relatedTlds.map(e=>({...e,price:e.price?Number(e.price):void 0})):void 0};return await t("/api/favorites",{method:"POST",body:JSON.stringify(r)})}catch(e){throw e}},onMutate:async t=>{await e.cancelQueries({queryKey:["/api/favorites"]});let r=e.getQueryData(["/api/favorites"]);return r&&e.setQueryData(["/api/favorites"],{favorites:[...r.favorites,{id:Date.now(),domainName:t.domainName,tld:t.tld,fullDomain:t.fullDomain,available:t.available,price:t.price,createdAt:new Date().toISOString(),relatedTlds:t.relatedTlds}]}),{previousFavorites:r}},onError:(t,r,n)=>{(null==n?void 0:n.previousFavorites)&&e.setQueryData(["/api/favorites"],n.previousFavorites)},onSettled:()=>{e.invalidateQueries({queryKey:["/api/favorites"]})}}),p=(0,c.D)({mutationFn:async e=>await t("/api/favorites/".concat(e),{method:"DELETE"}),onMutate:async t=>{await e.cancelQueries({queryKey:["/api/favorites"]});let r=e.getQueryData(["/api/favorites"]);return r&&e.setQueryData(["/api/favorites"],{favorites:r.favorites.filter(e=>e.id!==t)}),{previousFavorites:r}},onError:(t,r,n)=>{(null==n?void 0:n.previousFavorites)&&e.setQueryData(["/api/favorites"],n.previousFavorites)},onSettled:()=>{e.invalidateQueries({queryKey:["/api/favorites"]})}}),f=async(e,t)=>{if(!r)return{success:!1,requiresAuth:!0};try{let r=e=>{if(null!=e)return"string"==typeof e?Number(e):e};return await h.mutateAsync({domainName:e.name,tld:e.tld,fullDomain:e.fullDomain,available:e.available,price:r(e.price),relatedTlds:null==t?void 0:t.map(e=>({tld:e.tld,fullDomain:e.fullDomain,available:e.available,price:r(e.price)}))}),{success:!0}}catch(e){return{success:!1,error:e}}},m=async e=>{if(!r)return{success:!1,requiresAuth:!0};try{return await p.mutateAsync(e),{success:!0}}catch(e){return{success:!1,error:e}}};return{favorites:(null==n?void 0:n.favorites)||[],isLoading:i,error:o,refetch:d,addFavorite:f,deleteFavorite:m,isAddingFavorite:h.isPending,isDeletingFavorite:p.isPending,isAuthenticated:r}}(),v=async(e,t)=>{try{if(!y){r({title:"Sign in Required",description:"You need to create an account or sign in to save favorites.",variant:"default"});return}let n=await f(e,t);if(n.success)i("favorite_added",{domain_name:e.name,tld:e.tld,full_domain:e.fullDomain,available:e.available}),r({title:"Domain Favorited",description:"".concat(e.fullDomain," has been added to your favorites."),variant:"default"});else if(n.requiresAuth)r({title:"Sign in Required",description:"You need to create an account or sign in to save favorites.",variant:"default"});else throw Error("Failed to add favorite")}catch(e){r({title:"Error",description:"There was a problem adding this domain to your favorites.",variant:"destructive"})}},g=async e=>{try{if(!y){r({title:"Sign in Required",description:"You need to create an account or sign in to manage favorites.",variant:"default"});return}let t=p.find(t=>{var r;return t.fullDomain===e||(null===(r=t.relatedTlds)||void 0===r?void 0:r.some(t=>t.fullDomain===e))});if(!t)return;let n=await m(t.id);if(n.success)i("favorite_removed",{full_domain:e}),r({title:"Domain Removed",description:"Domain has been removed from your favorites.",variant:"default"});else if(n.requiresAuth)r({title:"Sign in Required",description:"You need to create an account or sign in to manage favorites.",variant:"default"});else throw Error("Failed to remove favorite")}catch(e){r({title:"Error",description:"There was a problem removing this domain from your favorites.",variant:"destructive"})}},b=e=>p.some(t=>{var r;return t.fullDomain===e||(null===(r=t.relatedTlds)||void 0===r?void 0:r.some(t=>t.fullDomain===e))});return(0,n.jsx)(h.Provider,{value:{favorites:p,addFavorite:v,removeFavorite:g,toggleFavorite:(e,t)=>{b(e.fullDomain)?g(e.fullDomain):v(e,t)},isFavorite:b},children:t})}function f(){let e=(0,i.useContext)(h);if(void 0===e)throw Error("useFavorites must be used within a FavoritesProvider");return e}},9527:function(e,t,r){"use strict";r.d(t,{z:function(){return i}});var n=r(7294);function i(){return{trackEvent:(0,n.useCallback)((e,t)=>{window.gtag&&window.gtag("event",e,t)},[])}}},2917:function(e,t,r){"use strict";r.d(t,{K:function(){return s}});var n=r(873),i=r(8486),o=r(7294);function s(){let{getToken:e}=(0,n.a)();return{authFetch:(0,o.useCallback)(async function(t){let r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};try{let n=await e(),o=await (0,i.SC)(t,r,n);return(0,i.Bc)(o)}catch(e){throw e}},[e])}}},873:function(e,t,r){"use strict";r.d(t,{a:function(){return s}});var n=r(8100),i=r(7294),o=r(3454);function s(){let{isAuthenticated:e,isLoading:t,user:r,loginWithRedirect:s,logout:a,getAccessTokenSilently:c,getAccessTokenWithPopup:u}=(0,n.D3)(),l=(0,i.useCallback)(()=>{s()},[s]),d=(0,i.useCallback)(()=>{a({logoutParams:{returnTo:window.location.origin}})},[a]),h=(0,i.useCallback)(async()=>{try{var t;if(!e)return null;let r=(null===(t=window.ENV)||void 0===t?void 0:t.AUTH0_AUDIENCE)||o.env.NEXT_PUBLIC_AUTH0_AUDIENCE||"https://api.domainmate.net";try{let e=await c({authorizationParams:{audience:r},detailedResponse:!1});if(e&&"object"==typeof e&&"access_token"in e)return e.access_token;return e}catch(e){return await u({authorizationParams:{audience:r}})}}catch(e){return null}},[c,u,e]);return{isAuthenticated:e,isLoading:t,user:r,login:l,logout:d,getToken:h}}},5079:function(e,t,r){"use strict";r.d(t,{pm:function(){return h}});var n=r(7294);let i=0,o=new Map,s=e=>{if(o.has(e))return;let t=setTimeout(()=>{o.delete(e),l({type:"REMOVE_TOAST",toastId:e})},1e6);o.set(e,t)},a=(e,t)=>{switch(t.type){case"ADD_TOAST":return{...e,toasts:[t.toast,...e.toasts].slice(0,1)};case"UPDATE_TOAST":return{...e,toasts:e.toasts.map(e=>e.id===t.toast.id?{...e,...t.toast}:e)};case"DISMISS_TOAST":{let{toastId:r}=t;return r?s(r):e.toasts.forEach(e=>{s(e.id)}),{...e,toasts:e.toasts.map(e=>e.id===r||void 0===r?{...e,open:!1}:e)}}case"REMOVE_TOAST":if(void 0===t.toastId)return{...e,toasts:[]};return{...e,toasts:e.toasts.filter(e=>e.id!==t.toastId)}}},c=[],u={toasts:[]};function l(e){u=a(u,e),c.forEach(e=>{e(u)})}function d(e){let{...t}=e,r=(i=(i+1)%Number.MAX_SAFE_INTEGER).toString(),n=()=>l({type:"DISMISS_TOAST",toastId:r});return l({type:"ADD_TOAST",toast:{...t,id:r,open:!0,onOpenChange:e=>{e||n()}}}),{id:r,dismiss:n,update:e=>l({type:"UPDATE_TOAST",toast:{...e,id:r}})}}function h(){let[e,t]=n.useState(u);return n.useEffect(()=>(c.push(t),()=>{let e=c.indexOf(t);e>-1&&c.splice(e,1)}),[e]),{...e,toast:d,dismiss:e=>l({type:"DISMISS_TOAST",toastId:e})}}},8486:function(e,t,r){"use strict";r.d(t,{Bc:function(){return s},SC:function(){return o}});let n=()=>({"X-App-Request":"DomainMate-App","Content-Type":"application/json"}),i=async e=>{let t=n();return e&&(t.Authorization="Bearer ".concat(e)),t},o=async function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=arguments.length>2?arguments[2]:void 0,n={...await i(r),...t.headers||{}};try{return await fetch(e,{...t,headers:n,credentials:"include",cache:"no-store"})}catch(e){throw e}},s=async e=>{if(!e.ok){let t=await e.text();throw Error("API Error ".concat(e.status,": ").concat(t))}return e.json()}},6916:function(e,t,r){"use strict";r.d(t,{Nv:function(){return g},Eh:function(){return b}});var n=r(4139),i=r(6888),o=r(7037),s=r(7506),a=class extends s.l{constructor(e={}){super(),this.config=e,this.#e=new Map}#e;build(e,t,r){let o=t.queryKey,s=t.queryHash??(0,n.Rm)(o,t),a=this.get(s);return a||(a=new i.A({cache:this,queryKey:o,queryHash:s,options:e.defaultQueryOptions(t),state:r,defaultOptions:e.getQueryDefaults(o)}),this.add(a)),a}add(e){this.#e.has(e.queryHash)||(this.#e.set(e.queryHash,e),this.notify({type:"added",query:e}))}remove(e){let t=this.#e.get(e.queryHash);t&&(e.destroy(),t===e&&this.#e.delete(e.queryHash),this.notify({type:"removed",query:e}))}clear(){o.V.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}get(e){return this.#e.get(e)}getAll(){return[...this.#e.values()]}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n._x)(t,e))}findAll(e={}){let t=this.getAll();return Object.keys(e).length>0?t.filter(t=>(0,n._x)(e,t)):t}notify(e){o.V.batch(()=>{this.listeners.forEach(t=>{t(e)})})}onFocus(){o.V.batch(()=>{this.getAll().forEach(e=>{e.onFocus()})})}onOnline(){o.V.batch(()=>{this.getAll().forEach(e=>{e.onOnline()})})}},c=r(9289),u=class extends s.l{constructor(e={}){super(),this.config=e,this.#t=new Map,this.#r=Date.now()}#t;#r;build(e,t,r){let n=new c.m({mutationCache:this,mutationId:++this.#r,options:e.defaultMutationOptions(t),state:r});return this.add(n),n}add(e){let t=l(e),r=this.#t.get(t)??[];r.push(e),this.#t.set(t,r),this.notify({type:"added",mutation:e})}remove(e){let t=l(e);if(this.#t.has(t)){let r=this.#t.get(t)?.filter(t=>t!==e);r&&(0===r.length?this.#t.delete(t):this.#t.set(t,r))}this.notify({type:"removed",mutation:e})}canRun(e){let t=this.#t.get(l(e))?.find(e=>"pending"===e.state.status);return!t||t===e}runNext(e){let t=this.#t.get(l(e))?.find(t=>t!==e&&t.state.isPaused);return t?.continue()??Promise.resolve()}clear(){o.V.batch(()=>{this.getAll().forEach(e=>{this.remove(e)})})}getAll(){return[...this.#t.values()].flat()}find(e){let t={exact:!0,...e};return this.getAll().find(e=>(0,n.X7)(t,e))}findAll(e={}){return this.getAll().filter(t=>(0,n.X7)(e,t))}notify(e){o.V.batch(()=>{this.listeners.forEach(t=>{t(e)})})}resumePausedMutations(){let e=this.getAll().filter(e=>e.state.isPaused);return o.V.batch(()=>Promise.all(e.map(e=>e.continue().catch(n.ZT))))}};function l(e){return e.options.scope?.id??String(e.mutationId)}var d=r(6474),h=r(4304);function p(e){return{onFetch:(t,r)=>{let i=t.options,o=t.fetchOptions?.meta?.fetchMore?.direction,s=t.state.data?.pages||[],a=t.state.data?.pageParams||[],c={pages:[],pageParams:[]},u=0,l=async()=>{let r=!1,l=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(t.signal.aborted?r=!0:t.signal.addEventListener("abort",()=>{r=!0}),t.signal)})},d=(0,n.cG)(t.options,t.fetchOptions),h=async(e,i,o)=>{if(r)return Promise.reject();if(null==i&&e.pages.length)return Promise.resolve(e);let s={queryKey:t.queryKey,pageParam:i,direction:o?"backward":"forward",meta:t.options.meta};l(s);let a=await d(s),{maxPages:c}=t.options,u=o?n.Ht:n.VX;return{pages:u(e.pages,a,c),pageParams:u(e.pageParams,i,c)}};if(o&&s.length){let e="backward"===o,t={pages:s,pageParams:a},r=(e?function(e,{pages:t,pageParams:r}){return t.length>0?e.getPreviousPageParam?.(t[0],t,r[0],r):void 0}:f)(i,t);c=await h(t,r,e)}else{let t=e??s.length;do{let e=0===u?a[0]??i.initialPageParam:f(i,c);if(u>0&&null==e)break;c=await h(c,e),u++}while(u<t)}return c};t.options.persister?t.fetchFn=()=>t.options.persister?.(l,{queryKey:t.queryKey,meta:t.options.meta,signal:t.signal},r):t.fetchFn=l}}}function f(e,{pages:t,pageParams:r}){let n=t.length-1;return t.length>0?e.getNextPageParam(t[n],t,r[n],r):void 0}var m=class{#n;#i;#o;#s;#a;#c;#u;#l;constructor(e={}){this.#n=e.queryCache||new a,this.#i=e.mutationCache||new u,this.#o=e.defaultOptions||{},this.#s=new Map,this.#a=new Map,this.#c=0}mount(){this.#c++,1===this.#c&&(this.#u=d.j.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#n.onFocus())}),this.#l=h.N.subscribe(async e=>{e&&(await this.resumePausedMutations(),this.#n.onOnline())}))}unmount(){this.#c--,0===this.#c&&(this.#u?.(),this.#u=void 0,this.#l?.(),this.#l=void 0)}isFetching(e){return this.#n.findAll({...e,fetchStatus:"fetching"}).length}isMutating(e){return this.#i.findAll({...e,status:"pending"}).length}getQueryData(e){let t=this.defaultQueryOptions({queryKey:e});return this.#n.get(t.queryHash)?.state.data}ensureQueryData(e){let t=this.getQueryData(e.queryKey);if(void 0===t)return this.fetchQuery(e);{let r=this.defaultQueryOptions(e),i=this.#n.build(this,r);return e.revalidateIfStale&&i.isStaleByTime((0,n.KC)(r.staleTime,i))&&this.prefetchQuery(r),Promise.resolve(t)}}getQueriesData(e){return this.#n.findAll(e).map(({queryKey:e,state:t})=>[e,t.data])}setQueryData(e,t,r){let i=this.defaultQueryOptions({queryKey:e}),o=this.#n.get(i.queryHash),s=o?.state.data,a=(0,n.SE)(t,s);if(void 0!==a)return this.#n.build(this,i).setData(a,{...r,manual:!0})}setQueriesData(e,t,r){return o.V.batch(()=>this.#n.findAll(e).map(({queryKey:e})=>[e,this.setQueryData(e,t,r)]))}getQueryState(e){let t=this.defaultQueryOptions({queryKey:e});return this.#n.get(t.queryHash)?.state}removeQueries(e){let t=this.#n;o.V.batch(()=>{t.findAll(e).forEach(e=>{t.remove(e)})})}resetQueries(e,t){let r=this.#n,n={type:"active",...e};return o.V.batch(()=>(r.findAll(e).forEach(e=>{e.reset()}),this.refetchQueries(n,t)))}cancelQueries(e={},t={}){let r={revert:!0,...t};return Promise.all(o.V.batch(()=>this.#n.findAll(e).map(e=>e.cancel(r)))).then(n.ZT).catch(n.ZT)}invalidateQueries(e={},t={}){return o.V.batch(()=>{if(this.#n.findAll(e).forEach(e=>{e.invalidate()}),"none"===e.refetchType)return Promise.resolve();let r={...e,type:e.refetchType??e.type??"active"};return this.refetchQueries(r,t)})}refetchQueries(e={},t){let r={...t,cancelRefetch:t?.cancelRefetch??!0};return Promise.all(o.V.batch(()=>this.#n.findAll(e).filter(e=>!e.isDisabled()).map(e=>{let t=e.fetch(void 0,r);return r.throwOnError||(t=t.catch(n.ZT)),"paused"===e.state.fetchStatus?Promise.resolve():t}))).then(n.ZT)}fetchQuery(e){let t=this.defaultQueryOptions(e);void 0===t.retry&&(t.retry=!1);let r=this.#n.build(this,t);return r.isStaleByTime((0,n.KC)(t.staleTime,r))?r.fetch(t):Promise.resolve(r.state.data)}prefetchQuery(e){return this.fetchQuery(e).then(n.ZT).catch(n.ZT)}fetchInfiniteQuery(e){return e.behavior=p(e.pages),this.fetchQuery(e)}prefetchInfiniteQuery(e){return this.fetchInfiniteQuery(e).then(n.ZT).catch(n.ZT)}ensureInfiniteQueryData(e){return e.behavior=p(e.pages),this.ensureQueryData(e)}resumePausedMutations(){return h.N.isOnline()?this.#i.resumePausedMutations():Promise.resolve()}getQueryCache(){return this.#n}getMutationCache(){return this.#i}getDefaultOptions(){return this.#o}setDefaultOptions(e){this.#o=e}setQueryDefaults(e,t){this.#s.set((0,n.Ym)(e),{queryKey:e,defaultOptions:t})}getQueryDefaults(e){let t=[...this.#s.values()],r={};return t.forEach(t=>{(0,n.to)(e,t.queryKey)&&(r={...r,...t.defaultOptions})}),r}setMutationDefaults(e,t){this.#a.set((0,n.Ym)(e),{mutationKey:e,defaultOptions:t})}getMutationDefaults(e){let t=[...this.#a.values()],r={};return t.forEach(t=>{(0,n.to)(e,t.mutationKey)&&(r={...r,...t.defaultOptions})}),r}defaultQueryOptions(e){if(e._defaulted)return e;let t={...this.#o.queries,...this.getQueryDefaults(e.queryKey),...e,_defaulted:!0};return t.queryHash||(t.queryHash=(0,n.Rm)(t.queryKey,t)),void 0===t.refetchOnReconnect&&(t.refetchOnReconnect="always"!==t.networkMode),void 0===t.throwOnError&&(t.throwOnError=!!t.suspense),!t.networkMode&&t.persister&&(t.networkMode="offlineFirst"),!0!==t.enabled&&t.queryFn===n.CN&&(t.enabled=!1),t}defaultMutationOptions(e){return e?._defaulted?e:{...this.#o.mutations,...e?.mutationKey&&this.getMutationDefaults(e.mutationKey),...e,_defaulted:!0}}clear(){this.#n.clear(),this.#i.clear()}},y=r(8486);async function v(e){if(!e.ok){let t=await e.text()||e.statusText;throw Error("".concat(e.status,": ").concat(t))}}async function g(e,t,r){let n={method:e,body:r?JSON.stringify(r):void 0};r&&(n.headers={"Content-Type":"application/json"});let i=await (0,y.SC)(t,n);return await v(i),i}let b=new m({defaultOptions:{queries:{queryFn:(e=>{let{on401:t}=e;return async e=>{var r;let n=e.queryKey,i=n[0];if(null==e?void 0:null===(r=e.meta)||void 0===r?void 0:r.queryParams){let t=new URLSearchParams;Object.entries(e.meta.queryParams).forEach(e=>{let[r,n]=e;void 0!==n&&""!==n&&t.append(r,n)});let r=t.toString();r&&(i="".concat(i,"?").concat(r))}else if(n.length>1&&"/api/domains/search"===i&&"string"==typeof n[1]){let e=n[1],t=n[2];i="".concat(i,"?term=").concat(encodeURIComponent(e)),t&&Array.isArray(t)&&t.length>0&&(i+="&tlds=".concat(t.join(",")))}let o=await (0,y.SC)(i);return"returnNull"===t&&401===o.status?null:(await v(o),await o.json())}})({on401:"throw"}),refetchInterval:!1,refetchOnWindowFocus:!1,staleTime:1/0,retry:!1},mutations:{retry:!1}}})},7009:function(e,t,r){"use strict";r.d(t,{cn:function(){return Y}});let n=e=>{let t=a(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:n}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),i(r,t)||s(e)},getConflictingClassGroupIds:(e,t)=>{let i=r[e]||[];return t&&n[e]?[...i,...n[e]]:i}}},i=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],n=t.nextPart.get(r),o=n?i(e.slice(1),n):void 0;if(o)return o;if(0===t.validators.length)return;let s=e.join("-");return t.validators.find(({validator:e})=>e(s))?.classGroupId},o=/^\[(.+)\]$/,s=e=>{if(o.test(e)){let t=o.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},a=e=>{let{theme:t,prefix:r}=e,n={nextPart:new Map,validators:[]};return d(Object.entries(e.classGroups),r).forEach(([e,r])=>{c(r,n,e,t)}),n},c=(e,t,r,n)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:u(t,e)).classGroupId=r;return}if("function"==typeof e){if(l(e)){c(e(n),t,r,n);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,i])=>{c(i,u(t,e),r,n)})})},u=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},l=e=>e.isThemeGetter,d=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,h=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,n=new Map,i=(i,o)=>{r.set(i,o),++t>e&&(t=0,n=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=n.get(e))?(i(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):i(e,t)}}},p=e=>{let{separator:t,experimentalParseClassName:r}=e,n=1===t.length,i=t[0],o=t.length,s=e=>{let r;let s=[],a=0,c=0;for(let u=0;u<e.length;u++){let l=e[u];if(0===a){if(l===i&&(n||e.slice(u,u+o)===t)){s.push(e.slice(c,u)),c=u+o;continue}if("/"===l){r=u;continue}}"["===l?a++:"]"===l&&a--}let u=0===s.length?e:e.substring(c),l=u.startsWith("!"),d=l?u.substring(1):u;return{modifiers:s,hasImportantModifier:l,baseClassName:d,maybePostfixModifierPosition:r&&r>c?r-c:void 0}};return r?e=>r({className:e,parseClassName:s}):s},f=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},m=e=>({cache:h(e.cacheSize),parseClassName:p(e),...n(e)}),y=/\s+/,v=(e,t)=>{let{parseClassName:r,getClassGroupId:n,getConflictingClassGroupIds:i}=t,o=[],s=e.trim().split(y),a="";for(let e=s.length-1;e>=0;e-=1){let t=s[e],{modifiers:c,hasImportantModifier:u,baseClassName:l,maybePostfixModifierPosition:d}=r(t),h=!!d,p=n(h?l.substring(0,d):l);if(!p){if(!h||!(p=n(l))){a=t+(a.length>0?" "+a:a);continue}h=!1}let m=f(c).join(":"),y=u?m+"!":m,v=y+p;if(o.includes(v))continue;o.push(v);let g=i(p,h);for(let e=0;e<g.length;++e){let t=g[e];o.push(y+t)}a=t+(a.length>0?" "+a:a)}return a};function g(){let e,t,r=0,n="";for(;r<arguments.length;)(e=arguments[r++])&&(t=b(e))&&(n&&(n+=" "),n+=t);return n}let b=e=>{let t;if("string"==typeof e)return e;let r="";for(let n=0;n<e.length;n++)e[n]&&(t=b(e[n]))&&(r&&(r+=" "),r+=t);return r},w=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},x=/^\[(?:([a-z-]+):)?(.+)\]$/i,k=/^\d+\/\d+$/,C=new Set(["px","full","screen"]),E=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,T=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,O=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,S=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,P=e=>R(e)||C.has(e)||k.test(e),I=e=>Z(e,"length",Q),R=e=>!!e&&!Number.isNaN(Number(e)),j=e=>Z(e,"number",R),N=e=>!!e&&Number.isInteger(Number(e)),D=e=>e.endsWith("%")&&R(e.slice(0,-1)),L=e=>x.test(e),A=e=>E.test(e),M=new Set(["length","size","percentage"]),z=e=>Z(e,M,V),F=e=>Z(e,"position",V),U=new Set(["image","url"]),K=e=>Z(e,U,H),W=e=>Z(e,"",G),q=()=>!0,Z=(e,t,r)=>{let n=x.exec(e);return!!n&&(n[1]?"string"==typeof t?n[1]===t:t.has(n[1]):r(n[2]))},Q=e=>T.test(e)&&!O.test(e),V=()=>!1,G=e=>S.test(e),H=e=>_.test(e),X=function(e,...t){let r,n,i;let o=function(a){return n=(r=m(t.reduce((e,t)=>t(e),e()))).cache.get,i=r.cache.set,o=s,s(a)};function s(e){let t=n(e);if(t)return t;let o=v(e,r);return i(e,o),o}return function(){return o(g.apply(null,arguments))}}(()=>{let e=w("colors"),t=w("spacing"),r=w("blur"),n=w("brightness"),i=w("borderColor"),o=w("borderRadius"),s=w("borderSpacing"),a=w("borderWidth"),c=w("contrast"),u=w("grayscale"),l=w("hueRotate"),d=w("invert"),h=w("gap"),p=w("gradientColorStops"),f=w("gradientColorStopPositions"),m=w("inset"),y=w("margin"),v=w("opacity"),g=w("padding"),b=w("saturate"),x=w("scale"),k=w("sepia"),C=w("skew"),E=w("space"),T=w("translate"),O=()=>["auto","contain","none"],S=()=>["auto","hidden","clip","visible","scroll"],_=()=>["auto",L,t],M=()=>[L,t],U=()=>["",P,I],Z=()=>["auto",R,L],Q=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],V=()=>["solid","dashed","dotted","double","none"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],H=()=>["start","end","center","between","around","evenly","stretch"],X=()=>["","0",L],Y=()=>["auto","avoid","all","avoid-page","page","left","right","column"],J=()=>[R,L];return{cacheSize:500,separator:":",theme:{colors:[q],spacing:[P,I],blur:["none","",A,L],brightness:J(),borderColor:[e],borderRadius:["none","","full",A,L],borderSpacing:M(),borderWidth:U(),contrast:J(),grayscale:X(),hueRotate:J(),invert:X(),gap:M(),gradientColorStops:[e],gradientColorStopPositions:[D,I],inset:_(),margin:_(),opacity:J(),padding:M(),saturate:J(),scale:J(),sepia:X(),skew:J(),space:M(),translate:M()},classGroups:{aspect:[{aspect:["auto","square","video",L]}],container:["container"],columns:[{columns:[A]}],"break-after":[{"break-after":Y()}],"break-before":[{"break-before":Y()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...Q(),L]}],overflow:[{overflow:S()}],"overflow-x":[{"overflow-x":S()}],"overflow-y":[{"overflow-y":S()}],overscroll:[{overscroll:O()}],"overscroll-x":[{"overscroll-x":O()}],"overscroll-y":[{"overscroll-y":O()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[m]}],"inset-x":[{"inset-x":[m]}],"inset-y":[{"inset-y":[m]}],start:[{start:[m]}],end:[{end:[m]}],top:[{top:[m]}],right:[{right:[m]}],bottom:[{bottom:[m]}],left:[{left:[m]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",N,L]}],basis:[{basis:_()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",L]}],grow:[{grow:X()}],shrink:[{shrink:X()}],order:[{order:["first","last","none",N,L]}],"grid-cols":[{"grid-cols":[q]}],"col-start-end":[{col:["auto",{span:["full",N,L]},L]}],"col-start":[{"col-start":Z()}],"col-end":[{"col-end":Z()}],"grid-rows":[{"grid-rows":[q]}],"row-start-end":[{row:["auto",{span:[N,L]},L]}],"row-start":[{"row-start":Z()}],"row-end":[{"row-end":Z()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",L]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",L]}],gap:[{gap:[h]}],"gap-x":[{"gap-x":[h]}],"gap-y":[{"gap-y":[h]}],"justify-content":[{justify:["normal",...H()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...H(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...H(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[g]}],px:[{px:[g]}],py:[{py:[g]}],ps:[{ps:[g]}],pe:[{pe:[g]}],pt:[{pt:[g]}],pr:[{pr:[g]}],pb:[{pb:[g]}],pl:[{pl:[g]}],m:[{m:[y]}],mx:[{mx:[y]}],my:[{my:[y]}],ms:[{ms:[y]}],me:[{me:[y]}],mt:[{mt:[y]}],mr:[{mr:[y]}],mb:[{mb:[y]}],ml:[{ml:[y]}],"space-x":[{"space-x":[E]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[E]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",L,t]}],"min-w":[{"min-w":[L,t,"min","max","fit"]}],"max-w":[{"max-w":[L,t,"none","full","min","max","fit","prose",{screen:[A]},A]}],h:[{h:[L,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[L,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[L,t,"auto","min","max","fit"]}],"font-size":[{text:["base",A,I]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",j]}],"font-family":[{font:[q]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractons"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",L]}],"line-clamp":[{"line-clamp":["none",R,j]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",P,L]}],"list-image":[{"list-image":["none",L]}],"list-style-type":[{list:["none","disc","decimal",L]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[v]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[v]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...V(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",P,I]}],"underline-offset":[{"underline-offset":["auto",P,L]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:M()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",L]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",L]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[v]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...Q(),F]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",z]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},K]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[f]}],"gradient-via-pos":[{via:[f]}],"gradient-to-pos":[{to:[f]}],"gradient-from":[{from:[p]}],"gradient-via":[{via:[p]}],"gradient-to":[{to:[p]}],rounded:[{rounded:[o]}],"rounded-s":[{"rounded-s":[o]}],"rounded-e":[{"rounded-e":[o]}],"rounded-t":[{"rounded-t":[o]}],"rounded-r":[{"rounded-r":[o]}],"rounded-b":[{"rounded-b":[o]}],"rounded-l":[{"rounded-l":[o]}],"rounded-ss":[{"rounded-ss":[o]}],"rounded-se":[{"rounded-se":[o]}],"rounded-ee":[{"rounded-ee":[o]}],"rounded-es":[{"rounded-es":[o]}],"rounded-tl":[{"rounded-tl":[o]}],"rounded-tr":[{"rounded-tr":[o]}],"rounded-br":[{"rounded-br":[o]}],"rounded-bl":[{"rounded-bl":[o]}],"border-w":[{border:[a]}],"border-w-x":[{"border-x":[a]}],"border-w-y":[{"border-y":[a]}],"border-w-s":[{"border-s":[a]}],"border-w-e":[{"border-e":[a]}],"border-w-t":[{"border-t":[a]}],"border-w-r":[{"border-r":[a]}],"border-w-b":[{"border-b":[a]}],"border-w-l":[{"border-l":[a]}],"border-opacity":[{"border-opacity":[v]}],"border-style":[{border:[...V(),"hidden"]}],"divide-x":[{"divide-x":[a]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[a]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[v]}],"divide-style":[{divide:V()}],"border-color":[{border:[i]}],"border-color-x":[{"border-x":[i]}],"border-color-y":[{"border-y":[i]}],"border-color-s":[{"border-s":[i]}],"border-color-e":[{"border-e":[i]}],"border-color-t":[{"border-t":[i]}],"border-color-r":[{"border-r":[i]}],"border-color-b":[{"border-b":[i]}],"border-color-l":[{"border-l":[i]}],"divide-color":[{divide:[i]}],"outline-style":[{outline:["",...V()]}],"outline-offset":[{"outline-offset":[P,L]}],"outline-w":[{outline:[P,I]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:U()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[v]}],"ring-offset-w":[{"ring-offset":[P,I]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",A,W]}],"shadow-color":[{shadow:[q]}],opacity:[{opacity:[v]}],"mix-blend":[{"mix-blend":[...G(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":G()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[n]}],contrast:[{contrast:[c]}],"drop-shadow":[{"drop-shadow":["","none",A,L]}],grayscale:[{grayscale:[u]}],"hue-rotate":[{"hue-rotate":[l]}],invert:[{invert:[d]}],saturate:[{saturate:[b]}],sepia:[{sepia:[k]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[n]}],"backdrop-contrast":[{"backdrop-contrast":[c]}],"backdrop-grayscale":[{"backdrop-grayscale":[u]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[l]}],"backdrop-invert":[{"backdrop-invert":[d]}],"backdrop-opacity":[{"backdrop-opacity":[v]}],"backdrop-saturate":[{"backdrop-saturate":[b]}],"backdrop-sepia":[{"backdrop-sepia":[k]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[s]}],"border-spacing-x":[{"border-spacing-x":[s]}],"border-spacing-y":[{"border-spacing-y":[s]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",L]}],duration:[{duration:J()}],ease:[{ease:["linear","in","out","in-out",L]}],delay:[{delay:J()}],animate:[{animate:["none","spin","ping","pulse","bounce",L]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[x]}],"scale-x":[{"scale-x":[x]}],"scale-y":[{"scale-y":[x]}],rotate:[{rotate:[N,L]}],"translate-x":[{"translate-x":[T]}],"translate-y":[{"translate-y":[T]}],"skew-x":[{"skew-x":[C]}],"skew-y":[{"skew-y":[C]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",L]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",L]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":M()}],"scroll-mx":[{"scroll-mx":M()}],"scroll-my":[{"scroll-my":M()}],"scroll-ms":[{"scroll-ms":M()}],"scroll-me":[{"scroll-me":M()}],"scroll-mt":[{"scroll-mt":M()}],"scroll-mr":[{"scroll-mr":M()}],"scroll-mb":[{"scroll-mb":M()}],"scroll-ml":[{"scroll-ml":M()}],"scroll-p":[{"scroll-p":M()}],"scroll-px":[{"scroll-px":M()}],"scroll-py":[{"scroll-py":M()}],"scroll-ps":[{"scroll-ps":M()}],"scroll-pe":[{"scroll-pe":M()}],"scroll-pt":[{"scroll-pt":M()}],"scroll-pr":[{"scroll-pr":M()}],"scroll-pb":[{"scroll-pb":M()}],"scroll-pl":[{"scroll-pl":M()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",L]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[P,I,j]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function Y(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return X(function(){for(var e,t,r=0,n="",i=arguments.length;r<i;r++)(e=arguments[r])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t)){var o=t.length;for(r=0;r<o;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n)}else for(n in t)t[n]&&(i&&(i+=" "),i+=n)}return i}(e))&&(n&&(n+=" "),n+=t);return n}(t))}},1134:function(e,t,r){"use strict";r.d(t,{Z:function(){return c}});var n=r(7294);let i=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase(),o=function(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return t.filter((e,t,r)=>!!e&&r.indexOf(e)===t).join(" ")};var s={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let a=(0,n.forwardRef)((e,t)=>{let{color:r="currentColor",size:i=24,strokeWidth:a=2,absoluteStrokeWidth:c,className:u="",children:l,iconNode:d,...h}=e;return(0,n.createElement)("svg",{ref:t,...s,width:i,height:i,stroke:r,strokeWidth:c?24*Number(a)/Number(i):a,className:o("lucide",u),...h},[...d.map(e=>{let[t,r]=e;return(0,n.createElement)(t,r)}),...Array.isArray(l)?l:[l]])}),c=(e,t)=>{let r=(0,n.forwardRef)((r,s)=>{let{className:c,...u}=r;return(0,n.createElement)(a,{ref:s,iconNode:t,className:o("lucide-".concat(i(e)),c),...u})});return r.displayName="".concat(e),r}},2190:function(e,t,r){"use strict";r.d(t,{Z:function(){return n}});let n=(0,r(1134).Z)("X",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},5926:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return ex}});var n=r(5893),i=r(9008),o=r.n(i),s=r(202),a=r(6916),c=r(5079),u=r(7294),l=r(3935),d=r(6206),h=r(8771),p=r(4548),f=r(5360),m=r(6063),y=r(2651),v=r(9115),g=r(5320),b=r(9698),w=r(7342),x=r(9981),k=u.forwardRef((e,t)=>(0,n.jsx)(g.WV.span,{...e,ref:t,style:{position:"absolute",border:0,width:1,height:1,padding:0,margin:-1,overflow:"hidden",clip:"rect(0, 0, 0, 0)",whiteSpace:"nowrap",wordWrap:"normal",...e.style}}));k.displayName="VisuallyHidden";var C="ToastProvider",[E,T,O]=(0,p.B)("Toast"),[S,_]=(0,f.b)("Toast",[O]),[P,I]=S(C),R=e=>{let{__scopeToast:t,label:r="Notification",duration:i=5e3,swipeDirection:o="right",swipeThreshold:s=50,children:a}=e,[c,l]=u.useState(null),[d,h]=u.useState(0),p=u.useRef(!1),f=u.useRef(!1);return r.trim()||console.error(`Invalid prop \`label\` supplied to \`${C}\`. Expected non-empty \`string\`.`),(0,n.jsx)(E.Provider,{scope:t,children:(0,n.jsx)(P,{scope:t,label:r,duration:i,swipeDirection:o,swipeThreshold:s,toastCount:d,viewport:c,onViewportChange:l,onToastAdd:u.useCallback(()=>h(e=>e+1),[]),onToastRemove:u.useCallback(()=>h(e=>e-1),[]),isFocusedToastEscapeKeyDownRef:p,isClosePausedRef:f,children:a})})};R.displayName=C;var j="ToastViewport",N=["F8"],D="toast.viewportPause",L="toast.viewportResume",A=u.forwardRef((e,t)=>{let{__scopeToast:r,hotkey:i=N,label:o="Notifications ({hotkey})",...s}=e,a=I(j,r),c=T(r),l=u.useRef(null),d=u.useRef(null),p=u.useRef(null),f=u.useRef(null),y=(0,h.e)(t,f,a.onViewportChange),v=i.join("+").replace(/Key/g,"").replace(/Digit/g,""),b=a.toastCount>0;u.useEffect(()=>{let e=e=>{0!==i.length&&i.every(t=>e[t]||e.code===t)&&f.current?.focus()};return document.addEventListener("keydown",e),()=>document.removeEventListener("keydown",e)},[i]),u.useEffect(()=>{let e=l.current,t=f.current;if(b&&e&&t){let r=()=>{if(!a.isClosePausedRef.current){let e=new CustomEvent(D);t.dispatchEvent(e),a.isClosePausedRef.current=!0}},n=()=>{if(a.isClosePausedRef.current){let e=new CustomEvent(L);t.dispatchEvent(e),a.isClosePausedRef.current=!1}},i=t=>{e.contains(t.relatedTarget)||n()},o=()=>{e.contains(document.activeElement)||n()};return e.addEventListener("focusin",r),e.addEventListener("focusout",i),e.addEventListener("pointermove",r),e.addEventListener("pointerleave",o),window.addEventListener("blur",r),window.addEventListener("focus",n),()=>{e.removeEventListener("focusin",r),e.removeEventListener("focusout",i),e.removeEventListener("pointermove",r),e.removeEventListener("pointerleave",o),window.removeEventListener("blur",r),window.removeEventListener("focus",n)}}},[b,a.isClosePausedRef]);let w=u.useCallback(({tabbingDirection:e})=>{let t=c().map(t=>{let r=t.ref.current,n=[r,...function(e){let t=[],r=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;r.nextNode();)t.push(r.currentNode);return t}(r)];return"forwards"===e?n:n.reverse()});return("forwards"===e?t.reverse():t).flat()},[c]);return u.useEffect(()=>{let e=f.current;if(e){let t=t=>{let r=t.altKey||t.ctrlKey||t.metaKey;if("Tab"===t.key&&!r){let r=document.activeElement,n=t.shiftKey;if(t.target===e&&n){d.current?.focus();return}let i=w({tabbingDirection:n?"backwards":"forwards"}),o=i.findIndex(e=>e===r);ee(i.slice(o+1))?t.preventDefault():n?d.current?.focus():p.current?.focus()}};return e.addEventListener("keydown",t),()=>e.removeEventListener("keydown",t)}},[c,w]),(0,n.jsxs)(m.I0,{ref:l,role:"region","aria-label":o.replace("{hotkey}",v),tabIndex:-1,style:{pointerEvents:b?void 0:"none"},children:[b&&(0,n.jsx)(z,{ref:d,onFocusFromOutsideViewport:()=>{ee(w({tabbingDirection:"forwards"}))}}),(0,n.jsx)(E.Slot,{scope:r,children:(0,n.jsx)(g.WV.ol,{tabIndex:-1,...s,ref:y})}),b&&(0,n.jsx)(z,{ref:p,onFocusFromOutsideViewport:()=>{ee(w({tabbingDirection:"backwards"}))}})]})});A.displayName=j;var M="ToastFocusProxy",z=u.forwardRef((e,t)=>{let{__scopeToast:r,onFocusFromOutsideViewport:i,...o}=e,s=I(M,r);return(0,n.jsx)(k,{"aria-hidden":!0,tabIndex:0,...o,ref:t,style:{position:"fixed"},onFocus:e=>{let t=e.relatedTarget;s.viewport?.contains(t)||i()}})});z.displayName=M;var F="Toast",U=u.forwardRef((e,t)=>{let{forceMount:r,open:i,defaultOpen:o,onOpenChange:s,...a}=e,[c=!0,u]=(0,w.T)({prop:i,defaultProp:o,onChange:s});return(0,n.jsx)(v.z,{present:r||c,children:(0,n.jsx)(q,{open:c,...a,ref:t,onClose:()=>u(!1),onPause:(0,b.W)(e.onPause),onResume:(0,b.W)(e.onResume),onSwipeStart:(0,d.M)(e.onSwipeStart,e=>{e.currentTarget.setAttribute("data-swipe","start")}),onSwipeMove:(0,d.M)(e.onSwipeMove,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","move"),e.currentTarget.style.setProperty("--radix-toast-swipe-move-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-move-y",`${r}px`)}),onSwipeCancel:(0,d.M)(e.onSwipeCancel,e=>{e.currentTarget.setAttribute("data-swipe","cancel"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-end-y")}),onSwipeEnd:(0,d.M)(e.onSwipeEnd,e=>{let{x:t,y:r}=e.detail.delta;e.currentTarget.setAttribute("data-swipe","end"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-x"),e.currentTarget.style.removeProperty("--radix-toast-swipe-move-y"),e.currentTarget.style.setProperty("--radix-toast-swipe-end-x",`${t}px`),e.currentTarget.style.setProperty("--radix-toast-swipe-end-y",`${r}px`),u(!1)})})})});U.displayName=F;var[K,W]=S(F,{onClose(){}}),q=u.forwardRef((e,t)=>{let{__scopeToast:r,type:i="foreground",duration:o,open:s,onClose:a,onEscapeKeyDown:c,onPause:p,onResume:f,onSwipeStart:y,onSwipeMove:v,onSwipeCancel:w,onSwipeEnd:x,...k}=e,C=I(F,r),[T,O]=u.useState(null),S=(0,h.e)(t,e=>O(e)),_=u.useRef(null),P=u.useRef(null),R=o||C.duration,j=u.useRef(0),N=u.useRef(R),A=u.useRef(0),{onToastAdd:M,onToastRemove:z}=C,U=(0,b.W)(()=>{T?.contains(document.activeElement)&&C.viewport?.focus(),a()}),W=u.useCallback(e=>{e&&e!==1/0&&(window.clearTimeout(A.current),j.current=new Date().getTime(),A.current=window.setTimeout(U,e))},[U]);u.useEffect(()=>{let e=C.viewport;if(e){let t=()=>{W(N.current),f?.()},r=()=>{let e=new Date().getTime()-j.current;N.current=N.current-e,window.clearTimeout(A.current),p?.()};return e.addEventListener(D,r),e.addEventListener(L,t),()=>{e.removeEventListener(D,r),e.removeEventListener(L,t)}}},[C.viewport,R,p,f,W]),u.useEffect(()=>{s&&!C.isClosePausedRef.current&&W(R)},[s,R,C.isClosePausedRef,W]),u.useEffect(()=>(M(),()=>z()),[M,z]);let q=u.useMemo(()=>T?function e(t){let r=[];return Array.from(t.childNodes).forEach(t=>{if(t.nodeType===t.TEXT_NODE&&t.textContent&&r.push(t.textContent),t.nodeType===t.ELEMENT_NODE){let n=t.ariaHidden||t.hidden||"none"===t.style.display,i=""===t.dataset.radixToastAnnounceExclude;if(!n){if(i){let e=t.dataset.radixToastAnnounceAlt;e&&r.push(e)}else r.push(...e(t))}}}),r}(T):null,[T]);return C.viewport?(0,n.jsxs)(n.Fragment,{children:[q&&(0,n.jsx)(Z,{__scopeToast:r,role:"status","aria-live":"foreground"===i?"assertive":"polite","aria-atomic":!0,children:q}),(0,n.jsx)(K,{scope:r,onClose:U,children:l.createPortal((0,n.jsx)(E.ItemSlot,{scope:r,children:(0,n.jsx)(m.fC,{asChild:!0,onEscapeKeyDown:(0,d.M)(c,()=>{C.isFocusedToastEscapeKeyDownRef.current||U(),C.isFocusedToastEscapeKeyDownRef.current=!1}),children:(0,n.jsx)(g.WV.li,{role:"status","aria-live":"off","aria-atomic":!0,tabIndex:0,"data-state":s?"open":"closed","data-swipe-direction":C.swipeDirection,...k,ref:S,style:{userSelect:"none",touchAction:"none",...e.style},onKeyDown:(0,d.M)(e.onKeyDown,e=>{"Escape"!==e.key||(c?.(e.nativeEvent),e.nativeEvent.defaultPrevented||(C.isFocusedToastEscapeKeyDownRef.current=!0,U()))}),onPointerDown:(0,d.M)(e.onPointerDown,e=>{0===e.button&&(_.current={x:e.clientX,y:e.clientY})}),onPointerMove:(0,d.M)(e.onPointerMove,e=>{if(!_.current)return;let t=e.clientX-_.current.x,r=e.clientY-_.current.y,n=!!P.current,i=["left","right"].includes(C.swipeDirection),o=["left","up"].includes(C.swipeDirection)?Math.min:Math.max,s=i?o(0,t):0,a=i?0:o(0,r),c="touch"===e.pointerType?10:2,u={x:s,y:a},l={originalEvent:e,delta:u};n?(P.current=u,$("toast.swipeMove",v,l,{discrete:!1})):B(u,C.swipeDirection,c)?(P.current=u,$("toast.swipeStart",y,l,{discrete:!1}),e.target.setPointerCapture(e.pointerId)):(Math.abs(t)>c||Math.abs(r)>c)&&(_.current=null)}),onPointerUp:(0,d.M)(e.onPointerUp,e=>{let t=P.current,r=e.target;if(r.hasPointerCapture(e.pointerId)&&r.releasePointerCapture(e.pointerId),P.current=null,_.current=null,t){let r=e.currentTarget,n={originalEvent:e,delta:t};B(t,C.swipeDirection,C.swipeThreshold)?$("toast.swipeEnd",x,n,{discrete:!0}):$("toast.swipeCancel",w,n,{discrete:!0}),r.addEventListener("click",e=>e.preventDefault(),{once:!0})}})})})}),C.viewport)})]}):null}),Z=e=>{let{__scopeToast:t,children:r,...i}=e,o=I(F,t),[s,a]=u.useState(!1),[c,l]=u.useState(!1);return function(e=()=>{}){let t=(0,b.W)(e);(0,x.b)(()=>{let e=0,r=0;return e=window.requestAnimationFrame(()=>r=window.requestAnimationFrame(t)),()=>{window.cancelAnimationFrame(e),window.cancelAnimationFrame(r)}},[t])}(()=>a(!0)),u.useEffect(()=>{let e=window.setTimeout(()=>l(!0),1e3);return()=>window.clearTimeout(e)},[]),c?null:(0,n.jsx)(y.h,{asChild:!0,children:(0,n.jsx)(k,{...i,children:s&&(0,n.jsxs)(n.Fragment,{children:[o.label," ",r]})})})},Q=u.forwardRef((e,t)=>{let{__scopeToast:r,...i}=e;return(0,n.jsx)(g.WV.div,{...i,ref:t})});Q.displayName="ToastTitle";var V=u.forwardRef((e,t)=>{let{__scopeToast:r,...i}=e;return(0,n.jsx)(g.WV.div,{...i,ref:t})});V.displayName="ToastDescription";var G="ToastAction",H=u.forwardRef((e,t)=>{let{altText:r,...i}=e;return r.trim()?(0,n.jsx)(J,{altText:r,asChild:!0,children:(0,n.jsx)(Y,{...i,ref:t})}):(console.error(`Invalid prop \`altText\` supplied to \`${G}\`. Expected non-empty \`string\`.`),null)});H.displayName=G;var X="ToastClose",Y=u.forwardRef((e,t)=>{let{__scopeToast:r,...i}=e,o=W(X,r);return(0,n.jsx)(J,{asChild:!0,children:(0,n.jsx)(g.WV.button,{type:"button",...i,ref:t,onClick:(0,d.M)(e.onClick,o.onClose)})})});Y.displayName=X;var J=u.forwardRef((e,t)=>{let{__scopeToast:r,altText:i,...o}=e;return(0,n.jsx)(g.WV.div,{"data-radix-toast-announce-exclude":"","data-radix-toast-announce-alt":i||void 0,...o,ref:t})});function $(e,t,r,{discrete:n}){let i=r.originalEvent.currentTarget,o=new CustomEvent(e,{bubbles:!0,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,g.jH)(i,o):i.dispatchEvent(o)}var B=(e,t,r=0)=>{let n=Math.abs(e.x),i=Math.abs(e.y),o=n>i;return"left"===t||"right"===t?o&&n>r:!o&&i>r};function ee(e){let t=document.activeElement;return e.some(e=>e===t||(e.focus(),document.activeElement!==t))}var et=r(5139),er=r(2190),en=r(7009);let ei=u.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(A,{ref:t,className:(0,en.cn)("fixed top-0 z-[100] flex max-h-screen w-full flex-col-reverse p-4 sm:bottom-0 sm:right-0 sm:top-auto sm:flex-col md:max-w-[420px]",r),...i})});ei.displayName=A.displayName;let eo=(0,et.j)("group pointer-events-auto relative flex w-full items-center justify-between space-x-4 overflow-hidden rounded-md border p-6 pr-8 shadow-lg transition-all data-[swipe=cancel]:translate-x-0 data-[swipe=end]:translate-x-[var(--radix-toast-swipe-end-x)] data-[swipe=move]:translate-x-[var(--radix-toast-swipe-move-x)] data-[swipe=move]:transition-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[swipe=end]:animate-out data-[state=closed]:fade-out-80 data-[state=closed]:slide-out-to-right-full data-[state=open]:slide-in-from-top-full data-[state=open]:sm:slide-in-from-bottom-full",{variants:{variant:{default:"border bg-background text-foreground",destructive:"destructive group border-destructive bg-destructive text-destructive-foreground"}},defaultVariants:{variant:"default"}}),es=u.forwardRef((e,t)=>{let{className:r,variant:i,...o}=e;return(0,n.jsx)(U,{ref:t,className:(0,en.cn)(eo({variant:i}),r),...o})});es.displayName=U.displayName,u.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(H,{ref:t,className:(0,en.cn)("inline-flex h-8 shrink-0 items-center justify-center rounded-md border bg-transparent px-3 text-sm font-medium ring-offset-background transition-colors hover:bg-secondary focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 group-[.destructive]:border-muted/40 group-[.destructive]:hover:border-destructive/30 group-[.destructive]:hover:bg-destructive group-[.destructive]:hover:text-destructive-foreground group-[.destructive]:focus:ring-destructive",r),...i})}).displayName=H.displayName;let ea=u.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(Y,{ref:t,className:(0,en.cn)("absolute right-2 top-2 rounded-md p-1 text-foreground/50 opacity-0 transition-opacity hover:text-foreground focus:opacity-100 focus:outline-none focus:ring-2 group-hover:opacity-100 group-[.destructive]:text-red-300 group-[.destructive]:hover:text-red-50 group-[.destructive]:focus:ring-red-400 group-[.destructive]:focus:ring-offset-red-600",r),"toast-close":"",...i,children:(0,n.jsx)(er.Z,{className:"h-4 w-4"})})});ea.displayName=Y.displayName;let ec=u.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(Q,{ref:t,className:(0,en.cn)("text-sm font-semibold",r),...i})});ec.displayName=Q.displayName;let eu=u.forwardRef((e,t)=>{let{className:r,...i}=e;return(0,n.jsx)(V,{ref:t,className:(0,en.cn)("text-sm opacity-90",r),...i})});function el(){let{toasts:e}=(0,c.pm)();return(0,n.jsxs)(R,{children:[e.map(function(e){let{id:t,title:r,description:i,action:o,...s}=e;return(0,n.jsxs)(es,{...s,children:[(0,n.jsxs)("div",{className:"grid gap-1",children:[r&&(0,n.jsx)(ec,{children:r}),i&&(0,n.jsx)(eu,{children:i})]}),o,(0,n.jsx)(ea,{})]},t)}),(0,n.jsx)(ei,{})]})}eu.displayName=V.displayName;var ed=r(6944),eh=r(8100),ep=r(3454);let ef=(e,t)=>{var r;return(null===(r=window.ENV)||void 0===r?void 0:r[e.replace("NEXT_PUBLIC_","")])||ep.env[e]||t},em=ef("NEXT_PUBLIC_AUTH0_DOMAIN","auth.domainmate.net"),ey=ef("NEXT_PUBLIC_AUTH0_CLIENT_ID","kCkykBJw9agqnP1Nl1ImKxQgVyA9T0uS"),ev=ef("NEXT_PUBLIC_AUTH0_AUDIENCE","https://api.domainmate.net"),eg={domain:em,clientId:ey,authorizationParams:{redirect_uri:window.location.origin,audience:ev},cacheLocation:"localstorage"};function eb(e){let{children:t}=e;return eg.domain&&eg.clientId||console.error("Auth0 configuration is missing required values"),(0,n.jsx)(eh.tw,{domain:eg.domain,clientId:eg.clientId,authorizationParams:eg.authorizationParams,cacheLocation:eg.cacheLocation,useRefreshTokens:!0,children:t})}function ew(){let e=(0,u.useRef)([]);return(0,u.useEffect)(()=>{if("undefined"==typeof document)return;let t=[],r=(e,r)=>{let n=document.createElement(e);return Object.entries(r).forEach(e=>{let[t,r]=e;n.setAttribute(t,r)}),document.head.appendChild(n),t.push(n),n};return r("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),r("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossorigin:""}),r("link",{rel:"preload",as:"font",href:"https://fonts.gstatic.com/s/inter/v12/UcCO3FwrK3iLTeHuS_fvQtMwCp50KnMw2boKoduKmMEVuGKYAZ9hiA.woff2",type:"font/woff2",crossorigin:""}),r("link",{rel:"dns-prefetch",href:"https://www.googletagmanager.com"}),e.current=t,()=>{e.current.forEach(e=>{e.parentNode&&e.parentNode.removeChild(e)})}},[]),null}function ex(e){let{Component:t,pageProps:r}=e;return(0,n.jsxs)(n.Fragment,{children:[(0,n.jsxs)(o(),{children:[(0,n.jsx)("meta",{name:"viewport",content:"width=device-width, initial-scale=1.0"}),(0,n.jsx)("meta",{name:"author",content:"DomainMate"}),(0,n.jsx)("link",{rel:"icon",type:"image/x-icon",href:"/favicon.ico"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"32x32",href:"/favicon-32x32.png"}),(0,n.jsx)("link",{rel:"icon",type:"image/png",sizes:"16x16",href:"/favicon-16x16.png"}),(0,n.jsx)("link",{rel:"apple-touch-icon",sizes:"180x180",href:"/apple-touch-icon.png"}),(0,n.jsx)("link",{rel:"manifest",href:"/site.webmanifest"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),(0,n.jsx)("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"}),(0,n.jsx)("link",{href:"https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap",rel:"stylesheet",media:"print",onLoad:e=>{e.target.media="all"}}),(0,n.jsx)("style",{dangerouslySetInnerHTML:{__html:"\n            body, html {\n              margin: 0;\n              padding: 0;\n              font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;\n            }\n            #__next {\n              min-height: 100vh;\n            }\n            .initial-content {\n              font-family: 'Inter', sans-serif;\n              max-width: 800px;\n              margin: 0 auto;\n              padding: 20px;\n              text-align: center;\n            }\n            .initial-heading {\n              font-size: 2rem;\n              font-weight: bold;\n              color: #1f2937;\n              margin-bottom: 1rem;\n            }\n            .initial-text {\n              font-size: 1.125rem;\n              color: #4b5563;\n              max-width: 600px;\n              margin: 0 auto;\n            }\n          "}}),(0,n.jsx)("meta",{name:"google-adsense-account",content:"ca-pub-****************"})]}),(0,n.jsx)(eb,{children:(0,n.jsx)(s.aH,{client:a.Eh,children:(0,n.jsxs)(ed.c,{children:[(0,n.jsx)(ew,{}),(0,n.jsx)(t,{...r}),(0,n.jsx)(el,{})]})})})]})}r(3434)},3434:function(){},7663:function(e){!function(){var t={229:function(e){var t,r,n,i=e.exports={};function o(){throw Error("setTimeout has not been defined")}function s(){throw Error("clearTimeout has not been defined")}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===o||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:o}catch(e){t=o}try{r="function"==typeof clearTimeout?clearTimeout:s}catch(e){r=s}}();var c=[],u=!1,l=-1;function d(){u&&n&&(u=!1,n.length?c=n.concat(c):l=-1,c.length&&h())}function h(){if(!u){var e=a(d);u=!0;for(var t=c.length;t;){for(n=c,c=[];++l<t;)n&&n[l].run();l=-1,t=c.length}n=null,u=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===s||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function p(e,t){this.fun=e,this.array=t}function f(){}i.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];c.push(new p(e,t)),1!==c.length||u||a(h)},p.prototype.run=function(){this.fun.apply(null,this.array)},i.title="browser",i.browser=!0,i.env={},i.argv=[],i.version="",i.versions={},i.on=f,i.addListener=f,i.once=f,i.off=f,i.removeListener=f,i.removeAllListeners=f,i.emit=f,i.prependListener=f,i.prependOnceListener=f,i.listeners=function(e){return[]},i.binding=function(e){throw Error("process.binding is not supported")},i.cwd=function(){return"/"},i.chdir=function(e){throw Error("process.chdir is not supported")},i.umask=function(){return 0}}},r={};function n(e){var i=r[e];if(void 0!==i)return i.exports;var o=r[e]={exports:{}},s=!0;try{t[e](o,o.exports,n),s=!1}finally{s&&delete r[e]}return o.exports}n.ab="//";var i=n(229);e.exports=i}()},9008:function(e,t,r){e.exports=r(3867)},6206:function(e,t,r){"use strict";function n(e,t,{checkForDefaultPrevented:r=!0}={}){return function(n){if(e?.(n),!1===r||!n.defaultPrevented)return t?.(n)}}r.d(t,{M:function(){return n}})},4548:function(e,t,r){"use strict";r.d(t,{B:function(){return a}});var n=r(7294),i=r(5893),o=r(8771),s=r(8426);function a(e){let t=e+"CollectionProvider",[r,a]=function(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let s=n.createContext(o),a=r.length;function c(t){let{scope:r,children:o,...c}=t,u=r?.[e][a]||s,l=n.useMemo(()=>c,Object.values(c));return(0,i.jsx)(u.Provider,{value:l,children:o})}return r=[...r,o],c.displayName=t+"Provider",[c,function(r,i){let c=i?.[e][a]||s,u=n.useContext(c);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}(t),[c,u]=r(t,{collectionRef:{current:null},itemMap:new Map}),l=e=>{let{scope:t,children:r}=e,o=n.useRef(null),s=n.useRef(new Map).current;return(0,i.jsx)(c,{scope:t,itemMap:s,collectionRef:o,children:r})};l.displayName=t;let d=e+"CollectionSlot",h=n.forwardRef((e,t)=>{let{scope:r,children:n}=e,a=u(d,r),c=(0,o.e)(t,a.collectionRef);return(0,i.jsx)(s.g7,{ref:c,children:n})});h.displayName=d;let p=e+"CollectionItemSlot",f="data-radix-collection-item",m=n.forwardRef((e,t)=>{let{scope:r,children:a,...c}=e,l=n.useRef(null),d=(0,o.e)(t,l),h=u(p,r);return n.useEffect(()=>(h.itemMap.set(l,{ref:l,...c}),()=>void h.itemMap.delete(l))),(0,i.jsx)(s.g7,{[f]:"",ref:d,children:a})});return m.displayName=p,[{Provider:l,Slot:h,ItemSlot:m},function(t){let r=u(e+"CollectionConsumer",t);return n.useCallback(()=>{let e=r.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll(`[${f}]`));return Array.from(r.itemMap.values()).sort((e,r)=>t.indexOf(e.ref.current)-t.indexOf(r.ref.current))},[r.collectionRef,r.itemMap])},a]}},8771:function(e,t,r){"use strict";r.d(t,{F:function(){return i},e:function(){return o}});var n=r(7294);function i(...e){return t=>e.forEach(e=>{"function"==typeof e?e(t):null!=e&&(e.current=t)})}function o(...e){return n.useCallback(i(...e),e)}},5360:function(e,t,r){"use strict";r.d(t,{b:function(){return s},k:function(){return o}});var n=r(7294),i=r(5893);function o(e,t){let r=n.createContext(t),o=e=>{let{children:t,...o}=e,s=n.useMemo(()=>o,Object.values(o));return(0,i.jsx)(r.Provider,{value:s,children:t})};return o.displayName=e+"Provider",[o,function(i){let o=n.useContext(r);if(o)return o;if(void 0!==t)return t;throw Error(`\`${i}\` must be used within \`${e}\``)}]}function s(e,t=[]){let r=[],o=()=>{let t=r.map(e=>n.createContext(e));return function(r){let i=r?.[e]||t;return n.useMemo(()=>({[`__scope${e}`]:{...r,[e]:i}}),[r,i])}};return o.scopeName=e,[function(t,o){let s=n.createContext(o),a=r.length;r=[...r,o];let c=t=>{let{scope:r,children:o,...c}=t,u=r?.[e]?.[a]||s,l=n.useMemo(()=>c,Object.values(c));return(0,i.jsx)(u.Provider,{value:l,children:o})};return c.displayName=t+"Provider",[c,function(r,i){let c=i?.[e]?.[a]||s,u=n.useContext(c);if(u)return u;if(void 0!==o)return o;throw Error(`\`${r}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let r=()=>{let r=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let i=r.reduce((t,{useScope:r,scopeName:n})=>{let i=r(e)[`__scope${n}`];return{...t,...i}},{});return n.useMemo(()=>({[`__scope${t.scopeName}`]:i}),[i])}};return r.scopeName=t.scopeName,r}(o,...t)]}},6063:function(e,t,r){"use strict";r.d(t,{I0:function(){return v},XB:function(){return h},fC:function(){return y}});var n,i=r(7294),o=r(6206),s=r(5320),a=r(8771),c=r(9698),u=r(5893),l="dismissableLayer.update",d=i.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),h=i.forwardRef((e,t)=>{let{disableOutsidePointerEvents:r=!1,onEscapeKeyDown:h,onPointerDownOutside:p,onFocusOutside:y,onInteractOutside:v,onDismiss:g,...b}=e,w=i.useContext(d),[x,k]=i.useState(null),C=x?.ownerDocument??globalThis?.document,[,E]=i.useState({}),T=(0,a.e)(t,e=>k(e)),O=Array.from(w.layers),[S]=[...w.layersWithOutsidePointerEventsDisabled].slice(-1),_=O.indexOf(S),P=x?O.indexOf(x):-1,I=w.layersWithOutsidePointerEventsDisabled.size>0,R=P>=_,j=function(e,t=globalThis?.document){let r=(0,c.W)(e),n=i.useRef(!1),o=i.useRef(()=>{});return i.useEffect(()=>{let e=e=>{if(e.target&&!n.current){let n=function(){m("dismissableLayer.pointerDownOutside",r,i,{discrete:!0})},i={originalEvent:e};"touch"===e.pointerType?(t.removeEventListener("click",o.current),o.current=n,t.addEventListener("click",o.current,{once:!0})):n()}else t.removeEventListener("click",o.current);n.current=!1},i=window.setTimeout(()=>{t.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(i),t.removeEventListener("pointerdown",e),t.removeEventListener("click",o.current)}},[t,r]),{onPointerDownCapture:()=>n.current=!0}}(e=>{let t=e.target,r=[...w.branches].some(e=>e.contains(t));!R||r||(p?.(e),v?.(e),e.defaultPrevented||g?.())},C),N=function(e,t=globalThis?.document){let r=(0,c.W)(e),n=i.useRef(!1);return i.useEffect(()=>{let e=e=>{e.target&&!n.current&&m("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return t.addEventListener("focusin",e),()=>t.removeEventListener("focusin",e)},[t,r]),{onFocusCapture:()=>n.current=!0,onBlurCapture:()=>n.current=!1}}(e=>{let t=e.target;[...w.branches].some(e=>e.contains(t))||(y?.(e),v?.(e),e.defaultPrevented||g?.())},C);return!function(e,t=globalThis?.document){let r=(0,c.W)(e);i.useEffect(()=>{let e=e=>{"Escape"===e.key&&r(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[r,t])}(e=>{P!==w.layers.size-1||(h?.(e),!e.defaultPrevented&&g&&(e.preventDefault(),g()))},C),i.useEffect(()=>{if(x)return r&&(0===w.layersWithOutsidePointerEventsDisabled.size&&(n=C.body.style.pointerEvents,C.body.style.pointerEvents="none"),w.layersWithOutsidePointerEventsDisabled.add(x)),w.layers.add(x),f(),()=>{r&&1===w.layersWithOutsidePointerEventsDisabled.size&&(C.body.style.pointerEvents=n)}},[x,C,r,w]),i.useEffect(()=>()=>{x&&(w.layers.delete(x),w.layersWithOutsidePointerEventsDisabled.delete(x),f())},[x,w]),i.useEffect(()=>{let e=()=>E({});return document.addEventListener(l,e),()=>document.removeEventListener(l,e)},[]),(0,u.jsx)(s.WV.div,{...b,ref:T,style:{pointerEvents:I?R?"auto":"none":void 0,...e.style},onFocusCapture:(0,o.M)(e.onFocusCapture,N.onFocusCapture),onBlurCapture:(0,o.M)(e.onBlurCapture,N.onBlurCapture),onPointerDownCapture:(0,o.M)(e.onPointerDownCapture,j.onPointerDownCapture)})});h.displayName="DismissableLayer";var p=i.forwardRef((e,t)=>{let r=i.useContext(d),n=i.useRef(null),o=(0,a.e)(t,n);return i.useEffect(()=>{let e=n.current;if(e)return r.branches.add(e),()=>{r.branches.delete(e)}},[r.branches]),(0,u.jsx)(s.WV.div,{...e,ref:o})});function f(){let e=new CustomEvent(l);document.dispatchEvent(e)}function m(e,t,r,{discrete:n}){let i=r.originalEvent.target,o=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:r});t&&i.addEventListener(e,t,{once:!0}),n?(0,s.jH)(i,o):i.dispatchEvent(o)}p.displayName="DismissableLayerBranch";var y=h,v=p},2651:function(e,t,r){"use strict";r.d(t,{h:function(){return c}});var n=r(7294),i=r(3935),o=r(5320),s=r(9981),a=r(5893),c=n.forwardRef((e,t)=>{let{container:r,...c}=e,[u,l]=n.useState(!1);(0,s.b)(()=>l(!0),[]);let d=r||u&&globalThis?.document?.body;return d?i.createPortal((0,a.jsx)(o.WV.div,{...c,ref:t}),d):null});c.displayName="Portal"},9115:function(e,t,r){"use strict";r.d(t,{z:function(){return s}});var n=r(7294),i=r(8771),o=r(9981),s=e=>{let t,r;let{present:s,children:c}=e,u=function(e){var t,r;let[i,s]=n.useState(),c=n.useRef({}),u=n.useRef(e),l=n.useRef("none"),[d,h]=(t=e?"mounted":"unmounted",r={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},n.useReducer((e,t)=>r[e][t]??e,t));return n.useEffect(()=>{let e=a(c.current);l.current="mounted"===d?e:"none"},[d]),(0,o.b)(()=>{let t=c.current,r=u.current;if(r!==e){let n=l.current,i=a(t);e?h("MOUNT"):"none"===i||t?.display==="none"?h("UNMOUNT"):r&&n!==i?h("ANIMATION_OUT"):h("UNMOUNT"),u.current=e}},[e,h]),(0,o.b)(()=>{if(i){let e;let t=i.ownerDocument.defaultView??window,r=r=>{let n=a(c.current).includes(r.animationName);if(r.target===i&&n&&(h("ANIMATION_END"),!u.current)){let r=i.style.animationFillMode;i.style.animationFillMode="forwards",e=t.setTimeout(()=>{"forwards"===i.style.animationFillMode&&(i.style.animationFillMode=r)})}},n=e=>{e.target===i&&(l.current=a(c.current))};return i.addEventListener("animationstart",n),i.addEventListener("animationcancel",r),i.addEventListener("animationend",r),()=>{t.clearTimeout(e),i.removeEventListener("animationstart",n),i.removeEventListener("animationcancel",r),i.removeEventListener("animationend",r)}}h("ANIMATION_END")},[i,h]),{isPresent:["mounted","unmountSuspended"].includes(d),ref:n.useCallback(e=>{e&&(c.current=getComputedStyle(e)),s(e)},[])}}(s),l="function"==typeof c?c({present:u.isPresent}):n.Children.only(c),d=(0,i.e)(u.ref,(t=Object.getOwnPropertyDescriptor(l.props,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?l.ref:(t=Object.getOwnPropertyDescriptor(l,"ref")?.get)&&"isReactWarning"in t&&t.isReactWarning?l.props.ref:l.props.ref||l.ref);return"function"==typeof c||u.isPresent?n.cloneElement(l,{ref:d}):null};function a(e){return e?.animationName||"none"}s.displayName="Presence"},5320:function(e,t,r){"use strict";r.d(t,{WV:function(){return a},jH:function(){return c}});var n=r(7294),i=r(3935),o=r(8426),s=r(5893),a=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","span","svg","ul"].reduce((e,t)=>{let r=n.forwardRef((e,r)=>{let{asChild:n,...i}=e,a=n?o.g7:t;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,s.jsx)(a,{...i,ref:r})});return r.displayName=`Primitive.${t}`,{...e,[t]:r}},{});function c(e,t){e&&i.flushSync(()=>e.dispatchEvent(t))}},8426:function(e,t,r){"use strict";r.d(t,{g7:function(){return s}});var n=r(7294),i=r(8771),o=r(5893),s=n.forwardRef((e,t)=>{let{children:r,...i}=e,s=n.Children.toArray(r),c=s.find(u);if(c){let e=c.props.children,r=s.map(t=>t!==c?t:n.Children.count(e)>1?n.Children.only(null):n.isValidElement(e)?e.props.children:null);return(0,o.jsx)(a,{...i,ref:t,children:n.isValidElement(e)?n.cloneElement(e,void 0,r):null})}return(0,o.jsx)(a,{...i,ref:t,children:r})});s.displayName="Slot";var a=n.forwardRef((e,t)=>{let{children:r,...o}=e;if(n.isValidElement(r)){let e,s;let a=(e=Object.getOwnPropertyDescriptor(r.props,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.ref:(e=Object.getOwnPropertyDescriptor(r,"ref")?.get)&&"isReactWarning"in e&&e.isReactWarning?r.props.ref:r.props.ref||r.ref;return n.cloneElement(r,{...function(e,t){let r={...t};for(let n in t){let i=e[n],o=t[n];/^on[A-Z]/.test(n)?i&&o?r[n]=(...e)=>{o(...e),i(...e)}:i&&(r[n]=i):"style"===n?r[n]={...i,...o}:"className"===n&&(r[n]=[i,o].filter(Boolean).join(" "))}return{...e,...r}}(o,r.props),ref:t?(0,i.F)(t,a):a})}return n.Children.count(r)>1?n.Children.only(null):null});a.displayName="SlotClone";var c=({children:e})=>(0,o.jsx)(o.Fragment,{children:e});function u(e){return n.isValidElement(e)&&e.type===c}},9698:function(e,t,r){"use strict";r.d(t,{W:function(){return i}});var n=r(7294);function i(e){let t=n.useRef(e);return n.useEffect(()=>{t.current=e}),n.useMemo(()=>(...e)=>t.current?.(...e),[])}},7342:function(e,t,r){"use strict";r.d(t,{T:function(){return o}});var n=r(7294),i=r(9698);function o({prop:e,defaultProp:t,onChange:r=()=>{}}){let[o,s]=function({defaultProp:e,onChange:t}){let r=n.useState(e),[o]=r,s=n.useRef(o),a=(0,i.W)(t);return n.useEffect(()=>{s.current!==o&&(a(o),s.current=o)},[o,s,a]),r}({defaultProp:t,onChange:r}),a=void 0!==e,c=a?e:o,u=(0,i.W)(r);return[c,n.useCallback(t=>{if(a){let r="function"==typeof t?t(e):t;r!==e&&u(r)}else s(t)},[a,e,s,u])]}},9981:function(e,t,r){"use strict";r.d(t,{b:function(){return i}});var n=r(7294),i=globalThis?.document?n.useLayoutEffect:()=>{}},6474:function(e,t,r){"use strict";r.d(t,{j:function(){return o}});var n=r(7506),i=r(4139),o=new class extends n.l{#d;#h;#p;constructor(){super(),this.#p=e=>{if(!i.sk&&window.addEventListener){let t=()=>e();return window.addEventListener("visibilitychange",t,!1),()=>{window.removeEventListener("visibilitychange",t)}}}}onSubscribe(){this.#h||this.setEventListener(this.#p)}onUnsubscribe(){this.hasListeners()||(this.#h?.(),this.#h=void 0)}setEventListener(e){this.#p=e,this.#h?.(),this.#h=e(e=>{"boolean"==typeof e?this.setFocused(e):this.onFocus()})}setFocused(e){this.#d!==e&&(this.#d=e,this.onFocus())}onFocus(){let e=this.isFocused();this.listeners.forEach(t=>{t(e)})}isFocused(){return"boolean"==typeof this.#d?this.#d:globalThis.document?.visibilityState!=="hidden"}}},9289:function(e,t,r){"use strict";r.d(t,{R:function(){return a},m:function(){return s}});var n=r(7037),i=r(8907),o=r(2008),s=class extends i.F{#f;#i;#m;constructor(e){super(),this.mutationId=e.mutationId,this.#i=e.mutationCache,this.#f=[],this.state=e.state||a(),this.setOptions(e.options),this.scheduleGc()}setOptions(e){this.options=e,this.updateGcTime(this.options.gcTime)}get meta(){return this.options.meta}addObserver(e){this.#f.includes(e)||(this.#f.push(e),this.clearGcTimeout(),this.#i.notify({type:"observerAdded",mutation:this,observer:e}))}removeObserver(e){this.#f=this.#f.filter(t=>t!==e),this.scheduleGc(),this.#i.notify({type:"observerRemoved",mutation:this,observer:e})}optionalRemove(){this.#f.length||("pending"===this.state.status?this.scheduleGc():this.#i.remove(this))}continue(){return this.#m?.continue()??this.execute(this.state.variables)}async execute(e){this.#m=(0,o.Mz)({fn:()=>this.options.mutationFn?this.options.mutationFn(e):Promise.reject(Error("No mutationFn found")),onFail:(e,t)=>{this.#y({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#y({type:"pause"})},onContinue:()=>{this.#y({type:"continue"})},retry:this.options.retry??0,retryDelay:this.options.retryDelay,networkMode:this.options.networkMode,canRun:()=>this.#i.canRun(this)});let t="pending"===this.state.status,r=!this.#m.canStart();try{if(!t){this.#y({type:"pending",variables:e,isPaused:r}),await this.#i.config.onMutate?.(e,this);let t=await this.options.onMutate?.(e);t!==this.state.context&&this.#y({type:"pending",context:t,variables:e,isPaused:r})}let n=await this.#m.start();return await this.#i.config.onSuccess?.(n,e,this.state.context,this),await this.options.onSuccess?.(n,e,this.state.context),await this.#i.config.onSettled?.(n,null,this.state.variables,this.state.context,this),await this.options.onSettled?.(n,null,e,this.state.context),this.#y({type:"success",data:n}),n}catch(t){try{throw await this.#i.config.onError?.(t,e,this.state.context,this),await this.options.onError?.(t,e,this.state.context),await this.#i.config.onSettled?.(void 0,t,this.state.variables,this.state.context,this),await this.options.onSettled?.(void 0,t,e,this.state.context),t}finally{this.#y({type:"error",error:t})}}finally{this.#i.runNext(this)}}#y(e){this.state=(t=>{switch(e.type){case"failed":return{...t,failureCount:e.failureCount,failureReason:e.error};case"pause":return{...t,isPaused:!0};case"continue":return{...t,isPaused:!1};case"pending":return{...t,context:e.context,data:void 0,failureCount:0,failureReason:null,error:null,isPaused:e.isPaused,status:"pending",variables:e.variables,submittedAt:Date.now()};case"success":return{...t,data:e.data,failureCount:0,failureReason:null,error:null,status:"success",isPaused:!1};case"error":return{...t,data:void 0,error:e.error,failureCount:t.failureCount+1,failureReason:e.error,isPaused:!1,status:"error"}}})(this.state),n.V.batch(()=>{this.#f.forEach(t=>{t.onMutationUpdate(e)}),this.#i.notify({mutation:this,type:"updated",action:e})})}};function a(){return{context:void 0,data:void 0,error:null,failureCount:0,failureReason:null,isPaused:!1,status:"idle",variables:void 0,submittedAt:0}}},7037:function(e,t,r){"use strict";r.d(t,{V:function(){return n}});var n=function(){let e=[],t=0,r=e=>{e()},n=e=>{e()},i=e=>setTimeout(e,0),o=n=>{t?e.push(n):i(()=>{r(n)})},s=()=>{let t=e;e=[],t.length&&i(()=>{n(()=>{t.forEach(e=>{r(e)})})})};return{batch:e=>{let r;t++;try{r=e()}finally{--t||s()}return r},batchCalls:e=>(...t)=>{o(()=>{e(...t)})},schedule:o,setNotifyFunction:e=>{r=e},setBatchNotifyFunction:e=>{n=e},setScheduler:e=>{i=e}}}()},4304:function(e,t,r){"use strict";r.d(t,{N:function(){return o}});var n=r(7506),i=r(4139),o=new class extends n.l{#v=!0;#h;#p;constructor(){super(),this.#p=e=>{if(!i.sk&&window.addEventListener){let t=()=>e(!0),r=()=>e(!1);return window.addEventListener("online",t,!1),window.addEventListener("offline",r,!1),()=>{window.removeEventListener("online",t),window.removeEventListener("offline",r)}}}}onSubscribe(){this.#h||this.setEventListener(this.#p)}onUnsubscribe(){this.hasListeners()||(this.#h?.(),this.#h=void 0)}setEventListener(e){this.#p=e,this.#h?.(),this.#h=e(this.setOnline.bind(this))}setOnline(e){this.#v!==e&&(this.#v=e,this.listeners.forEach(t=>{t(e)}))}isOnline(){return this.#v}}},6888:function(e,t,r){"use strict";r.d(t,{A:function(){return a},z:function(){return c}});var n=r(4139),i=r(7037),o=r(2008),s=r(8907),a=class extends s.F{#g;#b;#w;#m;#o;#x;constructor(e){super(),this.#x=!1,this.#o=e.defaultOptions,this.setOptions(e.options),this.observers=[],this.#w=e.cache,this.queryKey=e.queryKey,this.queryHash=e.queryHash,this.#g=function(e){let t="function"==typeof e.initialData?e.initialData():e.initialData,r=void 0!==t,n=r?"function"==typeof e.initialDataUpdatedAt?e.initialDataUpdatedAt():e.initialDataUpdatedAt:0;return{data:t,dataUpdateCount:0,dataUpdatedAt:r?n??Date.now():0,error:null,errorUpdateCount:0,errorUpdatedAt:0,fetchFailureCount:0,fetchFailureReason:null,fetchMeta:null,isInvalidated:!1,status:r?"success":"pending",fetchStatus:"idle"}}(this.options),this.state=e.state??this.#g,this.scheduleGc()}get meta(){return this.options.meta}get promise(){return this.#m?.promise}setOptions(e){this.options={...this.#o,...e},this.updateGcTime(this.options.gcTime)}optionalRemove(){this.observers.length||"idle"!==this.state.fetchStatus||this.#w.remove(this)}setData(e,t){let r=(0,n.oE)(this.state.data,e,this.options);return this.#y({data:r,type:"success",dataUpdatedAt:t?.updatedAt,manual:t?.manual}),r}setState(e,t){this.#y({type:"setState",state:e,setStateOptions:t})}cancel(e){let t=this.#m?.promise;return this.#m?.cancel(e),t?t.then(n.ZT).catch(n.ZT):Promise.resolve()}destroy(){super.destroy(),this.cancel({silent:!0})}reset(){this.destroy(),this.setState(this.#g)}isActive(){return this.observers.some(e=>!1!==(0,n.Nc)(e.options.enabled,this))}isDisabled(){return this.getObserversCount()>0?!this.isActive():this.options.queryFn===n.CN||this.state.dataUpdateCount+this.state.errorUpdateCount===0}isStale(){return!!this.state.isInvalidated||(this.getObserversCount()>0?this.observers.some(e=>e.getCurrentResult().isStale):void 0===this.state.data)}isStaleByTime(e=0){return this.state.isInvalidated||void 0===this.state.data||!(0,n.Kp)(this.state.dataUpdatedAt,e)}onFocus(){let e=this.observers.find(e=>e.shouldFetchOnWindowFocus());e?.refetch({cancelRefetch:!1}),this.#m?.continue()}onOnline(){let e=this.observers.find(e=>e.shouldFetchOnReconnect());e?.refetch({cancelRefetch:!1}),this.#m?.continue()}addObserver(e){this.observers.includes(e)||(this.observers.push(e),this.clearGcTimeout(),this.#w.notify({type:"observerAdded",query:this,observer:e}))}removeObserver(e){this.observers.includes(e)&&(this.observers=this.observers.filter(t=>t!==e),this.observers.length||(this.#m&&(this.#x?this.#m.cancel({revert:!0}):this.#m.cancelRetry()),this.scheduleGc()),this.#w.notify({type:"observerRemoved",query:this,observer:e}))}getObserversCount(){return this.observers.length}invalidate(){this.state.isInvalidated||this.#y({type:"invalidate"})}fetch(e,t){if("idle"!==this.state.fetchStatus){if(void 0!==this.state.data&&t?.cancelRefetch)this.cancel({silent:!0});else if(this.#m)return this.#m.continueRetry(),this.#m.promise}if(e&&this.setOptions(e),!this.options.queryFn){let e=this.observers.find(e=>e.options.queryFn);e&&this.setOptions(e.options)}let r=new AbortController,i=e=>{Object.defineProperty(e,"signal",{enumerable:!0,get:()=>(this.#x=!0,r.signal)})},s={fetchOptions:t,options:this.options,queryKey:this.queryKey,state:this.state,fetchFn:()=>{let e=(0,n.cG)(this.options,t),r={queryKey:this.queryKey,meta:this.meta};return(i(r),this.#x=!1,this.options.persister)?this.options.persister(e,r,this):e(r)}};i(s),this.options.behavior?.onFetch(s,this),this.#b=this.state,("idle"===this.state.fetchStatus||this.state.fetchMeta!==s.fetchOptions?.meta)&&this.#y({type:"fetch",meta:s.fetchOptions?.meta});let a=e=>{(0,o.DV)(e)&&e.silent||this.#y({type:"error",error:e}),(0,o.DV)(e)||(this.#w.config.onError?.(e,this),this.#w.config.onSettled?.(this.state.data,e,this)),this.scheduleGc()};return this.#m=(0,o.Mz)({initialPromise:t?.initialPromise,fn:s.fetchFn,abort:r.abort.bind(r),onSuccess:e=>{if(void 0===e){a(Error(`${this.queryHash} data is undefined`));return}try{this.setData(e)}catch(e){a(e);return}this.#w.config.onSuccess?.(e,this),this.#w.config.onSettled?.(e,this.state.error,this),this.scheduleGc()},onError:a,onFail:(e,t)=>{this.#y({type:"failed",failureCount:e,error:t})},onPause:()=>{this.#y({type:"pause"})},onContinue:()=>{this.#y({type:"continue"})},retry:s.options.retry,retryDelay:s.options.retryDelay,networkMode:s.options.networkMode,canRun:()=>!0}),this.#m.start()}#y(e){this.state=(t=>{switch(e.type){case"failed":return{...t,fetchFailureCount:e.failureCount,fetchFailureReason:e.error};case"pause":return{...t,fetchStatus:"paused"};case"continue":return{...t,fetchStatus:"fetching"};case"fetch":return{...t,...c(t.data,this.options),fetchMeta:e.meta??null};case"success":return{...t,data:e.data,dataUpdateCount:t.dataUpdateCount+1,dataUpdatedAt:e.dataUpdatedAt??Date.now(),error:null,isInvalidated:!1,status:"success",...!e.manual&&{fetchStatus:"idle",fetchFailureCount:0,fetchFailureReason:null}};case"error":let r=e.error;if((0,o.DV)(r)&&r.revert&&this.#b)return{...this.#b,fetchStatus:"idle"};return{...t,error:r,errorUpdateCount:t.errorUpdateCount+1,errorUpdatedAt:Date.now(),fetchFailureCount:t.fetchFailureCount+1,fetchFailureReason:r,fetchStatus:"idle",status:"error"};case"invalidate":return{...t,isInvalidated:!0};case"setState":return{...t,...e.state}}})(this.state),i.V.batch(()=>{this.observers.forEach(e=>{e.onQueryUpdate()}),this.#w.notify({query:this,type:"updated",action:e})})}};function c(e,t){return{fetchFailureCount:0,fetchFailureReason:null,fetchStatus:(0,o.Kw)(t.networkMode)?"fetching":"paused",...void 0===e&&{error:null,status:"pending"}}}},8907:function(e,t,r){"use strict";r.d(t,{F:function(){return i}});var n=r(4139),i=class{#k;destroy(){this.clearGcTimeout()}scheduleGc(){this.clearGcTimeout(),(0,n.PN)(this.gcTime)&&(this.#k=setTimeout(()=>{this.optionalRemove()},this.gcTime))}updateGcTime(e){this.gcTime=Math.max(this.gcTime||0,e??(n.sk?1/0:3e5))}clearGcTimeout(){this.#k&&(clearTimeout(this.#k),this.#k=void 0)}}},2008:function(e,t,r){"use strict";r.d(t,{DV:function(){return l},Kw:function(){return c},Mz:function(){return d}});var n=r(6474),i=r(4304),o=r(3820),s=r(4139);function a(e){return Math.min(1e3*2**e,3e4)}function c(e){return(e??"online")!=="online"||i.N.isOnline()}var u=class extends Error{constructor(e){super("CancelledError"),this.revert=e?.revert,this.silent=e?.silent}};function l(e){return e instanceof u}function d(e){let t,r=!1,l=0,d=!1,h=(0,o.O)(),p=()=>n.j.isFocused()&&("always"===e.networkMode||i.N.isOnline())&&e.canRun(),f=()=>c(e.networkMode)&&e.canRun(),m=r=>{d||(d=!0,e.onSuccess?.(r),t?.(),h.resolve(r))},y=r=>{d||(d=!0,e.onError?.(r),t?.(),h.reject(r))},v=()=>new Promise(r=>{t=e=>{(d||p())&&r(e)},e.onPause?.()}).then(()=>{t=void 0,d||e.onContinue?.()}),g=()=>{let t;if(d)return;let n=0===l?e.initialPromise:void 0;try{t=n??e.fn()}catch(e){t=Promise.reject(e)}Promise.resolve(t).then(m).catch(t=>{if(d)return;let n=e.retry??(s.sk?0:3),i=e.retryDelay??a,o="function"==typeof i?i(l,t):i,c=!0===n||"number"==typeof n&&l<n||"function"==typeof n&&n(l,t);if(r||!c){y(t);return}l++,e.onFail?.(l,t),(0,s._v)(o).then(()=>p()?void 0:v()).then(()=>{r?y(t):g()})})};return{promise:h,cancel:t=>{d||(y(new u(t)),e.abort?.())},continue:()=>(t?.(),h),cancelRetry:()=>{r=!0},continueRetry:()=>{r=!1},canStart:f,start:()=>(f()?g():v().then(g),h)}}},7506:function(e,t,r){"use strict";r.d(t,{l:function(){return n}});var n=class{constructor(){this.listeners=new Set,this.subscribe=this.subscribe.bind(this)}subscribe(e){return this.listeners.add(e),this.onSubscribe(),()=>{this.listeners.delete(e),this.onUnsubscribe()}}hasListeners(){return this.listeners.size>0}onSubscribe(){}onUnsubscribe(){}}},3820:function(e,t,r){"use strict";function n(){let e,t;let r=new Promise((r,n)=>{e=r,t=n});function n(e){Object.assign(r,e),delete r.resolve,delete r.reject}return r.status="pending",r.catch(()=>{}),r.resolve=t=>{n({status:"fulfilled",value:t}),e(t)},r.reject=e=>{n({status:"rejected",reason:e}),t(e)},r}r.d(t,{O:function(){return n}})},4139:function(e,t,r){"use strict";r.d(t,{CN:function(){return C},Ht:function(){return k},KC:function(){return c},Kp:function(){return a},Nc:function(){return u},PN:function(){return s},Rm:function(){return h},SE:function(){return o},VS:function(){return m},VX:function(){return x},X7:function(){return d},Ym:function(){return p},ZT:function(){return i},_v:function(){return b},_x:function(){return l},cG:function(){return E},oE:function(){return w},sk:function(){return n},to:function(){return f}});var n="undefined"==typeof window||"Deno"in globalThis;function i(){}function o(e,t){return"function"==typeof e?e(t):e}function s(e){return"number"==typeof e&&e>=0&&e!==1/0}function a(e,t){return Math.max(e+(t||0)-Date.now(),0)}function c(e,t){return"function"==typeof e?e(t):e}function u(e,t){return"function"==typeof e?e(t):e}function l(e,t){let{type:r="all",exact:n,fetchStatus:i,predicate:o,queryKey:s,stale:a}=e;if(s){if(n){if(t.queryHash!==h(s,t.options))return!1}else if(!f(t.queryKey,s))return!1}if("all"!==r){let e=t.isActive();if("active"===r&&!e||"inactive"===r&&e)return!1}return("boolean"!=typeof a||t.isStale()===a)&&(!i||i===t.state.fetchStatus)&&(!o||!!o(t))}function d(e,t){let{exact:r,status:n,predicate:i,mutationKey:o}=e;if(o){if(!t.options.mutationKey)return!1;if(r){if(p(t.options.mutationKey)!==p(o))return!1}else if(!f(t.options.mutationKey,o))return!1}return(!n||t.state.status===n)&&(!i||!!i(t))}function h(e,t){return(t?.queryKeyHashFn||p)(e)}function p(e){return JSON.stringify(e,(e,t)=>v(t)?Object.keys(t).sort().reduce((e,r)=>(e[r]=t[r],e),{}):t)}function f(e,t){return e===t||typeof e==typeof t&&!!e&&!!t&&"object"==typeof e&&"object"==typeof t&&!Object.keys(t).some(r=>!f(e[r],t[r]))}function m(e,t){if(!t||Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}function y(e){return Array.isArray(e)&&e.length===Object.keys(e).length}function v(e){if(!g(e))return!1;let t=e.constructor;if(void 0===t)return!0;let r=t.prototype;return!!(g(r)&&r.hasOwnProperty("isPrototypeOf"))&&Object.getPrototypeOf(e)===Object.prototype}function g(e){return"[object Object]"===Object.prototype.toString.call(e)}function b(e){return new Promise(t=>{setTimeout(t,e)})}function w(e,t,r){return"function"==typeof r.structuralSharing?r.structuralSharing(e,t):!1!==r.structuralSharing?function e(t,r){if(t===r)return t;let n=y(t)&&y(r);if(n||v(t)&&v(r)){let i=n?t:Object.keys(t),o=i.length,s=n?r:Object.keys(r),a=s.length,c=n?[]:{},u=0;for(let o=0;o<a;o++){let a=n?o:s[o];(!n&&i.includes(a)||n)&&void 0===t[a]&&void 0===r[a]?(c[a]=void 0,u++):(c[a]=e(t[a],r[a]),c[a]===t[a]&&void 0!==t[a]&&u++)}return o===a&&u===o?t:c}return r}(e,t):t}function x(e,t,r=0){let n=[...e,t];return r&&n.length>r?n.slice(1):n}function k(e,t,r=0){let n=[t,...e];return r&&n.length>r?n.slice(0,-1):n}var C=Symbol();function E(e,t){return!e.queryFn&&t?.initialPromise?()=>t.initialPromise:e.queryFn&&e.queryFn!==C?e.queryFn:()=>Promise.reject(Error(`Missing queryFn: '${e.queryHash}'`))}},202:function(e,t,r){"use strict";r.d(t,{NL:function(){return s},aH:function(){return a}});var n=r(7294),i=r(5893),o=n.createContext(void 0),s=e=>{let t=n.useContext(o);if(e)return e;if(!t)throw Error("No QueryClient set, use QueryClientProvider to set one");return t},a=({client:e,children:t})=>(n.useEffect(()=>(e.mount(),()=>{e.unmount()}),[e]),(0,i.jsx)(o.Provider,{value:e,children:t}))},8029:function(e,t,r){"use strict";r.d(t,{D:function(){return d}});var n=r(7294),i=r(9289),o=r(7037),s=r(7506),a=r(4139),c=class extends s.l{#C;#E=void 0;#T;#O;constructor(e,t){super(),this.#C=e,this.setOptions(t),this.bindMethods(),this.#S()}bindMethods(){this.mutate=this.mutate.bind(this),this.reset=this.reset.bind(this)}setOptions(e){let t=this.options;this.options=this.#C.defaultMutationOptions(e),(0,a.VS)(this.options,t)||this.#C.getMutationCache().notify({type:"observerOptionsUpdated",mutation:this.#T,observer:this}),t?.mutationKey&&this.options.mutationKey&&(0,a.Ym)(t.mutationKey)!==(0,a.Ym)(this.options.mutationKey)?this.reset():this.#T?.state.status==="pending"&&this.#T.setOptions(this.options)}onUnsubscribe(){this.hasListeners()||this.#T?.removeObserver(this)}onMutationUpdate(e){this.#S(),this.#_(e)}getCurrentResult(){return this.#E}reset(){this.#T?.removeObserver(this),this.#T=void 0,this.#S(),this.#_()}mutate(e,t){return this.#O=t,this.#T?.removeObserver(this),this.#T=this.#C.getMutationCache().build(this.#C,this.options),this.#T.addObserver(this),this.#T.execute(e)}#S(){let e=this.#T?.state??(0,i.R)();this.#E={...e,isPending:"pending"===e.status,isSuccess:"success"===e.status,isError:"error"===e.status,isIdle:"idle"===e.status,mutate:this.mutate,reset:this.reset}}#_(e){o.V.batch(()=>{if(this.#O&&this.hasListeners()){let t=this.#E.variables,r=this.#E.context;e?.type==="success"?(this.#O.onSuccess?.(e.data,t,r),this.#O.onSettled?.(e.data,null,t,r)):e?.type==="error"&&(this.#O.onError?.(e.error,t,r),this.#O.onSettled?.(void 0,e.error,t,r))}this.listeners.forEach(e=>{e(this.#E)})})}},u=r(202),l=r(6290);function d(e,t){let r=(0,u.NL)(t),[i]=n.useState(()=>new c(r,e));n.useEffect(()=>{i.setOptions(e)},[i,e]);let s=n.useSyncExternalStore(n.useCallback(e=>i.subscribe(o.V.batchCalls(e)),[i]),()=>i.getCurrentResult(),()=>i.getCurrentResult()),a=n.useCallback((e,t)=>{i.mutate(e,t).catch(l.Z)},[i]);if(s.error&&(0,l.L)(i.options.throwOnError,[s.error]))throw s.error;return{...s,mutate:a,mutateAsync:s.mutate}}},777:function(e,t,r){"use strict";let n;r.d(t,{a:function(){return P}});var i=r(6474),o=r(7037),s=r(6888),a=r(7506),c=r(3820),u=r(4139),l=class extends a.l{constructor(e,t){super(),this.options=t,this.#C=e,this.#P=null,this.#I=(0,c.O)(),this.options.experimental_prefetchInRender||this.#I.reject(Error("experimental_prefetchInRender feature flag is not enabled")),this.bindMethods(),this.setOptions(t)}#C;#R=void 0;#j=void 0;#E=void 0;#N;#D;#I;#P;#L;#A;#M;#z;#F;#U;#K=new Set;bindMethods(){this.refetch=this.refetch.bind(this)}onSubscribe(){1===this.listeners.size&&(this.#R.addObserver(this),d(this.#R,this.options)?this.#W():this.updateResult(),this.#q())}onUnsubscribe(){this.hasListeners()||this.destroy()}shouldFetchOnReconnect(){return h(this.#R,this.options,this.options.refetchOnReconnect)}shouldFetchOnWindowFocus(){return h(this.#R,this.options,this.options.refetchOnWindowFocus)}destroy(){this.listeners=new Set,this.#Z(),this.#Q(),this.#R.removeObserver(this)}setOptions(e,t){let r=this.options,n=this.#R;if(this.options=this.#C.defaultQueryOptions(e),void 0!==this.options.enabled&&"boolean"!=typeof this.options.enabled&&"function"!=typeof this.options.enabled&&"boolean"!=typeof(0,u.Nc)(this.options.enabled,this.#R))throw Error("Expected enabled to be a boolean or a callback that returns a boolean");this.#V(),this.#R.setOptions(this.options),r._defaulted&&!(0,u.VS)(this.options,r)&&this.#C.getQueryCache().notify({type:"observerOptionsUpdated",query:this.#R,observer:this});let i=this.hasListeners();i&&p(this.#R,n,this.options,r)&&this.#W(),this.updateResult(t),i&&(this.#R!==n||(0,u.Nc)(this.options.enabled,this.#R)!==(0,u.Nc)(r.enabled,this.#R)||(0,u.KC)(this.options.staleTime,this.#R)!==(0,u.KC)(r.staleTime,this.#R))&&this.#G();let o=this.#H();i&&(this.#R!==n||(0,u.Nc)(this.options.enabled,this.#R)!==(0,u.Nc)(r.enabled,this.#R)||o!==this.#U)&&this.#X(o)}getOptimisticResult(e){let t=this.#C.getQueryCache().build(this.#C,e),r=this.createResult(t,e);return(0,u.VS)(this.getCurrentResult(),r)||(this.#E=r,this.#D=this.options,this.#N=this.#R.state),r}getCurrentResult(){return this.#E}trackResult(e,t){let r={};return Object.keys(e).forEach(n=>{Object.defineProperty(r,n,{configurable:!1,enumerable:!0,get:()=>(this.trackProp(n),t?.(n),e[n])})}),r}trackProp(e){this.#K.add(e)}getCurrentQuery(){return this.#R}refetch({...e}={}){return this.fetch({...e})}fetchOptimistic(e){let t=this.#C.defaultQueryOptions(e),r=this.#C.getQueryCache().build(this.#C,t);return r.fetch().then(()=>this.createResult(r,t))}fetch(e){return this.#W({...e,cancelRefetch:e.cancelRefetch??!0}).then(()=>(this.updateResult(),this.#E))}#W(e){this.#V();let t=this.#R.fetch(this.options,e);return e?.throwOnError||(t=t.catch(u.ZT)),t}#G(){this.#Z();let e=(0,u.KC)(this.options.staleTime,this.#R);if(u.sk||this.#E.isStale||!(0,u.PN)(e))return;let t=(0,u.Kp)(this.#E.dataUpdatedAt,e);this.#z=setTimeout(()=>{this.#E.isStale||this.updateResult()},t+1)}#H(){return("function"==typeof this.options.refetchInterval?this.options.refetchInterval(this.#R):this.options.refetchInterval)??!1}#X(e){this.#Q(),this.#U=e,!u.sk&&!1!==(0,u.Nc)(this.options.enabled,this.#R)&&(0,u.PN)(this.#U)&&0!==this.#U&&(this.#F=setInterval(()=>{(this.options.refetchIntervalInBackground||i.j.isFocused())&&this.#W()},this.#U))}#q(){this.#G(),this.#X(this.#H())}#Z(){this.#z&&(clearTimeout(this.#z),this.#z=void 0)}#Q(){this.#F&&(clearInterval(this.#F),this.#F=void 0)}createResult(e,t){let r;let n=this.#R,i=this.options,o=this.#E,a=this.#N,l=this.#D,h=e!==n?e.state:this.#j,{state:m}=e,y={...m},v=!1;if(t._optimisticResults){let r=this.hasListeners(),o=!r&&d(e,t),a=r&&p(e,n,t,i);(o||a)&&(y={...y,...(0,s.z)(m.data,e.options)}),"isRestoring"===t._optimisticResults&&(y.fetchStatus="idle")}let{error:g,errorUpdatedAt:b,status:w}=y;if(t.select&&void 0!==y.data){if(o&&y.data===a?.data&&t.select===this.#L)r=this.#A;else try{this.#L=t.select,r=t.select(y.data),r=(0,u.oE)(o?.data,r,t),this.#A=r,this.#P=null}catch(e){this.#P=e}}else r=y.data;if(void 0!==t.placeholderData&&void 0===r&&"pending"===w){let e;if(o?.isPlaceholderData&&t.placeholderData===l?.placeholderData)e=o.data;else if(e="function"==typeof t.placeholderData?t.placeholderData(this.#M?.state.data,this.#M):t.placeholderData,t.select&&void 0!==e)try{e=t.select(e),this.#P=null}catch(e){this.#P=e}void 0!==e&&(w="success",r=(0,u.oE)(o?.data,e,t),v=!0)}this.#P&&(g=this.#P,r=this.#A,b=Date.now(),w="error");let x="fetching"===y.fetchStatus,k="pending"===w,C="error"===w,E=k&&x,T=void 0!==r,O={status:w,fetchStatus:y.fetchStatus,isPending:k,isSuccess:"success"===w,isError:C,isInitialLoading:E,isLoading:E,data:r,dataUpdatedAt:y.dataUpdatedAt,error:g,errorUpdatedAt:b,failureCount:y.fetchFailureCount,failureReason:y.fetchFailureReason,errorUpdateCount:y.errorUpdateCount,isFetched:y.dataUpdateCount>0||y.errorUpdateCount>0,isFetchedAfterMount:y.dataUpdateCount>h.dataUpdateCount||y.errorUpdateCount>h.errorUpdateCount,isFetching:x,isRefetching:x&&!k,isLoadingError:C&&!T,isPaused:"paused"===y.fetchStatus,isPlaceholderData:v,isRefetchError:C&&T,isStale:f(e,t),refetch:this.refetch,promise:this.#I};if(this.options.experimental_prefetchInRender){let t=e=>{"error"===O.status?e.reject(O.error):void 0!==O.data&&e.resolve(O.data)},r=()=>{t(this.#I=O.promise=(0,c.O)())},i=this.#I;switch(i.status){case"pending":e.queryHash===n.queryHash&&t(i);break;case"fulfilled":("error"===O.status||O.data!==i.value)&&r();break;case"rejected":("error"!==O.status||O.error!==i.reason)&&r()}}return O}updateResult(e){let t=this.#E,r=this.createResult(this.#R,this.options);if(this.#N=this.#R.state,this.#D=this.options,void 0!==this.#N.data&&(this.#M=this.#R),(0,u.VS)(r,t))return;this.#E=r;let n={};e?.listeners!==!1&&(()=>{if(!t)return!0;let{notifyOnChangeProps:e}=this.options,r="function"==typeof e?e():e;if("all"===r||!r&&!this.#K.size)return!0;let n=new Set(r??this.#K);return this.options.throwOnError&&n.add("error"),Object.keys(this.#E).some(e=>this.#E[e]!==t[e]&&n.has(e))})()&&(n.listeners=!0),this.#_({...n,...e})}#V(){let e=this.#C.getQueryCache().build(this.#C,this.options);if(e===this.#R)return;let t=this.#R;this.#R=e,this.#j=e.state,this.hasListeners()&&(t?.removeObserver(this),e.addObserver(this))}onQueryUpdate(){this.updateResult(),this.hasListeners()&&this.#q()}#_(e){o.V.batch(()=>{e.listeners&&this.listeners.forEach(e=>{e(this.#E)}),this.#C.getQueryCache().notify({query:this.#R,type:"observerResultsUpdated"})})}};function d(e,t){return!1!==(0,u.Nc)(t.enabled,e)&&void 0===e.state.data&&!("error"===e.state.status&&!1===t.retryOnMount)||void 0!==e.state.data&&h(e,t,t.refetchOnMount)}function h(e,t,r){if(!1!==(0,u.Nc)(t.enabled,e)){let n="function"==typeof r?r(e):r;return"always"===n||!1!==n&&f(e,t)}return!1}function p(e,t,r,n){return(e!==t||!1===(0,u.Nc)(n.enabled,e))&&(!r.suspense||"error"!==e.state.status)&&f(e,r)}function f(e,t){return!1!==(0,u.Nc)(t.enabled,e)&&e.isStaleByTime((0,u.KC)(t.staleTime,e))}var m=r(7294),y=r(202);r(5893);var v=m.createContext((n=!1,{clearReset:()=>{n=!1},reset:()=>{n=!0},isReset:()=>n})),g=()=>m.useContext(v),b=r(6290),w=(e,t)=>{(e.suspense||e.throwOnError||e.experimental_prefetchInRender)&&!t.isReset()&&(e.retryOnMount=!1)},x=e=>{m.useEffect(()=>{e.clearReset()},[e])},k=({result:e,errorResetBoundary:t,throwOnError:r,query:n})=>e.isError&&!t.isReset()&&!e.isFetching&&n&&(0,b.L)(r,[e.error,n]),C=m.createContext(!1),E=()=>m.useContext(C);C.Provider;var T=e=>{e.suspense&&(void 0===e.staleTime&&(e.staleTime=1e3),"number"==typeof e.gcTime&&(e.gcTime=Math.max(e.gcTime,1e3)))},O=(e,t)=>e.isLoading&&e.isFetching&&!t,S=(e,t)=>e?.suspense&&t.isPending,_=(e,t,r)=>t.fetchOptimistic(e).catch(()=>{r.clearReset()});function P(e,t){return function(e,t,r){let n=(0,y.NL)(r),i=E(),s=g(),a=n.defaultQueryOptions(e);n.getDefaultOptions().queries?._experimental_beforeQuery?.(a),a._optimisticResults=i?"isRestoring":"optimistic",T(a),w(a,s),x(s);let c=!n.getQueryCache().get(a.queryHash),[l]=m.useState(()=>new t(n,a)),d=l.getOptimisticResult(a);if(m.useSyncExternalStore(m.useCallback(e=>{let t=i?b.Z:l.subscribe(o.V.batchCalls(e));return l.updateResult(),t},[l,i]),()=>l.getCurrentResult(),()=>l.getCurrentResult()),m.useEffect(()=>{l.setOptions(a,{listeners:!1})},[a,l]),S(a,d))throw _(a,l,s);if(k({result:d,errorResetBoundary:s,throwOnError:a.throwOnError,query:n.getQueryCache().get(a.queryHash)}))throw d.error;if(n.getDefaultOptions().queries?._experimental_afterQuery?.(a,d),a.experimental_prefetchInRender&&!u.sk&&O(d,i)){let e=c?_(a,l,s):n.getQueryCache().get(a.queryHash)?.promise;e?.catch(b.Z).finally(()=>{l.updateResult()})}return a.notifyOnChangeProps?d:l.trackResult(d)}(e,l,t)}},6290:function(e,t,r){"use strict";function n(e,t){return"function"==typeof e?e(...t):!!e}function i(){}r.d(t,{L:function(){return n},Z:function(){return i}})},5139:function(e,t,r){"use strict";r.d(t,{j:function(){return o}});let n=e=>"boolean"==typeof e?"".concat(e):0===e?"0":e,i=function(){for(var e,t,r=0,n="";r<arguments.length;)(e=arguments[r++])&&(t=function e(t){var r,n,i="";if("string"==typeof t||"number"==typeof t)i+=t;else if("object"==typeof t){if(Array.isArray(t))for(r=0;r<t.length;r++)t[r]&&(n=e(t[r]))&&(i&&(i+=" "),i+=n);else for(r in t)t[r]&&(i&&(i+=" "),i+=r)}return i}(e))&&(n&&(n+=" "),n+=t);return n},o=(e,t)=>r=>{var o;if((null==t?void 0:t.variants)==null)return i(e,null==r?void 0:r.class,null==r?void 0:r.className);let{variants:s,defaultVariants:a}=t,c=Object.keys(s).map(e=>{let t=null==r?void 0:r[e],i=null==a?void 0:a[e];if(null===t)return null;let o=n(t)||n(i);return s[e][o]}),u=r&&Object.entries(r).reduce((e,t)=>{let[r,n]=t;return void 0===n||(e[r]=n),e},{});return i(e,c,null==t?void 0:null===(o=t.compoundVariants)||void 0===o?void 0:o.reduce((e,t)=>{let{class:r,className:n,...i}=t;return Object.entries(i).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...a,...u}[t]):({...a,...u})[t]===r})?[...e,r,n]:e},[]),null==r?void 0:r.class,null==r?void 0:r.className)}}},function(e){var t=function(t){return e(e.s=t)};e.O(0,[774,179],function(){return t(6840),t(3079)}),_N_E=e.O()}]);