(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{8312:function(e,a,t){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return t(1337)}])},1337:function(e,a,t){"use strict";t.r(a),t.d(a,{__N_SSG:function(){return e1},default:function(){return e2}});var s=t(5893),r=t(9008),i=t.n(r),n=t(3749),l=t(7294),o=t(9232),d=t(5570),c=t(6263),m=t(5139),x=t(7009);let h=(0,m.j)("relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground",{variants:{variant:{default:"bg-background text-foreground",destructive:"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive"}},defaultVariants:{variant:"default"}}),u=l.forwardRef((e,a)=>{let{className:t,variant:r,...i}=e;return(0,s.jsx)("div",{ref:a,role:"alert",className:(0,x.cn)(h({variant:r}),t),...i})});u.displayName="Alert";let g=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("h5",{ref:a,className:(0,x.cn)("mb-1 font-medium leading-none tracking-tight",t),...r})});g.displayName="AlertTitle";let p=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{ref:a,className:(0,x.cn)("text-sm [&_p]:leading-relaxed",t),...r})});p.displayName="AlertDescription";var f=t(893),v=t(6194),b=t(8806),j=t(8453),y=t(1601),N=t(2629),w=t(341),k=t(1793),D=t(1475),C=t(6215),z=t(6127),S=t(8655),T=t(2125),R=t(3058);function Z(e){let{onSearch:a,isLoading:t}=e,[r,i]=(0,l.useState)(""),[n,m]=(0,l.useState)(""),[x,h]=(0,l.useState)(!1),[g,Z]=(0,l.useState)(""),A=(0,l.useRef)(null),L=(0,c.CP)(),_={business:(0,s.jsx)(f.Z,{className:"w-4 h-4"}),ecommerce:(0,s.jsx)(v.Z,{className:"w-4 h-4"}),restaurant:(0,s.jsx)(b.Z,{className:"w-4 h-4"}),tech:(0,s.jsx)(j.Z,{className:"w-4 h-4"}),health:(0,s.jsx)(y.Z,{className:"w-4 h-4"}),fitness:(0,s.jsx)(N.Z,{className:"w-4 h-4"}),photography:(0,s.jsx)(w.Z,{className:"w-4 h-4"}),app:(0,s.jsx)(k.Z,{className:"w-4 h-4"}),creative:(0,s.jsx)(D.Z,{className:"w-4 h-4"}),realestate:(0,s.jsx)(C.Z,{className:"w-4 h-4"})},I=e=>e.trim()?e.trim().length<2?(m("Search term must be at least 2 characters"),!1):(m(""),!0):(m("Please enter a search term"),!1),F=e=>{i((0,c.Io)(e)),Z(e),A.current&&A.current.focus(),m("")};(0,l.useEffect)(()=>{g&&r!==(0,c.Io)(g)&&Z("")},[r,g]);let Y=Array.from({length:10},(e,a)=>(0,s.jsx)("div",{className:"absolute rounded-full bg-white/80 w-1 h-1 animate-particles",style:{left:"".concat(50+35*Math.cos(a*Math.PI/5),"%"),top:"50%",animationDelay:"".concat(.1*a,"s")}},a));return(0,s.jsxs)("form",{className:"max-w-3xl mx-auto",onSubmit:e=>{e.preventDefault(),I(r)&&(a(r.trim()),A.current&&A.current.blur())},children:[(0,s.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4",children:[(0,s.jsxs)("div",{className:"flex-grow relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 -z-10 rounded-lg transition-all duration-300 ".concat(x?"bg-gradient-to-r from-primary-100/50 to-secondary-100/50 blur-md":"")}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)(o.I,{ref:A,id:"ideaInput",value:r,onChange:e=>{i(e.target.value),n&&m(""),g&&Z("")},onFocus:()=>h(!0),onBlur:()=>h(!1),className:"w-full px-3 sm:px-5 py-5 sm:py-7 text-sm sm:text-base pr-12 transition-all duration-300 border-2 shadow-sm ".concat(n?"border-red-500 focus-visible:ring-red-500":x?"border-primary-500 shadow-primary-200/40":""),placeholder:"e.g. sustainable fashion marketplace","aria-invalid":!!n,"aria-describedby":n?"search-error":void 0}),(0,s.jsx)(z.Z,{className:"absolute right-4 top-1/2 -translate-y-1/2 w-5 h-5 transition-all duration-300 ".concat(x?"text-primary-500":"text-gray-400")})]}),n&&(0,s.jsxs)(u,{variant:"destructive",className:"mt-2 py-2 animate-slide-up",children:[(0,s.jsx)(S.Z,{className:"h-4 w-4"}),(0,s.jsx)(p,{id:"search-error",className:"text-sm",children:n})]})]}),(0,s.jsx)("div",{className:"flex items-end",children:(0,s.jsx)(d.z,{type:"submit",className:"px-4 sm:px-6 py-5 sm:py-7 w-full sm:w-auto relative overflow-hidden transition-all duration-300 effect-3d ".concat(t?"bg-gradient-to-r from-primary-600 to-secondary-600":"bg-gradient-to-r from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700"),disabled:!r.trim()||t,children:t?(0,s.jsxs)(s.Fragment,{children:[Y,(0,s.jsx)(T.Z,{className:"w-5 h-5 mr-2 animate-spin"}),"Creating..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(R.Z,{className:"w-5 h-5 mr-2"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Create Creative Domains"}),(0,s.jsx)("span",{className:"xs:hidden",children:"Create"})]})})})]}),(0,s.jsxs)("div",{className:"mt-4 sm:mt-6 flex flex-wrap gap-1.5 sm:gap-2",children:[(0,s.jsx)("span",{className:"text-xs sm:text-sm text-gray-600 mr-1 pt-1 w-full sm:w-auto mb-1 sm:mb-0",children:"Popular examples:"}),L.map(e=>(0,s.jsxs)(d.z,{type:"button",variant:"outline",size:"sm",className:"text-xs sm:text-sm h-auto px-2 sm:px-3 py-1 flex items-center gap-1 sm:gap-1.5 transition-all duration-300 ".concat(g===e.name?"bg-gradient-to-r from-primary-100 to-primary-50 text-primary-700 border-primary-300 font-medium shadow-sm":"hover:border-primary-300 hover:text-primary-700"),onClick:()=>F(e.name),children:[_[e.name.toLowerCase()]||(0,s.jsx)(f.Z,{className:"w-3 h-3"}),e.displayName]},e.name))]})]})}var A=t(5121),L=t(8865),_=t(7865),I=t(6944),F=t(698),Y=t(9527);function E(e){let{domain:a,index:t=0}=e,[r,i]=(0,l.useState)(!1),o=(0,l.useRef)(null),{isFavorite:m,toggleFavorite:x}=(0,I.r)(),{trackEvent:h}=(0,Y.z)(),u=a.available?"bg-gradient-to-r from-green-500 to-emerald-600 text-white":"bg-gradient-to-r from-red-500 to-rose-600 text-white",g=a.available?"Available":"Unavailable",p=a.available?"":"opacity-75",f=a.available?"Register Now":"Not Available",v=a.available?"Registration":"Registered on",b=a.available?"".concat((0,c.T4)(void 0!==a.price?a.price:null),"/year"):a.registrationDate||"Unknown",j=()=>{a.available&&i(!0)};return(0,s.jsxs)("div",{className:"relative transform transition-all duration-500 animate-scale-in",style:{animationDelay:"".concat(.1*t,"s")},ref:o,children:[(0,s.jsx)(F.G,{show:r&&a.available,onComplete:()=>i(!1)}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"".concat(p," transition-all duration-300 relative overflow-hidden rounded-xl shadow-lg"),children:(0,s.jsxs)(n.Zb,{className:"border-0 bg-transparent overflow-hidden h-full",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center border-b p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.z,{variant:"ghost",size:"icon",className:"h-7 w-7 rounded-full ".concat(m(a.fullDomain)?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-400"),onClick:()=>x(a),title:m(a.fullDomain)?"Remove from favorites":"Add to favorites",children:(0,s.jsx)(y.Z,{className:"h-4 w-4",fill:m(a.fullDomain)?"currentColor":"none"})}),(0,s.jsx)("h3",{className:"font-medium text-base sm:text-lg transition-all duration-300 break-words",children:a.fullDomain})]}),(0,s.jsxs)(A.C,{className:"".concat(u," shadow transition-all duration-300"),children:[a.available&&(0,s.jsx)(L.Z,{className:"w-3 h-3 mr-1"}),g]})]}),(0,s.jsxs)(n.aY,{className:"p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3 sm:mb-4",children:[(0,s.jsx)("span",{className:"text-xs sm:text-sm text-gray-600 transition-all duration-300",children:v}),(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium transition-all duration-300",children:b})]}),(0,s.jsx)("div",{children:a.available?(0,s.jsxs)(d.z,{className:"w-full bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center justify-center gap-1 transition-all duration-300",variant:"outline",size:"sm",onClick:()=>{j(),h("domain_registration_click",{domain_name:a.name,tld:a.tld,price:a.price}),window.open("https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D".concat(encodeURIComponent(a.fullDomain)))},children:[(0,s.jsx)(_.Z,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:f}),(0,s.jsxs)("span",{className:"text-xs opacity-75 ml-1",children:[(0,c.T4)(void 0!==a.price?a.price:null),"/yr"]})]}):(0,s.jsx)(d.z,{className:"w-full transition-all duration-300 overflow-hidden relative",variant:"outline",disabled:!0,children:(0,s.jsx)("span",{className:"relative z-10",children:f})})})]})]})})})]})}var P=t(5432);function M(e){let{groupedDomain:a,index:t=0}=e,[r,i]=(0,l.useState)(!1),[o,m]=(0,l.useState)(!1),x=(0,l.useRef)(null),{isFavorite:h,toggleFavorite:u}=(0,I.r)(),g=(()=>{let e=a.domains.find(e=>"com"===e.tld&&e.available);return e?e:a.domains.find(e=>e.available)||a.domains[0]})(),p=a.domains.length>1,f=a.availableTlds.length>0,v=a.domains.filter(e=>e.fullDomain!==g.fullDomain),b=f?"bg-gradient-to-r from-green-500 to-emerald-600 text-white":"bg-gradient-to-r from-red-500 to-rose-600 text-white",j=f?"".concat(a.availableTlds.length," TLD").concat(1!==a.availableTlds.length?"s":""," Available"):"Unavailable",N=f?"":"opacity-75",w=f?"Starting at":"Registered",k=f?"".concat((0,c.T4)(void 0!==a.lowestPrice?a.lowestPrice:null),"/year"):"Unavailable",D=()=>{f&&i(!0)},C=()=>{m(!o)};return(0,s.jsxs)("div",{className:"relative transform transition-all duration-500 animate-scale-in",style:{animationDelay:"".concat(.1*t,"s")},ref:x,children:[(0,s.jsx)(F.G,{show:r&&f,onComplete:()=>i(!1)}),(0,s.jsx)("div",{className:"relative",children:(0,s.jsx)("div",{className:"".concat(N," transition-all duration-300 relative overflow-hidden rounded-xl shadow-lg"),children:(0,s.jsxs)(n.Zb,{className:"border-0 bg-transparent overflow-hidden h-full",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center border-b p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,s.jsxs)("div",{className:"flex items-center gap-2",children:[(0,s.jsx)(d.z,{variant:"ghost",size:"icon",className:"h-7 w-7 rounded-full ".concat(h(g.fullDomain)?"text-red-500 hover:text-red-600":"text-gray-400 hover:text-red-400"),onClick:()=>u(g,v),title:h(g.fullDomain)?"Remove from favorites":"Add to favorites",children:(0,s.jsx)(y.Z,{className:"h-4 w-4",fill:h(g.fullDomain)?"currentColor":"none"})}),(0,s.jsxs)("h3",{className:"font-medium text-base sm:text-lg transition-all duration-300 break-words",children:[g.fullDomain,p&&(0,s.jsxs)("span",{className:"text-gray-500 text-sm ml-1",children:["(+",a.domains.length-1," more)"]})]})]}),(0,s.jsxs)(A.C,{className:"".concat(b," shadow transition-all duration-300"),children:[f&&(0,s.jsx)(L.Z,{className:"w-3 h-3 mr-1"}),j]})]}),(0,s.jsxs)(n.aY,{className:"p-3 sm:p-4 relative z-10 transition-all duration-300",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-3 sm:mb-4",children:[(0,s.jsx)("span",{className:"text-xs sm:text-sm text-gray-600 transition-all duration-300",children:w}),(0,s.jsx)("span",{className:"text-xs sm:text-sm font-medium transition-all duration-300",children:k})]}),f&&(0,s.jsxs)("div",{className:"mb-3 sm:mb-4",children:[(0,s.jsxs)("div",{className:"flex flex-wrap gap-1.5 sm:gap-2 mb-2",children:[a.availableTlds.slice(0,o?void 0:3).map(e=>{let t=a.domains.find(a=>a.tld===e),r=(null==t?void 0:t.price)!==void 0?(0,c.T4)(t.price):"N/A";return(0,s.jsxs)(d.z,{variant:"outline",size:"sm",className:"bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center gap-1 text-xs sm:text-sm py-1 h-auto",onClick:()=>{D(),window.open("https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D".concat(encodeURIComponent(t?t.fullDomain:"".concat(a.baseName,".").concat(e))))},children:[(0,s.jsx)(_.Z,{className:"w-3 h-3"}),(0,s.jsxs)("span",{children:[".",e]}),(0,s.jsxs)("span",{className:"text-xs opacity-75 ml-1",children:[r,"/yr"]})]},e)}),!o&&a.availableTlds.length>3&&(0,s.jsxs)(d.z,{variant:"ghost",size:"sm",className:"text-gray-600 hover:bg-gray-100 hover:text-gray-900",onClick:C,children:["+",a.availableTlds.length-3," more"]})]}),a.availableTlds.length>3&&o&&(0,s.jsxs)(d.z,{variant:"ghost",size:"sm",className:"w-full text-xs flex items-center justify-center py-1 text-gray-600 hover:bg-gray-100 hover:text-gray-900",onClick:C,children:[(0,s.jsx)(P.Z,{className:"w-3 h-3 mr-1"}),"Show fewer options"]})]}),!f&&(0,s.jsx)("div",{children:(0,s.jsx)(d.z,{className:"w-full transition-all duration-300 overflow-hidden relative",variant:"outline",disabled:!0,children:(0,s.jsx)("span",{className:"relative z-10",children:"Not Available"})})})]})]})})})]})}var G=t(8030),V=t(777),q=t(8486);function O(){let{data:e,isLoading:a,error:t,refetch:s}=(0,V.a)({queryKey:["/api/rate-limit"],refetchOnWindowFocus:!0,refetchInterval:6e4,queryFn:async e=>{let{queryKey:a}=e;try{let e=await (0,q.SC)(a[0]),t=await e.json();return e.status,t}catch(e){throw e}}}),r="rateLimited"in(e||{})?e.rateLimitInfo:null==e?void 0:e.rateLimitInfo;return{rateLimitInfo:r,isLimited:"rateLimited"in(e||{})||(null==r?void 0:r.isLimited)||!1,isLoading:a,error:t,refetch:s}}var B=t(3766);function U(e){let{searchTerm:a="",searchStatus:t="idle",onGenerateMore:r}=e,i=(0,c.tW)(a),{rateLimitInfo:l,isLimited:o}=O();return(0,s.jsx)(n.Zb,{className:"bg-white shadow-md mb-10 overflow-hidden border-t-4 border-t-primary-500",children:(0,s.jsxs)(n.aY,{className:"p-6",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-primary-100 flex items-center justify-center mr-3 animate-float-slow",children:(0,s.jsx)(G.Z,{className:"w-5 h-5 text-primary-600"})}),(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Try these popular domain patterns"})]}),(0,s.jsx)("p",{className:"text-gray-600 mb-6 max-w-2xl",children:"While these common patterns can be useful, our AI excels at creating truly unique, creative domain names that go beyond conventional patterns to perfectly capture your ideas essence."}),(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-5",children:i.map((e,a)=>(0,s.jsxs)("div",{className:"bg-gradient-to-br from-white to-gray-50 border border-gray-200 p-5 rounded-xl hover:shadow-md transition-all duration-300 hover:-translate-y-1 effect-3d animate-scale-in",style:{animationDelay:"".concat(.1*a,"s")},children:[(0,s.jsx)("h3",{className:"font-medium text-lg mb-2 text-gradient",children:e.title}),(0,s.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:e.description}),(0,s.jsx)("div",{className:"flex flex-wrap gap-2 mt-2",children:e.examples.map((e,a)=>(0,s.jsx)("span",{className:"text-xs bg-white border border-primary-200 text-primary-700 px-2 py-1 rounded-full shadow-sm",children:e},a))})]},a))}),r&&(0,s.jsxs)("div",{className:"mt-8 flex flex-col items-center",children:[o&&l?(0,s.jsxs)("div",{className:"mb-4 p-4 bg-red-50 border border-red-200 rounded-lg text-center max-w-md",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center mb-2",children:[(0,s.jsx)(S.Z,{className:"h-5 w-5 text-red-500 mr-2"}),(0,s.jsx)("span",{className:"font-medium text-red-700",children:"Rate Limit Reached"})]}),(0,s.jsxs)("p",{className:"text-sm text-red-600 mb-2",children:["You've reached the limit of ",l.total," searches per hour."]}),(0,s.jsxs)("p",{className:"text-xs text-red-500",children:["Try again ",(0,B.Q)(new Date(l.resetTime),{addSuffix:!0})]})]}):null,(0,s.jsx)(d.z,{onClick:r,disabled:"generating"===t||o,className:"bg-gradient-to-r ".concat(o?"from-gray-400 to-gray-500 cursor-not-allowed":"from-primary-600 to-secondary-600 hover:from-primary-700 hover:to-secondary-700"," shadow-md hover:shadow-lg transition-all"),children:"generating"===t?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin h-4 w-4 mr-2 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Crafting Creative Domain Ideas..."]}):o?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(S.Z,{className:"w-4 h-4 mr-2"}),"Rate Limit Reached"]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(R.Z,{className:"w-4 h-4 mr-2"}),"Generate Creative & Unique Domains"]})})]})]})})}var W=t(4976),Q=t(8178),H=t(3243);function K(e){let{domains:a,groupedDomains:t=[],isLoading:r=!1,searchStatus:i="idle",onGenerateMore:n,onRefresh:o}=e,[c,m]=(0,l.useState)("grouped"),[x,h]=(0,l.useState)(!1),{rateLimitInfo:u,isLimited:g}=O();(0,l.useEffect)(()=>{h(!1);let e=setTimeout(()=>{h(!0)},100);return()=>clearTimeout(e)},[a]);let p=a.filter(e=>e.available),f=a.filter(e=>!e.available);return 0!==a.length||r?(0,s.jsxs)("div",{className:"transition-opacity duration-500 ".concat(x?"opacity-100":"opacity-0"),children:[p.length>0&&(0,s.jsxs)("div",{className:"mb-8",children:[p.length>0&&(0,s.jsxs)("div",{className:"flex items-center justify-between mb-4",children:[(0,s.jsxs)("div",{className:"flex flex-wrap items-center",children:[(0,s.jsxs)("h3",{className:"text-base sm:text-lg font-medium text-gray-800 flex items-center",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-full w-5 sm:w-6 h-5 sm:h-6 flex items-center justify-center text-xs mr-2",children:p.length}),"Available Domains"]}),(0,s.jsx)("div",{className:"ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded bg-gradient-to-r from-green-100 to-emerald-100 text-green-800",children:"Ready to register"})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsxs)(d.z,{variant:"grouped"===c?"default":"outline",size:"sm",className:"flex items-center gap-1 ".concat("grouped"===c?"bg-blue-600 text-white hover:bg-blue-700":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>m("grouped"),title:"Group by domain name",children:[(0,s.jsx)(Q.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Group by name"})]}),(0,s.jsxs)(d.z,{variant:"individual"===c?"default":"outline",size:"sm",className:"flex items-center gap-1 ".concat("individual"===c?"bg-blue-600 text-white hover:bg-blue-700":"text-gray-700 hover:bg-gray-100 hover:text-gray-900"),onClick:()=>m("individual"),title:"Show all domains",children:[(0,s.jsx)(H.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Show all domains"})]})]})]}),"grouped"===c&&t&&t.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:t.filter(e=>e.availableTlds.length>0).map((e,a)=>(0,s.jsx)(M,{groupedDomain:e,index:a},e.baseName))}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:p.map((e,a)=>(0,s.jsx)(E,{domain:e,index:a},e.fullDomain))}),n&&(0,s.jsx)("div",{className:"mt-8 flex justify-center",children:(0,s.jsx)(d.z,{onClick:n,variant:"generating"===i?"default":"outline",disabled:r||"generating"===i,className:"effect-3d transition-all duration-300 ".concat("generating"===i?"bg-gradient-to-r from-primary-600 to-secondary-600 text-white shadow-md":"border-primary-200 hover:border-primary-300 hover:bg-primary-50"),children:"generating"===i?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("svg",{className:"animate-spin h-4 w-4 mr-2 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"2"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Generating More Suggestions..."]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(R.Z,{className:"w-4 h-4 mr-2 text-primary-600"}),(0,s.jsx)("span",{className:"hidden xs:inline",children:"Generate More Suggestions"}),(0,s.jsx)("span",{className:"xs:hidden",children:"More Suggestions"})]})})})]}),f.length>0&&(0,s.jsxs)("div",{children:[(0,s.jsxs)("div",{className:"flex flex-wrap items-center mb-4",children:[(0,s.jsxs)("h3",{className:"text-base sm:text-lg font-medium text-gray-800 flex items-center",children:[(0,s.jsx)("span",{className:"bg-gradient-to-r from-red-500 to-rose-600 text-white rounded-full w-5 sm:w-6 h-5 sm:h-6 flex items-center justify-center text-xs mr-2",children:f.length}),"Unavailable Domains"]}),(0,s.jsx)("div",{className:"ml-2 inline-flex items-center justify-center px-2 py-1 text-xs font-medium rounded bg-gradient-to-r from-red-100 to-rose-100 text-rose-800",children:"Already registered"})]}),"grouped"===c&&t&&t.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:t.filter(e=>0===e.availableTlds.length&&e.domains.length>0).map((e,a)=>(0,s.jsx)(M,{groupedDomain:e,index:a+p.length},e.baseName))}):(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:f.map((e,a)=>(0,s.jsx)(E,{domain:e,index:a+p.length},e.fullDomain))})]}),0===p.length&&!r&&(0,s.jsxs)(s.Fragment,{children:[g&&u?(0,s.jsxs)("div",{className:"bg-white shadow-md rounded-lg p-6 mb-8 border-t-4 border-t-red-500",children:[(0,s.jsxs)("div",{className:"flex items-center mb-4",children:[(0,s.jsx)("div",{className:"w-10 h-10 rounded-full bg-red-100 flex items-center justify-center mr-3",children:(0,s.jsx)(S.Z,{className:"w-5 h-5 text-red-600"})}),(0,s.jsx)("h2",{className:"text-xl font-bold text-gray-800",children:"Rate Limit Reached"})]}),(0,s.jsxs)("p",{className:"text-gray-600 mb-4",children:["You've reached the limit of ",u.total," searches per hour. Please try again ",(0,B.Q)(new Date(u.resetTime),{addSuffix:!0}),"."]}),(0,s.jsx)("div",{className:"bg-red-50 p-4 rounded-lg border border-red-100 mb-4",children:(0,s.jsx)("p",{className:"text-sm text-red-700",children:"This limit helps us prevent abuse and ensure the service remains available for everyone."})})]}):null,(0,s.jsx)(U,{onGenerateMore:n,searchStatus:i})]})]}):(0,s.jsxs)("div",{className:"text-center py-12",children:[(0,s.jsx)("div",{className:"animate-float-slow mb-4",children:(0,s.jsx)("svg",{className:"mx-auto w-24 h-24 text-gray-300",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"1.5",d:"M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2h2.5M15 11h3.5a2 2 0 012 2v1a2 2 0 01-2 2h-5.5M3 16.5h18M3 7.5h18"})})}),(0,s.jsx)("h3",{className:"text-xl font-semibold text-gray-700 mb-2",children:"No domains found"}),(0,s.jsx)("p",{className:"text-gray-500 max-w-md mx-auto mb-6",children:"Try searching with different keywords or adjusting your filters to find relevant domain suggestions."}),o&&(0,s.jsxs)(d.z,{onClick:o,className:"effect-3d",children:[(0,s.jsx)(W.Z,{className:"w-4 h-4 mr-2"}),"Try Again"]})]})}var $=t(1603),J=t(3835),X=t(2190),ee=t(3879),ea=t(2854);ea.fC,ea.xz;let et=ea.h_;ea.x8;let es=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ea.aV,{ref:a,className:(0,x.cn)("fixed inset-0 z-50 bg-black/80  data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0",t),...r})});es.displayName=ea.aV.displayName,l.forwardRef((e,a)=>{let{className:t,children:r,...i}=e;return(0,s.jsxs)(et,{children:[(0,s.jsx)(es,{}),(0,s.jsxs)(ea.VY,{ref:a,className:(0,x.cn)("fixed left-[50%] top-[50%] z-50 grid w-full max-w-lg translate-x-[-50%] translate-y-[-50%] gap-4 border bg-background p-6 shadow-lg duration-200 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[state=closed]:slide-out-to-left-1/2 data-[state=closed]:slide-out-to-top-[48%] data-[state=open]:slide-in-from-left-1/2 data-[state=open]:slide-in-from-top-[48%] sm:rounded-lg",t),...i,children:[r,(0,s.jsxs)(ea.x8,{className:"absolute right-4 top-4 rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground",children:[(0,s.jsx)(X.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}).displayName=ea.VY.displayName,l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ea.Dx,{ref:a,className:(0,x.cn)("text-lg font-semibold leading-none tracking-tight",t),...r})}).displayName=ea.Dx.displayName,l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ea.dk,{ref:a,className:(0,x.cn)("text-sm text-muted-foreground",t),...r})}).displayName=ea.dk.displayName;let er=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ee.mY,{ref:a,className:(0,x.cn)("flex h-full w-full flex-col overflow-hidden rounded-md bg-popover text-popover-foreground",t),...r})});er.displayName=ee.mY.displayName;let ei=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsxs)("div",{className:"flex items-center border-b px-3","cmdk-input-wrapper":"",children:[(0,s.jsx)(z.Z,{className:"mr-2 h-4 w-4 shrink-0 opacity-50"}),(0,s.jsx)(ee.mY.Input,{ref:a,className:(0,x.cn)("flex h-11 w-full rounded-md bg-transparent py-3 text-sm outline-none placeholder:text-muted-foreground disabled:cursor-not-allowed disabled:opacity-50",t),...r})]})});ei.displayName=ee.mY.Input.displayName,l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ee.mY.List,{ref:a,className:(0,x.cn)("max-h-[300px] overflow-y-auto overflow-x-hidden",t),...r})}).displayName=ee.mY.List.displayName;let en=l.forwardRef((e,a)=>(0,s.jsx)(ee.mY.Empty,{ref:a,className:"py-6 text-center text-sm",...e}));en.displayName=ee.mY.Empty.displayName;let el=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ee.mY.Group,{ref:a,className:(0,x.cn)("overflow-hidden p-1 text-foreground [&_[cmdk-group-heading]]:px-2 [&_[cmdk-group-heading]]:py-1.5 [&_[cmdk-group-heading]]:text-xs [&_[cmdk-group-heading]]:font-medium [&_[cmdk-group-heading]]:text-muted-foreground",t),...r})});el.displayName=ee.mY.Group.displayName,l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ee.mY.Separator,{ref:a,className:(0,x.cn)("-mx-1 h-px bg-border",t),...r})}).displayName=ee.mY.Separator.displayName;let eo=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(ee.mY.Item,{ref:a,className:(0,x.cn)("relative flex cursor-default select-none items-center rounded-sm px-2 py-1.5 text-sm outline-none data-[disabled=true]:pointer-events-none data-[selected='true']:bg-accent data-[selected=true]:text-accent-foreground data-[disabled=true]:opacity-50",t),...r})});eo.displayName=ee.mY.Item.displayName;var ed=t(6419);let ec=ed.fC,em=ed.xz,ex=l.forwardRef((e,a)=>{let{className:t,align:r="center",sideOffset:i=4,...n}=e;return(0,s.jsx)(ed.h_,{children:(0,s.jsx)(ed.VY,{ref:a,align:r,sideOffset:i,className:(0,x.cn)("z-50 w-72 rounded-md border bg-popover p-4 text-popover-foreground shadow-md outline-none data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",t),...n})})});ex.displayName=ed.VY.displayName;let eh=[{name:"com",price:6.49,style:"bg-primary-100 text-primary-700 border-primary-200",category:"generic"},{name:"net",price:10.98,style:"bg-teal-100 text-teal-700 border-teal-200",category:"generic"},{name:"org",price:7.48,style:"bg-green-100 text-green-700 border-green-200",category:"generic"},{name:"io",price:34.98,style:"bg-violet-100 text-violet-700 border-violet-200",category:"generic"},{name:"app",price:12.98,style:"bg-fuchsia-100 text-fuchsia-700 border-fuchsia-200",category:"new"},{name:"dev",price:10.98,style:"bg-emerald-100 text-emerald-700 border-emerald-200",category:"new"},{name:"me",price:9.98,style:"bg-orange-100 text-orange-700 border-orange-200",category:"new"},{name:"shop",price:1.28,style:"bg-amber-100 text-amber-700 border-amber-200",category:"new"},{name:"online",price:.98,style:"bg-indigo-100 text-indigo-700 border-indigo-200",category:"new"}],eu=(0,m.j)("inline-flex items-center justify-center rounded-full px-2.5 py-0.5 text-xs font-medium transition-all duration-200 border",{variants:{variant:{outline:"",filled:"",subtle:"border-transparent"},animation:{none:"",pulse:"hover:animate-pulse",bounce:"hover:animate-bounce",wiggle:"hover:animate-wiggle"},interactive:{true:"cursor-pointer transform transition-transform hover:scale-110",false:""}},defaultVariants:{variant:"filled",animation:"none",interactive:!1}});function eg(e){let{className:a,tld:t,selected:r=!1,variant:i,animation:n,interactive:l,count:o,...d}=e,c=function(e){let a=e.startsWith(".")?e.substring(1):e,t=eh.find(e=>e.name===a.toLowerCase());return(null==t?void 0:t.style)||"bg-gray-100 text-gray-700 border-gray-200"}(t),m=t.startsWith(".")?t:".".concat(t);return(0,s.jsxs)("span",{className:(0,x.cn)(eu({variant:i,animation:n,interactive:l}),c,r?"ring-2 ring-offset-1 shadow-md":"",a),...d,children:[m,void 0!==o&&(0,s.jsx)("span",{className:"ml-1.5 inline-flex items-center justify-center rounded-full bg-white/30 px-1.5 text-[0.625rem]",children:o})]})}function ep(e){let{tlds:a,selectedTlds:t,onChange:r}=e,[i,n]=(0,l.useState)(!1),o=e=>{let a=Array.isArray(t)?t:[];a.includes(e)?r(a.filter(a=>a!==e)):r([...a,e])},c=()=>{r([]),n(!1)};return(0,s.jsxs)("div",{className:"flex flex-col space-y-2",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsxs)("label",{className:"text-sm font-medium flex items-center gap-1 text-gray-700",children:[(0,s.jsx)($.Z,{className:"w-3.5 h-3.5 text-primary-500"}),"TLD Extensions"]}),(0,s.jsxs)("div",{className:"space-x-1",children:[(0,s.jsx)(d.z,{variant:"link",size:"sm",className:"text-xs h-auto p-0 text-primary-600 hover:text-primary-700",onClick:()=>{Array.isArray(a)&&a.length>0&&r([...a])},children:"All"}),(0,s.jsx)("span",{className:"text-xs text-gray-300",children:"|"}),(0,s.jsx)(d.z,{variant:"link",size:"sm",className:"text-xs h-auto p-0 text-primary-600 hover:text-primary-700",onClick:c,children:"Clear"})]})]}),(0,s.jsxs)(ec,{open:i,onOpenChange:n,children:[(0,s.jsx)(em,{asChild:!0,children:(0,s.jsxs)(d.z,{variant:"outline",role:"combobox","aria-expanded":i,className:"justify-between bg-white border-gray-200 hover:border-primary-300 hover:bg-primary-50/50 h-auto py-2",children:[(0,s.jsx)("span",{className:"truncate",children:Array.isArray(t)&&0!==t.length?Array.isArray(a)&&0!==a.length&&t.length===a.length?"All TLDs":"".concat(t.length," TLD").concat(t.length>1?"s":""," selected"):"Select TLDs"}),(0,s.jsx)(J.Z,{className:"ml-2 h-4 w-4 shrink-0 opacity-50"})]})}),(0,s.jsx)(ex,{className:"p-0 w-[calc(100vw-2rem)] sm:w-full min-w-[240px] bg-white shadow-xl border-gray-200",children:(0,s.jsxs)(er,{children:[(0,s.jsx)(ei,{placeholder:"Search TLDs...",className:"h-9 border-b rounded-none"}),(0,s.jsx)(en,{children:"No TLD found."}),(0,s.jsx)(el,{className:"max-h-[40vh] sm:max-h-[200px] overflow-y-auto p-1.5",children:Array.isArray(a)&&a.length>0?a.map(e=>(0,s.jsx)(eo,{value:e,onSelect:()=>o(e),className:"rounded-md mb-1 ".concat(t.includes(e)?"bg-primary-50 text-primary-700 font-medium":""),children:(0,s.jsxs)("div",{className:"flex items-center w-full",children:[(0,s.jsx)(L.Z,{className:(0,x.cn)("mr-2 h-3.5 w-3.5",t.includes(e)?"opacity-100 text-primary-500":"opacity-0")}),(0,s.jsx)(eg,{tld:e,selected:t.includes(e)})]})},e)):(0,s.jsx)(eo,{value:"placeholder",disabled:!0,children:"No TLDs available"})}),(0,s.jsxs)("div",{className:"border-t p-2 flex justify-between",children:[(0,s.jsxs)("span",{className:"text-xs text-gray-500",children:[t.length," of ",a.length," selected"]}),(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsx)(d.z,{variant:"ghost",size:"sm",className:"h-8 text-xs",onClick:c,children:"Clear"}),(0,s.jsx)(d.z,{size:"sm",className:"h-8 text-xs",onClick:()=>n(!1),children:"Apply"})]})]})]})})]}),Array.isArray(t)&&t.length>0&&(0,s.jsx)("div",{className:"flex flex-wrap gap-1 xs:gap-1.5 mt-2 animate-scale-in",children:t.map((e,a)=>(0,s.jsxs)("div",{className:"relative group animate-scale-in",style:{animationDelay:"".concat(.05*a,"s")},children:[(0,s.jsx)(eg,{tld:e,interactive:!0,animation:"pulse",onClick:()=>o(e),className:"pr-6 text-xs sm:text-sm"}),(0,s.jsx)("button",{className:"absolute right-1.5 top-1/2 -translate-y-1/2 w-3.5 h-3.5 rounded-full bg-gray-200 group-hover:bg-gray-300 flex items-center justify-center text-gray-500 group-hover:text-gray-700 transition-colors",onClick:()=>o(e),"aria-label":"Remove ".concat(e),children:(0,s.jsx)(X.Z,{className:"w-2 h-2"})})]},e))})]})}function ef(){let e=(0,c.ow)();return(0,s.jsx)("section",{id:"faq","aria-labelledby":"faq-heading",children:(0,s.jsx)(n.Zb,{className:"bg-white shadow-md",children:(0,s.jsxs)(n.aY,{className:"p-4 sm:p-6",children:[(0,s.jsx)("h2",{id:"faq-heading",className:"text-xl sm:text-2xl font-bold text-gray-800 mb-4 sm:mb-6",children:"Frequently Asked Questions About Our Domain Name Generator"}),(0,s.jsx)("div",{className:"space-y-6",children:e.map((a,t)=>(0,s.jsxs)("div",{className:t<e.length-1?"border-b pb-4 sm:pb-5":"",children:[(0,s.jsx)("h3",{className:"font-medium text-lg sm:text-xl mb-2 sm:mb-3 text-gray-700",children:a.question}),(0,s.jsx)("div",{className:"text-sm sm:text-base text-gray-600",dangerouslySetInnerHTML:{__html:a.answer}})]},t))})]})})})}var ev=t(2498),eb=t(7592),ej=t(4184),ey=t(2309);let eN=l.forwardRef((e,a)=>{let{className:t,value:r,indicatorClassName:i,animated:n=!1,...l}=e;return(0,s.jsx)(ey.fC,{ref:a,className:(0,x.cn)("relative h-4 w-full overflow-hidden rounded-full bg-secondary",t),...l,children:(0,s.jsx)(ey.z$,{className:(0,x.cn)("h-full flex-1 bg-primary transition-all rounded-full",n&&"animate-progress-pulse",i),style:{width:"".concat(r||0,"%")}})})});eN.displayName=ey.fC.displayName;var ew=t(734),ek=t(2020),eD=t(3432);function eC(e){let{isLoading:a,searchStatus:t,searchTerm:r=""}=e,[i,n]=(0,l.useState)(0),[o,d]=(0,l.useState)([{id:"analyze",title:"Analyzing Input",description:"Processing your concept and extracting key themes",status:"pending",icon:(0,s.jsx)(z.Z,{className:"h-5 w-5"})},{id:"generate",title:"Generating Domains",description:"Creating unique domain name suggestions",status:"pending",icon:(0,s.jsx)(ew.Z,{className:"h-5 w-5"})},{id:"check",title:"Checking Availability",description:"Verifying domain registration status",status:"pending",icon:(0,s.jsx)(ek.Z,{className:"h-5 w-5"})}]),c=(0,l.useRef)(0),m=(0,l.useRef)(0),x=(0,l.useRef)(!1),h=(0,l.useRef)(!1),u=(0,l.useRef)(null);return(0,l.useEffect)(()=>(a&&!x.current&&(x.current=!0,h.current=!1,c.current=0,m.current=0,n(0),d(e=>e.map((e,a)=>({...e,status:0===a?"active":"pending"}))),u.current&&clearInterval(u.current),u.current=setInterval(()=>{if(0===m.current)c.current<30?(c.current+=1,n(c.current)):(m.current=1,d(e=>e.map((e,a)=>({...e,status:0===a?"completed":1===a?"active":"pending"}))));else if(1===m.current){if(c.current<70){let e=c.current>60?.5:1;c.current+=e,n(c.current)}else m.current=2,d(e=>e.map((e,a)=>({...e,status:2===a?"active":a<2?"completed":"pending"})))}else if(2===m.current&&c.current<99){let e=c.current>90?.2:.5;c.current+=e,n(c.current)}},100)),!a&&x.current&&!h.current&&(h.current=!0,c.current=100,n(100),d(e=>e.map(e=>({...e,status:"completed"}))),u.current&&(clearInterval(u.current),u.current=null)),()=>{u.current&&(clearInterval(u.current),u.current=null)}),[a]),(0,l.useEffect)(()=>{"generating"===t?d(e=>e.map(e=>"analyze"===e.id?{...e,description:'Analyzing "'.concat(r,'" for domain ideas')}:e)):"checking"===t&&d(e=>e.map(e=>"check"===e.id?{...e,description:"Querying WHOIS databases for availability"}:e))},[t,r]),(0,s.jsxs)("div",{className:"max-w-md mx-auto bg-white rounded-lg shadow-md p-6 border border-gray-100",children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)("div",{className:"flex justify-between items-center mb-2",children:[(0,s.jsx)("span",{className:"text-sm font-medium text-gray-700",children:"Overall Progress"}),(0,s.jsxs)("span",{className:"text-sm font-medium text-primary-600",children:[Math.round(i),"%"]})]}),(0,s.jsx)(eN,{value:i,className:"h-2 bg-gray-100",indicatorClassName:"bg-gradient-to-r from-primary-500 via-secondary-500 to-primary-500 bg-size-200 animate-gradient",animated:!0})]}),(0,s.jsx)("div",{className:"space-y-4",children:o.map((e,a)=>(0,s.jsxs)("div",{className:"flex items-start gap-3",children:[(0,s.jsx)("div",{className:"flex-shrink-0 w-8 h-8 rounded-full flex items-center justify-center ".concat("completed"===e.status?"bg-green-100 text-green-600":"active"===e.status?"bg-primary-100 text-primary-600 animate-pulse":"bg-gray-100 text-gray-400"),children:"completed"===e.status?(0,s.jsx)(L.Z,{className:"h-4 w-4"}):"active"===e.status?(0,s.jsx)(eD.Z,{className:"h-4 w-4 animate-spin"}):e.icon}),(0,s.jsxs)("div",{className:"flex-grow",children:[(0,s.jsx)("h4",{className:"text-sm font-medium ".concat("completed"===e.status?"text-green-700":"active"===e.status?"text-primary-700":"text-gray-500"),children:e.title}),(0,s.jsx)("p",{className:"text-xs text-gray-500 mt-0.5",children:e.description}),"active"===e.status&&(0,s.jsx)("div",{className:"mt-2",children:(0,s.jsx)(eN,{value:0===a?i/30*100:1===a?(i-30)/40*100:(i-70)/30*100,className:"h-1 bg-gray-100",indicatorClassName:"bg-gradient-to-r from-primary-400 to-secondary-400",animated:!0})})]})]},e.id))})]})}var ez=t(202),eS=t(5079);let eT=()=>{let[e,a]=(0,l.useState)(""),[t,s]=(0,l.useState)("all"),[r,i]=(0,l.useState)([]),[n,o]=(0,l.useState)("idle"),d=(0,ez.NL)(),{toast:c}=(0,eS.pm)(),{trackEvent:m}=(0,Y.z)(),{rateLimitInfo:x,isLimited:h,refetch:u}=O(),{data:g}=(0,V.a)({queryKey:["/api/domains/tlds"],placeholderData:{tlds:eh.map(e=>e.name)}}),{data:p,isLoading:f,error:v,refetch:b}=(0,V.a)({queryKey:["/api/domains/search",e,r],enabled:e.length>1,meta:{queryParams:{term:e,tlds:r.join(","),limit:"100"}}});(0,l.useEffect)(()=>{0===r.length&&(null==g?void 0:g.tlds)&&g.tlds.length>0&&i(g.tlds)},[g,r.length]),(0,l.useEffect)(()=>{f&&e.length>1?o("generating"):e.length>1&&!f&&o("done")},[f,e]);let j=async e=>{if(e&&!(e.length<2)){if(await u(),h){c({title:"Rate Limit Reached",description:"You've reached the limit of ".concat(x.total," searches per hour. Please try again after ").concat(new Date(x.resetTime).toLocaleTimeString(),"."),variant:"destructive"});return}a(e),o("generating");try{0===r.length&&(null==g?void 0:g.tlds)&&i(g.tlds),await d.invalidateQueries({queryKey:["/api/domains/search",e,r]}),await b(),m("domain_search",{search_term:e,tlds:r.join(","),num_tlds:r.length}),u()}catch(t){let a="Error searching domains. Please try again.";t instanceof Error&&(t.message.includes("400")?a="Your search description may be too long or contains invalid characters. Please try a shorter description.":t.message.includes("429")&&(a="Rate limit exceeded. Please try again later.",u())),d.setQueryData(["/api/domains/search",e,r],{error:a,domains:[],searchTerm:e,timestamp:new Date().toISOString()}),c({title:"Search Error",description:a,variant:"destructive"}),o("done")}}},y=async()=>{if(e&&!(e.length<2)){if(m("generate_more_domains",{search_term:e}),await u(),h){c({title:"Rate Limit Reached",description:"You've reached the limit of ".concat(x.total," searches per hour. Please try again after ").concat(new Date(x.resetTime).toLocaleTimeString(),"."),variant:"destructive"});return}o("generating");try{let a=await (0,q.SC)("/api/domains/search?term=".concat(encodeURIComponent(e),"&tlds=").concat(encodeURIComponent(r.join(",")),"&limit=").concat(100));if(!a.ok){let t=await a.text(),s="Failed to generate more suggestions. Please try again.";400===a.status?s="Your search description may be too long or contains invalid characters. Please try a shorter description.":429===a.status?(s="Rate limit exceeded. Please try again later.",u()):500===a.status&&(s="Server error while generating suggestions. Please try again later.");let i=d.getQueryData(["/api/domains/search",e,r]);throw d.setQueryData(["/api/domains/search",e,r],{error:s,domains:(null==i?void 0:i.domains)||[],searchTerm:e,timestamp:new Date().toISOString()}),c({title:"Error",description:s,variant:"destructive"}),Error("API responded with ".concat(a.status,": ").concat(t||"Unknown error"))}let t=await a.json();console.log("Received new domain suggestions:",t);let s=d.getQueryData(["/api/domains/search",e,r]),i=(null==s?void 0:s.domains)||[],n=t.domains,l=new Map;i.forEach(e=>{l.set(e.fullDomain,e)});let m=[...i];n.forEach(e=>{let a=l.get(e.fullDomain);if(a){if(e.available&&!a.available){let a=m.findIndex(a=>a.fullDomain===e.fullDomain);-1!==a&&(m[a]=e)}}else m.push(e)});let x={...t,domains:m};d.setQueryData(["/api/domains/search",e,r],x),u(),o("done")}catch(e){console.error("Error generating more domain suggestions:",e),o("done")}}},N=()=>(null==p?void 0:p.domains)?p.domains.filter(e=>{let a=e.available,t=!(r.length>0)||r.includes(e.tld);return a&&t}):[],w=e=>{let a=new Map;return e.forEach(e=>{a.has(e.name)||a.set(e.name,new Map);let t=a.get(e.name),s=t.get(e.tld);s&&(s.available||!e.available)||t.set(e.tld,e)}),Array.from(a.entries()).map(e=>{var a;let[t,s]=e,r=Array.from(s.values()),i=r.filter(e=>e.available),n=r.filter(e=>!e.available),l=i.length>0?Math.min(...i.filter(e=>void 0!==e.price).map(e=>{let a=e.price;if("string"==typeof a){let e=parseFloat(a);return isNaN(e)?1/0:e}return"number"==typeof a?a:1/0})):void 0,o=null===(a=r[0])||void 0===a?void 0:a.type;return{baseName:t,domains:r,availableTlds:i.map(e=>e.tld),unavailableTlds:n.map(e=>e.tld),lowestPrice:l===1/0?void 0:l,type:o}})},k=()=>[...N()].sort((e,a)=>e.available!==a.available?e.available?-1:1:e.fullDomain.localeCompare(a.fullDomain)),D=(0,l.useMemo)(()=>w(k()),[r,t,null==p?void 0:p.domains]),C=(0,l.useMemo)(()=>[...D].sort((e,a)=>e.availableTlds.length!==a.availableTlds.length?a.availableTlds.length-e.availableTlds.length:e.baseName.localeCompare(a.baseName)),[D]);return{searchTerm:e,setSearchTerm:a,searchDomains:j,isLoading:f,error:v,data:p,domains:k(),groupedDomains:C,filterBy:t,setFilterBy:s,stats:(()=>{if(!(null==p?void 0:p.domains))return{available:0,unavailable:0};let e=p.domains.filter(e=>e.available).length,a=p.domains.length-e;return{available:e,unavailable:a}})(),hasMadeSearch:e.length>0,popularTlds:(null==g?void 0:g.tlds)||[],selectedTlds:r,setSelectedTlds:i,searchStatus:n,refetch:b,generateMoreSuggestions:y}};function eR(){let{rateLimitInfo:e,isLimited:a,isLoading:t}=O();if(t||!e||!a)return null;let{remaining:r,total:i,resetTime:n}=e,l=new Date(n),o=(0,B.Q)(l,{addSuffix:!0}),d=Math.ceil((l.getTime()-new Date().getTime())/6e4);return(0,s.jsxs)("div",{className:"flex flex-col space-y-2 p-3 bg-white rounded-lg shadow-sm border border-red-200",children:[(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,s.jsx)(S.Z,{className:"h-4 w-4 text-red-500"}),(0,s.jsx)("span",{className:"text-sm font-medium text-red-700",children:"Rate limit reached"})]}),(0,s.jsxs)("span",{className:"text-xs text-gray-600",children:["Resets in ",d," minutes"]})]}),(0,s.jsx)(eN,{value:100,className:"h-2 bg-red-100",indicatorClassName:"bg-red-500"}),(0,s.jsxs)("p",{className:"text-xs text-red-600 mt-1",children:["You've reached the hourly search limit. Please try again ",o,"."]})]})}var eZ=t(2840);function eA(){let{favorites:e}=(0,I.r)();return 0===e.length?null:(0,s.jsx)(eZ.V,{})}function eL(e){let{onCheck:a,isLoading:t}=e,[r,i]=(0,l.useState)(""),[n,c]=(0,l.useState)(""),[m,x]=(0,l.useState)(!1),h=(0,l.useRef)(null),{toast:g}=(0,eS.pm)(),f=e=>e.trim()?e.trim().length<2?(c("Domain name must be at least 2 characters"),!1):/^[a-zA-Z0-9-]+$/.test(e.trim())?(c(""),!0):(c("Domain name can only contain letters, numbers, and hyphens"),!1):(c("Please enter a domain name"),!1);return(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)("form",{onSubmit:e=>{e.preventDefault(),f(r)&&(a(r.trim()),h.current&&h.current.blur(),g({title:"Checking domain availability",description:'Checking availability for "'.concat(r.trim(),'" with your selected TLDs')}))},className:"space-y-3",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex gap-2",children:[(0,s.jsxs)("div",{className:"relative flex-1",children:[(0,s.jsx)(o.I,{ref:h,id:"customDomainInput",value:r,onChange:e=>{i(e.target.value),n&&c("")},onFocus:()=>x(!0),onBlur:()=>x(!1),className:"w-full px-3 py-2 text-sm pr-10 transition-all duration-300 border-2 ".concat(n?"border-red-500 focus-visible:ring-red-500":m?"border-primary-500 shadow-primary-200/40":""),placeholder:"Enter domain name (e.g. myawesomesite)","aria-invalid":!!n,"aria-describedby":n?"custom-domain-error":void 0,disabled:t}),(0,s.jsx)(z.Z,{className:"absolute right-3 top-1/2 -translate-y-1/2 w-4 h-4 transition-all duration-300 ".concat(m?"text-primary-500":"text-gray-400")})]}),(0,s.jsx)(d.z,{type:"submit",disabled:t||!r.trim(),className:"whitespace-nowrap",children:"Check Availability"})]}),(0,s.jsx)("p",{className:"text-xs text-gray-500",children:"Enter just the domain name without any extension. We'll check availability for all your selected TLDs."})]}),n&&(0,s.jsxs)(u,{variant:"destructive",className:"py-2",children:[(0,s.jsx)(S.Z,{className:"h-4 w-4"}),(0,s.jsx)(p,{id:"custom-domain-error",className:"text-sm",children:n})]})]})})}function e_(e){let{domainName:a,availableDomains:t,unavailableDomains:r,isLoading:i,hasChecked:l}=e;if(!l&&!i)return null;let o=(0,c.tv)([...t,...r]);return(0,s.jsx)(n.Zb,{className:"bg-white shadow-md overflow-hidden",children:(0,s.jsx)(n.aY,{className:"p-4 sm:p-6",children:i?(0,s.jsxs)("div",{className:"flex flex-col items-center justify-center py-8",children:[(0,s.jsx)(eD.Z,{className:"w-8 h-8 text-primary-500 animate-spin mb-4"}),(0,s.jsx)("h3",{className:"text-lg font-medium text-gray-700",children:"Checking domain availability..."}),(0,s.jsxs)("p",{className:"text-gray-500 mt-2",children:["We're checking availability for \"",a,'" with your selected TLDs']})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mb-4",children:[(0,s.jsxs)("h3",{className:"text-lg font-medium text-gray-800",children:["Results for ",(0,s.jsx)("span",{className:"text-primary-600 font-semibold",children:a})]}),(0,s.jsx)("p",{className:"text-sm text-gray-500 mt-1",children:t.length>0?"".concat(t.length," available domain").concat(1!==t.length?"s":""," found"):"No available domains found"})]}),o.length>0?(0,s.jsx)("div",{className:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6",children:o.map(e=>(0,s.jsx)(M,{groupedDomain:e},e.baseName))}):(0,s.jsx)("div",{className:"flex flex-col items-center justify-center py-8 px-4 text-center",children:(0,s.jsxs)("p",{className:"text-gray-500",children:['No domains found for "',a,'" with your selected TLDs.']})})]})})})}let eI=e=>{let[a,t]=(0,l.useState)(""),[s,r]=(0,l.useState)("idle");(0,ez.NL)();let{toast:i}=(0,eS.pm)(),{trackEvent:n}=(0,Y.z)(),{rateLimitInfo:o,isLimited:d,refetch:c}=O(),{data:m,isLoading:x,error:h,refetch:u,isRefetching:g}=(0,V.a)({queryKey:["/api/domains/check-custom",a,e],enabled:a.length>1,meta:{queryParams:{name:a,tlds:e.join(",")}}});return{domainName:a,checkDomainAvailability:async a=>{if(d){i({title:"Rate limit reached",description:"You've reached the maximum number of searches per hour. Please try again later.",variant:"destructive"});return}if(a.length<2){i({title:"Invalid domain name",description:"Domain name must be at least 2 characters long.",variant:"destructive"});return}t(a),r("checking");try{await u(),r("complete"),n("custom_domain_check",{domain_name:a,tlds:e.join(","),num_tlds:e.length}),c()}catch(e){console.error("Error checking domain availability:",e),r("error"),i({title:"Error checking domain",description:"There was an error checking domain availability. Please try again.",variant:"destructive"})}},isLoading:x||g,error:h,data:m,availableDomains:(null==m?void 0:m.domains)?m.domains.filter(e=>e.available):[],unavailableDomains:(null==m?void 0:m.domains)?m.domains.filter(e=>!e.available):[],stats:(()=>{if(!(null==m?void 0:m.domains))return{available:0,unavailable:0};let e=m.domains.filter(e=>e.available).length,a=m.domains.length-e;return{available:e,unavailable:a}})(),checkStatus:s,hasChecked:a.length>0&&"complete"===s}},eF=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("textarea",{className:(0,x.cn)("flex min-h-[80px] w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",t),ref:a,...r})});eF.displayName="Textarea";let eY=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("div",{className:"relative w-full overflow-auto",children:(0,s.jsx)("table",{ref:a,className:(0,x.cn)("w-full caption-bottom text-sm",t),...r})})});eY.displayName="Table";let eE=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("thead",{ref:a,className:(0,x.cn)("[&_tr]:border-b",t),...r})});eE.displayName="TableHeader";let eP=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("tbody",{ref:a,className:(0,x.cn)("[&_tr:last-child]:border-0",t),...r})});eP.displayName="TableBody",l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("tfoot",{ref:a,className:(0,x.cn)("border-t bg-muted/50 font-medium [&>tr]:last:border-b-0",t),...r})}).displayName="TableFooter";let eM=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("tr",{ref:a,className:(0,x.cn)("border-b transition-colors hover:bg-muted/50 data-[state=selected]:bg-muted",t),...r})});eM.displayName="TableRow";let eG=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("th",{ref:a,className:(0,x.cn)("h-12 px-4 text-left align-middle font-medium text-muted-foreground [&:has([role=checkbox])]:pr-0",t),...r})});eG.displayName="TableHead";let eV=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("td",{ref:a,className:(0,x.cn)("p-4 align-middle [&:has([role=checkbox])]:pr-0",t),...r})});eV.displayName="TableCell",l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)("caption",{ref:a,className:(0,x.cn)("mt-4 text-sm text-muted-foreground",t),...r})}).displayName="TableCaption";var eq=t(434);let eO=eq.fC,eB=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eq.aV,{ref:a,className:(0,x.cn)("inline-flex h-10 items-center justify-center rounded-md bg-muted p-1 text-muted-foreground",t),...r})});eB.displayName=eq.aV.displayName;let eU=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eq.xz,{ref:a,className:(0,x.cn)("inline-flex items-center justify-center whitespace-nowrap rounded-sm px-3 py-1.5 text-sm font-medium ring-offset-background transition-all focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 data-[state=active]:bg-background data-[state=active]:text-foreground data-[state=active]:shadow-sm",t),...r})});eU.displayName=eq.xz.displayName;let eW=l.forwardRef((e,a)=>{let{className:t,...r}=e;return(0,s.jsx)(eq.VY,{ref:a,className:(0,x.cn)("mt-2 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2",t),...r})});eW.displayName=eq.VY.displayName;var eQ=t(3050),eH=t(7695),eK=t(873),e$=t(8029),eJ=t(2917);function eX(e){let{selectedTlds:a}=e,[t,r]=(0,l.useState)(""),[i,o]=(0,l.useState)(""),[c,m]=(0,l.useState)("all"),{isAuthenticated:x,login:h}=(0,eK.a)(),{checkDomains:f,domains:v,isLoading:b,error:j}=function(e){let[a,t]=(0,l.useState)([]),[s,r]=(0,l.useState)("idle");(0,ez.NL)();let{toast:i}=(0,eS.pm)(),{trackEvent:n}=(0,Y.z)(),{authFetch:o}=(0,eJ.K)(),{rateLimitInfo:d,isLimited:c,refetch:m}=O(),x=(0,e$.D)({mutationFn:async a=>{if(!a||0===a.length)throw Error("No domains to check");return await o("/api/domains/check-bulk",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({domains:a,tlds:e})})},onSuccess:a=>{t(a.domains||[]),r("complete"),n("bulk_domain_check",{domains_count:a.domains.length,tlds:e.join(","),num_tlds:e.length})},onError:e=>{r("error"),i({title:"Error checking domains",description:"There was an error checking domain availability. Please try again.",variant:"destructive"})}});return{checkDomains:(0,l.useCallback)(async e=>{if(0===e.length){i({title:"No domains to check",description:"Please enter at least one domain name.",variant:"destructive"});return}if(await m(),c){i({title:"Rate Limit Reached",description:"You've reached the limit of ".concat(d.total," searches per hour. Please try again after ").concat(new Date(d.resetTime).toLocaleTimeString(),"."),variant:"destructive"});return}r("checking");try{await x.mutateAsync(e),m()}catch(e){}},[x,c,d,m,e,i]),domains:a,isLoading:x.isPending,error:x.error?x.error.message:null,checkStatus:s}}(a);(0,l.useEffect)(()=>{i&&o("")},[t]);let y=()=>{let e=t.split("\n").map(e=>e.trim()).filter(e=>e.length>0);if(0===e.length)return o("Please enter at least one domain name"),!1;if(e.length>100)return o("You can check up to 100 domains at once"),!1;let a=e.filter(e=>!/^[a-z0-9-]+$/.test(e.toLowerCase()));return!(a.length>0)||(o("Invalid domain format: ".concat(a.join(", "))),!1)},N=(v||[]).filter(e=>"all"===c||("available"===c?e.available:"unavailable"!==c||!e.available)),w=(null==v?void 0:v.filter(e=>e.available).length)||0,k=(null==v?void 0:v.filter(e=>!e.available).length)||0;return(0,s.jsx)("div",{className:"w-full",children:(0,s.jsxs)(n.Zb,{children:[(0,s.jsxs)(n.Ol,{children:[(0,s.jsx)(n.ll,{children:"Bulk Domain Checker"}),(0,s.jsx)(n.SZ,{children:"Check availability for multiple domains at once. Enter one domain name per line (without TLDs)."})]}),(0,s.jsxs)(n.aY,{children:[x?(0,s.jsx)("form",{onSubmit:e=>{if(e.preventDefault(),!x){h();return}y()&&f(t.split("\n").map(e=>e.trim()).filter(e=>e.length>0))},children:(0,s.jsxs)("div",{className:"space-y-4",children:[(0,s.jsx)(eF,{placeholder:"Enter domain names (one per line, without TLDs) example1 example2 example3",value:t,onChange:e=>{r(e.target.value)},rows:8,className:"font-mono ".concat(i?"border-red-500":""),disabled:b}),i&&(0,s.jsxs)(u,{variant:"destructive",children:[(0,s.jsx)(S.Z,{className:"h-4 w-4"}),(0,s.jsx)(g,{children:"Error"}),(0,s.jsx)(p,{children:i})]}),j&&(0,s.jsxs)(u,{variant:"destructive",children:[(0,s.jsx)(S.Z,{className:"h-4 w-4"}),(0,s.jsx)(g,{children:"Error"}),(0,s.jsx)(p,{children:j})]}),(0,s.jsxs)("div",{className:"flex justify-between items-center",children:[(0,s.jsx)("div",{className:"text-sm text-gray-500",children:a.length>0?"Checking against TLDs: ".concat(a.join(", ")):"Using default TLDs"}),(0,s.jsx)(d.z,{type:"submit",disabled:b||0===t.trim().length,children:b?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(eD.Z,{className:"mr-2 h-4 w-4 animate-spin"}),"Checking..."]}):"Check Domains"})]})]})}):(0,s.jsxs)("div",{className:"p-6 text-center",children:[(0,s.jsx)("div",{className:"mb-4 text-blue-600",children:(0,s.jsx)(S.Z,{className:"h-12 w-12 mx-auto"})}),(0,s.jsx)("h3",{className:"text-lg font-medium mb-2",children:"Sign In Required"}),(0,s.jsx)("p",{className:"text-gray-600 mb-4",children:"Please sign in to use the bulk domain checker feature."}),(0,s.jsx)(d.z,{onClick:()=>h(),children:"Sign In to Continue"})]}),x&&v&&v.length>0&&(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)(eO,{defaultValue:"all",value:c,onValueChange:e=>m(e),children:[(0,s.jsxs)(eB,{className:"grid w-full grid-cols-3",children:[(0,s.jsxs)(eU,{value:"all",children:["All (",v.length,")"]}),(0,s.jsxs)(eU,{value:"available",children:["Available (",w,")"]}),(0,s.jsxs)(eU,{value:"unavailable",children:["Unavailable (",k,")"]})]}),(0,s.jsx)(eW,{value:"all",className:"mt-4",children:(0,s.jsx)(e0,{domains:N})}),(0,s.jsx)(eW,{value:"available",className:"mt-4",children:(0,s.jsx)(e0,{domains:N})}),(0,s.jsx)(eW,{value:"unavailable",className:"mt-4",children:(0,s.jsx)(e0,{domains:N})})]})})]})]})})}function e0(e){let{domains:a}=e,{trackEvent:t}=(0,Y.z)();if(0===a.length)return(0,s.jsx)("div",{className:"text-center py-4",children:"No domains match the current filter"});let r=e=>{t("domain_registration_click",{domain_name:e.name,tld:e.tld,price:e.price,source:"bulk_checker"}),window.open("https://namecheap.pxf.io/c/6159477/386170/5618?u=https%3A%2F%2Fwww.namecheap.com%2Fdomains%2Fregistration%2Fresults.aspx%3Fdomain%3D".concat(encodeURIComponent(e.fullDomain)))};return(0,s.jsx)("div",{className:"rounded-md border",children:(0,s.jsxs)(eY,{children:[(0,s.jsx)(eE,{children:(0,s.jsxs)(eM,{children:[(0,s.jsx)(eG,{className:"w-[50%]",children:(0,s.jsx)("div",{className:"flex items-center",children:"Domain"})}),(0,s.jsx)(eG,{className:"w-[25%]",children:(0,s.jsx)("div",{className:"flex items-center",children:"Status"})}),(0,s.jsx)(eG,{className:"w-[25%]",children:(0,s.jsx)("div",{className:"flex items-center",children:"Register"})})]})}),(0,s.jsx)(eP,{children:a.map(e=>(0,s.jsxs)(eM,{children:[(0,s.jsx)(eV,{className:"font-medium",children:e.fullDomain}),(0,s.jsx)(eV,{children:e.available?(0,s.jsxs)("div",{className:"flex items-center text-green-600",children:[(0,s.jsx)(eQ.Z,{className:"mr-2 h-4 w-4"}),"Available"]}):(0,s.jsxs)("div",{className:"flex items-center text-red-600",children:[(0,s.jsx)(eH.Z,{className:"mr-2 h-4 w-4"}),"Taken"]})}),(0,s.jsx)(eV,{children:e.available?(0,s.jsxs)(d.z,{className:"bg-green-50 text-green-800 hover:bg-green-200 hover:text-green-900 border-green-200 flex items-center justify-center gap-1 transition-all duration-300",variant:"outline",size:"sm",onClick:()=>r(e),children:[(0,s.jsx)(_.Z,{className:"w-3 h-3"}),(0,s.jsx)("span",{children:"Register"}),(0,s.jsxs)("span",{className:"text-xs opacity-75 ml-1",children:[(0,c.T4)(e.price),"/yr"]})]}):(0,s.jsx)(d.z,{className:"w-full transition-all duration-300 overflow-hidden relative",variant:"outline",disabled:!0,size:"sm",children:(0,s.jsx)("span",{className:"relative z-10",children:"Not Available"})})})]},e.fullDomain))})]})})}var e1=!0;function e2(e){let{}=e,{searchTerm:a,searchDomains:t,isLoading:r,domains:o,groupedDomains:c,hasMadeSearch:m,popularTlds:x,selectedTlds:h,setSelectedTlds:u,searchStatus:g,refetch:p,generateMoreSuggestions:f}=eT(),{domainName:v,checkDomainAvailability:b,isLoading:j,availableDomains:y,unavailableDomains:N,hasChecked:w}=eI(h),k=r||j,[D,C]=(0,l.useState)(!1),[S,T]=(0,l.useState)("single");return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)(i(),{children:[(0,s.jsx)("title",{children:"Domain Name Generator & Business Name Maker | DomainMate"}),(0,s.jsx)("meta",{name:"description",content:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."}),(0,s.jsx)("meta",{name:"keywords",content:"domain name generator, domain name maker, business name generator for free, business name generator ai, ai domain generator, domain availability checker, brand name ideas, domain suggestions, available domains"}),(0,s.jsx)("meta",{property:"og:title",content:"Domain Name Generator & Business Name Maker | DomainMate"}),(0,s.jsx)("meta",{property:"og:description",content:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."}),(0,s.jsx)("meta",{property:"og:url",content:"https://domainmate.net"}),(0,s.jsx)("meta",{property:"og:type",content:"website"}),(0,s.jsx)("meta",{name:"twitter:card",content:"summary_large_image"}),(0,s.jsx)("meta",{name:"twitter:title",content:"Domain Name Generator & Business Name Maker | DomainMate"}),(0,s.jsx)("meta",{name:"twitter:description",content:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly."}),(0,s.jsx)("link",{rel:"canonical",href:"https://domainmate.net/"}),(0,s.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"WebApplication",name:"Domain Name Generator & Business Name Maker | DomainMate",url:"https://domainmate.net",description:"Create perfect domain names with our free AI domain name generator. Generate creative, available domains for your brand or startup instantly.",applicationCategory:"BusinessApplication",operatingSystem:"All",offers:{"@type":"Offer",price:"0",priceCurrency:"USD"},creator:{"@type":"Organization",name:"DomainMate",url:"https://domainmate.net"}})}})]}),(0,s.jsx)("div",{className:"font-sans text-gray-700 min-h-screen",children:(0,s.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,s.jsx)("header",{className:"mb-10",children:(0,s.jsx)(ev.w,{})}),(0,s.jsxs)("main",{children:[(0,s.jsxs)("div",{className:"mb-6",children:[(0,s.jsxs)(n.Zb,{className:"bg-white shadow-xl mb-8 border-0 overflow-hidden",children:[(0,s.jsx)("div",{className:"absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-primary-500 via-secondary-500 to-accent-teal-500"}),(0,s.jsxs)(n.aY,{className:"p-6 sm:p-8",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 mb-4",children:"AI Domain Name Generator"}),(0,s.jsx)("h2",{className:"text-xl sm:text-2xl font-semibold text-gray-700 mb-3",children:"Find the perfect available domain name instantly"}),(0,s.jsx)("p",{className:"text-lg sm:text-xl text-gray-600 max-w-3xl mx-auto mb-2",id:"main-description",style:{contentVisibility:"auto"},children:"Our advanced AI domain name generator creates highly creative, distinctive name suggestions that perfectly capture your business idea's essence."}),(0,s.jsx)("p",{className:"text-md text-gray-600 max-w-3xl mx-auto",children:"We prioritize creativity and relevance, then verify domain availability in real-time so you'll only see domains you can register immediately."})]}),(0,s.jsxs)("div",{className:"border-t border-gray-100 pt-8",children:[(0,s.jsx)(Z,{onSearch:e=>{C(!1),t(e)},isLoading:k}),(0,s.jsx)("div",{className:"mt-4 max-w-md mx-auto",children:(0,s.jsx)(d.z,{variant:"outline",size:"sm",className:"w-full flex items-center justify-center gap-2",onClick:()=>C(!D),children:D?(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(P.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Hide custom domain check"})]}):(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(z.Z,{className:"w-4 h-4"}),(0,s.jsx)("span",{children:"Check domain availability or bulk check"})]})})}),D&&(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("div",{className:"mt-4 max-w-md mx-auto p-4 bg-white rounded-lg shadow-sm border border-gray-100",children:[(0,s.jsx)(eL,{onCheck:b,isLoading:j}),(0,s.jsx)("div",{className:"mt-4 pt-4 border-t border-gray-100",children:(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsx)("div",{className:"text-sm text-gray-600",children:"Want to check multiple domains at once?"}),(0,s.jsxs)(d.z,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>T("bulk"),children:[(0,s.jsx)(ej.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Bulk Check"})]})]})})]}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(e_,{domainName:v,availableDomains:y,unavailableDomains:N,isLoading:j,hasChecked:w})})]}),"bulk"===S&&(0,s.jsxs)("div",{className:"mt-6",children:[(0,s.jsx)("div",{className:"mb-4",children:(0,s.jsxs)(d.z,{variant:"outline",size:"sm",className:"flex items-center gap-1",onClick:()=>T("single"),children:[(0,s.jsx)(z.Z,{className:"h-4 w-4"}),(0,s.jsx)("span",{children:"Back to Single Domain Search"})]})}),(0,s.jsx)(eX,{selectedTlds:h})]})]})]})]}),(0,s.jsxs)("div",{className:"mt-4 max-w-md mx-auto",children:[(0,s.jsx)(ep,{tlds:x||[],selectedTlds:h,onChange:e=>{u(e),m&&a&&setTimeout(()=>{p()},100)}}),(0,s.jsx)("div",{className:"mt-4",children:(0,s.jsx)(eR,{})})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsx)(eA,{})})]}),(0,s.jsx)(n.Zb,{className:"bg-white shadow-md mb-10",children:(0,s.jsxs)(n.aY,{className:"p-6",children:[!m&&!k&&(0,s.jsxs)("div",{className:"text-center py-10",children:[(0,s.jsx)(e4,{className:"h-16 w-16 text-gray-300 mx-auto mb-4"}),(0,s.jsx)("p",{className:"text-lg text-gray-500",children:"Enter your idea above to get domain suggestions"})]}),k&&(0,s.jsx)("div",{className:"relative py-8 overflow-hidden bg-white rounded-lg",children:(0,s.jsx)("div",{className:"relative z-10",children:(0,s.jsx)(eC,{isLoading:k,searchStatus:g,searchTerm:a})})}),m&&!k&&(0,s.jsxs)("div",{children:[(0,s.jsx)("div",{className:"mb-6",children:(0,s.jsxs)("h2",{className:"text-xl font-bold text-gray-800",children:["Domain suggestions for ",(0,s.jsx)("span",{className:"text-blue-600",children:a})]})}),(0,s.jsx)(K,{domains:o,groupedDomains:c,isLoading:k,searchStatus:g,onGenerateMore:f})]})]})}),(0,s.jsx)(ef,{})]}),(0,s.jsx)(eb.$,{})]})})]})}function e4(e){return(0,s.jsx)("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",strokeWidth:2,...e,children:(0,s.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m-9 9a9 9 0 019-9"})})}}},function(e){e.O(0,[5,809,380,840,888,774,179],function(){return e(e.s=8312)}),_N_E=e.O()}]);