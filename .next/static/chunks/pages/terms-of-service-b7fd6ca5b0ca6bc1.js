(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[507],{7011:function(e,i,n){(window.__NEXT_P=window.__NEXT_P||[]).push(["/terms-of-service",function(){return n(4034)}])},4034:function(e,i,n){"use strict";n.r(i),n.d(i,{__N_SSG:function(){return l},default:function(){return d}});var r=n(5893),s=n(9008),t=n.n(s),a=n(2498),o=n(7592),c=n(3749),l=!0;function d(e){let{lastUpdated:i}=e;return(0,r.jsxs)(r.Fragment,{children:[(0,r.jsxs)(t(),{children:[(0,r.jsx)("title",{children:"Terms of Service | DomainMate"}),(0,r.jsx)("meta",{name:"description",content:"Read DomainMate's terms of service to understand the rules and guidelines for using our domain name generator service."}),(0,r.jsx)("meta",{name:"robots",content:"index, follow"}),(0,r.jsx)("meta",{property:"og:title",content:"Terms of Service | DomainMate"}),(0,r.jsx)("meta",{property:"og:description",content:"Read DomainMate's terms of service to understand the rules and guidelines for using our domain name generator service."}),(0,r.jsx)("meta",{property:"og:url",content:"https://domainmate.net/terms-of-service"}),(0,r.jsx)("meta",{property:"og:type",content:"website"}),(0,r.jsx)("link",{rel:"canonical",href:"https://domainmate.net/terms-of-service"}),(0,r.jsx)("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify({"@context":"https://schema.org","@type":"WebPage",name:"Terms of Service",description:"DomainMate's terms of service explaining the rules and guidelines for using our service",url:"https://domainmate.net/terms-of-service",publisher:{"@type":"Organization",name:"DomainMate",url:"https://domainmate.net"},dateModified:i})}})]}),(0,r.jsx)("div",{className:"font-sans text-gray-700 min-h-screen",children:(0,r.jsxs)("div",{className:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8",children:[(0,r.jsx)("header",{className:"mb-10",children:(0,r.jsx)(a.w,{})}),(0,r.jsxs)("main",{children:[(0,r.jsxs)("div",{className:"mb-8",children:[(0,r.jsx)("h1",{className:"text-3xl sm:text-4xl font-bold text-gray-800 mb-4",children:"Terms of Service"}),(0,r.jsxs)("p",{className:"text-lg text-gray-600",children:["Last updated: ",new Date(i).toLocaleDateString()]})]}),(0,r.jsx)(c.Zb,{children:(0,r.jsx)(c.aY,{className:"p-8",children:(0,r.jsxs)("div",{className:"prose prose-lg max-w-none",children:[(0,r.jsx)("h2",{children:"Acceptance of Terms"}),(0,r.jsx)("p",{children:"By accessing and using DomainMate, you accept and agree to be bound by the terms and provision of this agreement."}),(0,r.jsx)("h2",{children:"Description of Service"}),(0,r.jsx)("p",{children:"DomainMate is an AI-powered domain name generator that helps users find available domain names for their businesses and projects. Our service includes:"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"AI-generated domain name suggestions"}),(0,r.jsx)("li",{children:"Domain availability checking"}),(0,r.jsx)("li",{children:"Bulk domain checking"}),(0,r.jsx)("li",{children:"Favorites management"}),(0,r.jsx)("li",{children:"User accounts and profiles"})]}),(0,r.jsx)("h2",{children:"User Accounts"}),(0,r.jsx)("p",{children:"To access certain features of our service, you may be required to create an account. You are responsible for maintaining the confidentiality of your account information."}),(0,r.jsx)("h2",{children:"Acceptable Use"}),(0,r.jsx)("p",{children:"You agree to use our service only for lawful purposes and in accordance with these Terms. You agree not to:"}),(0,r.jsxs)("ul",{children:[(0,r.jsx)("li",{children:"Use the service for any illegal or unauthorized purpose"}),(0,r.jsx)("li",{children:"Attempt to gain unauthorized access to our systems"}),(0,r.jsx)("li",{children:"Interfere with or disrupt the service"}),(0,r.jsx)("li",{children:"Use automated tools to access the service excessively"})]}),(0,r.jsx)("h2",{children:"Intellectual Property"}),(0,r.jsx)("p",{children:"The service and its original content, features, and functionality are and will remain the exclusive property of DomainMate and its licensors."}),(0,r.jsx)("h2",{children:"Domain Registration"}),(0,r.jsx)("p",{children:"DomainMate does not register domains on behalf of users. We provide suggestions and availability information, but domain registration must be completed through authorized domain registrars."}),(0,r.jsx)("h2",{children:"Disclaimer of Warranties"}),(0,r.jsx)("p",{children:"The information, software, products, and services included in or available through the service may include inaccuracies or typographical errors. We make no warranty that the service will be uninterrupted or error-free."}),(0,r.jsx)("h2",{children:"Limitation of Liability"}),(0,r.jsx)("p",{children:"In no event shall DomainMate be liable for any indirect, incidental, special, consequential, or punitive damages arising out of your use of the service."}),(0,r.jsx)("h2",{children:"Rate Limiting"}),(0,r.jsx)("p",{children:"To ensure fair usage and maintain service quality, we implement rate limiting on our API endpoints. Excessive usage may result in temporary restrictions."}),(0,r.jsx)("h2",{children:"Privacy"}),(0,r.jsx)("p",{children:"Your privacy is important to us. Please review our Privacy Policy, which also governs your use of the service."}),(0,r.jsx)("h2",{children:"Termination"}),(0,r.jsx)("p",{children:"We may terminate or suspend your account and bar access to the service immediately, without prior notice or liability, under our sole discretion, for any reason whatsoever."}),(0,r.jsx)("h2",{children:"Changes to Terms"}),(0,r.jsx)("p",{children:"We reserve the right to modify or replace these Terms at any time. If a revision is material, we will provide at least 30 days notice prior to any new terms taking effect."}),(0,r.jsx)("h2",{children:"Contact Information"}),(0,r.jsx)("p",{children:"If you have any questions about these Terms of Service, please contact us through our website."})]})})})]}),(0,r.jsx)(o.$,{})]})})]})}}},function(e){e.O(0,[5,380,888,774,179],function(){return e(e.s=7011)}),_N_E=e.O()}]);