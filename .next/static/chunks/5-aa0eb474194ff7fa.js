(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[5],{3541:function(e,t,n){"use strict";n.d(t,{Ry:function(){return c}});var r=new WeakMap,o=new WeakMap,i={},a=0,u=function(e){return e&&(e.host||u(e.parentNode))},l=function(e,t,n,l){var c=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=u(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var s=i[n],f=[],d=new Set,p=new Set(c),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};c.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(l),i=null!==t&&"false"!==t,a=(r.get(e)||0)+1,u=(s.get(e)||0)+1;r.set(e,a),s.set(e,u),f.push(e),1===a&&i&&o.set(e,!0),1===u&&e.setAttribute(n,"true"),i||e.setAttribute(l,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){f.forEach(function(e){var t=r.get(e)-1,i=s.get(e)-1;r.set(e,t),s.set(e,i),t||(o.has(e)||e.removeAttribute(l),o.delete(e)),i||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},c=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live]"))),l(r,o,n,"aria-hidden")):function(){return null}}},2089:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("BookOpen",[["path",{d:"M12 7v14",key:"1akyts"}],["path",{d:"M3 18a1 1 0 0 1-1-1V4a1 1 0 0 1 1-1h5a4 4 0 0 1 4 4 4 4 0 0 1 4-4h5a1 1 0 0 1 1 1v13a1 1 0 0 1-1 1h-6a3 3 0 0 0-3 3 3 3 0 0 0-3-3z",key:"ruj8y"}]])},8865:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]])},4307:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("ChevronRight",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]])},8655:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("CircleAlert",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["line",{x1:"12",x2:"12",y1:"8",y2:"12",key:"1pkeuh"}],["line",{x1:"12",x2:"12.01",y1:"16",y2:"16",key:"4dfq90"}]])},162:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Circle",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}]])},2553:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("FileText",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},3432:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("LoaderCircle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]])},775:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("LogOut",[["path",{d:"M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4",key:"1uf3rs"}],["polyline",{points:"16 17 21 12 16 7",key:"1gabdz"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}]])},6127:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("Search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]])},8267:function(e,t,n){"use strict";n.d(t,{Z:function(){return r}});let r=(0,n(1134).Z)("User",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},8199:function(e,t){"use strict";var n,r,o,i;Object.defineProperty(t,"__esModule",{value:!0}),function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{ACTION_FAST_REFRESH:function(){return f},ACTION_NAVIGATE:function(){return u},ACTION_PREFETCH:function(){return s},ACTION_REFRESH:function(){return a},ACTION_RESTORE:function(){return l},ACTION_SERVER_ACTION:function(){return d},ACTION_SERVER_PATCH:function(){return c},PrefetchCacheEntryStatus:function(){return r},PrefetchKind:function(){return n},isThenable:function(){return p}});let a="refresh",u="navigate",l="restore",c="server-patch",s="prefetch",f="fast-refresh",d="server-action";function p(e){return e&&("object"==typeof e||"function"==typeof e)&&"function"==typeof e.then}(o=n||(n={})).AUTO="auto",o.FULL="full",o.TEMPORARY="temporary",(i=r||(r={})).fresh="fresh",i.reusable="reusable",i.expired="expired",i.stale="stale",("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7195:function(e,t,n){"use strict";function r(e,t,n,r){return!1}Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"getDomainLocale",{enumerable:!0,get:function(){return r}}),n(8337),("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},8342:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"default",{enumerable:!0,get:function(){return b}});let r=n(8754),o=n(5893),i=r._(n(7294)),a=n(6075),u=n(3955),l=n(8041),c=n(9903),s=n(5490),f=n(1928),d=n(257),p=n(4229),h=n(7195),m=n(9470),v=n(8199),g=new Set;function y(e,t,n,r,o,i){if(i||(0,u.isLocalURL)(t)){if(!r.bypassPrefetchedCheck){let o=t+"%"+n+"%"+(void 0!==r.locale?r.locale:"locale"in e?e.locale:void 0);if(g.has(o))return;g.add(o)}(async()=>i?e.prefetch(t,o):e.prefetch(t,n,r))().catch(e=>{})}}function w(e){return"string"==typeof e?e:(0,l.formatUrl)(e)}let b=i.default.forwardRef(function(e,t){let n,r;let{href:l,as:g,children:b,prefetch:x=null,passHref:E,replace:M,shallow:C,scroll:R,locale:k,onClick:S,onMouseEnter:A,onTouchStart:T,legacyBehavior:j=!1,...P}=e;n=b,j&&("string"==typeof n||"number"==typeof n)&&(n=(0,o.jsx)("a",{children:n}));let O=i.default.useContext(f.RouterContext),_=i.default.useContext(d.AppRouterContext),L=null!=O?O:_,D=!O,N=!1!==x,I=null===x?v.PrefetchKind.AUTO:v.PrefetchKind.FULL,{href:F,as:W}=i.default.useMemo(()=>{if(!O){let e=w(l);return{href:e,as:g?w(g):e}}let[e,t]=(0,a.resolveHref)(O,l,!0);return{href:e,as:g?(0,a.resolveHref)(O,g):t||e}},[O,l,g]),K=i.default.useRef(F),H=i.default.useRef(W);j&&(r=i.default.Children.only(n));let V=j?r&&"object"==typeof r&&r.ref:t,[B,Z,z]=(0,p.useIntersection)({rootMargin:"200px"}),$=i.default.useCallback(e=>{(H.current!==W||K.current!==F)&&(z(),H.current=W,K.current=F),B(e),V&&("function"==typeof V?V(e):"object"==typeof V&&(V.current=e))},[W,V,F,z,B]);i.default.useEffect(()=>{L&&Z&&N&&y(L,F,W,{locale:k},{kind:I},D)},[W,F,Z,k,N,null==O?void 0:O.locale,L,D,I]);let U={ref:$,onClick(e){j||"function"!=typeof S||S(e),j&&r.props&&"function"==typeof r.props.onClick&&r.props.onClick(e),L&&!e.defaultPrevented&&function(e,t,n,r,o,a,l,c,s){let{nodeName:f}=e.currentTarget;if("A"===f.toUpperCase()&&(function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||!s&&!(0,u.isLocalURL)(n)))return;e.preventDefault();let d=()=>{let e=null==l||l;"beforePopState"in t?t[o?"replace":"push"](n,r,{shallow:a,locale:c,scroll:e}):t[o?"replace":"push"](r||n,{scroll:e})};s?i.default.startTransition(d):d()}(e,L,F,W,M,C,R,k,D)},onMouseEnter(e){j||"function"!=typeof A||A(e),j&&r.props&&"function"==typeof r.props.onMouseEnter&&r.props.onMouseEnter(e),L&&(N||!D)&&y(L,F,W,{locale:k,priority:!0,bypassPrefetchedCheck:!0},{kind:I},D)},onTouchStart:function(e){j||"function"!=typeof T||T(e),j&&r.props&&"function"==typeof r.props.onTouchStart&&r.props.onTouchStart(e),L&&(N||!D)&&y(L,F,W,{locale:k,priority:!0,bypassPrefetchedCheck:!0},{kind:I},D)}};if((0,c.isAbsoluteUrl)(W))U.href=W;else if(!j||E||"a"===r.type&&!("href"in r.props)){let e=void 0!==k?k:null==O?void 0:O.locale,t=(null==O?void 0:O.isLocaleDomain)&&(0,h.getDomainLocale)(W,e,null==O?void 0:O.locales,null==O?void 0:O.domainLocales);U.href=t||(0,m.addBasePath)((0,s.addLocale)(W,e,null==O?void 0:O.defaultLocale))}return j?i.default.cloneElement(r,U):(0,o.jsx)("a",{...P,...U,children:n})});("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},4229:function(e,t,n){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useIntersection",{enumerable:!0,get:function(){return l}});let r=n(7294),o=n(4474),i="function"==typeof IntersectionObserver,a=new Map,u=[];function l(e){let{rootRef:t,rootMargin:n,disabled:l}=e,c=l||!i,[s,f]=(0,r.useState)(!1),d=(0,r.useRef)(null),p=(0,r.useCallback)(e=>{d.current=e},[]);return(0,r.useEffect)(()=>{if(i){if(c||s)return;let e=d.current;if(e&&e.tagName)return function(e,t,n){let{id:r,observer:o,elements:i}=function(e){let t;let n={root:e.root||null,margin:e.rootMargin||""},r=u.find(e=>e.root===n.root&&e.margin===n.margin);if(r&&(t=a.get(r)))return t;let o=new Map;return t={id:n,observer:new IntersectionObserver(e=>{e.forEach(e=>{let t=o.get(e.target),n=e.isIntersecting||e.intersectionRatio>0;t&&n&&t(n)})},e),elements:o},u.push(n),a.set(n,t),t}(n);return i.set(e,t),o.observe(e),function(){if(i.delete(e),o.unobserve(e),0===i.size){o.disconnect(),a.delete(r);let e=u.findIndex(e=>e.root===r.root&&e.margin===r.margin);e>-1&&u.splice(e,1)}}}(e,e=>e&&f(e),{root:null==t?void 0:t.current,rootMargin:n})}else if(!s){let e=(0,o.requestIdleCallback)(()=>f(!0));return()=>(0,o.cancelIdleCallback)(e)}},[c,n,t,s,d.current]),[p,s,(0,r.useCallback)(()=>{f(!1)},[])]}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},1664:function(e,t,n){e.exports=n(8342)},1163:function(e,t,n){e.exports=n(3079)},6223:function(e,t,n){"use strict";n.d(t,{Z:function(){return G}});var r,o,i,a,u,l,c,s=function(){return(s=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function f(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}"function"==typeof SuppressedError&&SuppressedError;var d=n(7294),p="right-scroll-bar-position",h="width-before-scroll-bar";function m(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var v="undefined"!=typeof window?d.useLayoutEffect:d.useEffect,g=new WeakMap,y=(void 0===o&&(o={}),(void 0===i&&(i=function(e){return e}),a=[],u=!1,l={read:function(){if(u)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return a.length?a[a.length-1]:null},useMedium:function(e){var t=i(e,u);return a.push(t),function(){a=a.filter(function(e){return e!==t})}},assignSyncMedium:function(e){for(u=!0;a.length;){var t=a;a=[],t.forEach(e)}a={push:function(t){return e(t)},filter:function(){return a}}},assignMedium:function(e){u=!0;var t=[];if(a.length){var n=a;a=[],n.forEach(e),t=a}var r=function(){var n=t;t=[],n.forEach(e)},o=function(){return Promise.resolve().then(r)};o(),a={push:function(e){t.push(e),o()},filter:function(e){return t=t.filter(e),a}}}}).options=s({async:!0,ssr:!1},o),l),w=function(){},b=d.forwardRef(function(e,t){var n,r,o,i,a=d.useRef(null),u=d.useState({onScrollCapture:w,onWheelCapture:w,onTouchMoveCapture:w}),l=u[0],c=u[1],p=e.forwardProps,h=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,M=e.shards,C=e.sideCar,R=e.noIsolation,k=e.inert,S=e.allowPinchZoom,A=e.as,T=e.gapMode,j=f(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noIsolation","inert","allowPinchZoom","as","gapMode"]),P=(n=[a,t],r=function(e){return n.forEach(function(t){return m(t,e)})},(o=(0,d.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,i=o.facade,v(function(){var e=g.get(i);if(e){var t=new Set(e),r=new Set(n),o=i.current;t.forEach(function(e){r.has(e)||m(e,null)}),r.forEach(function(e){t.has(e)||m(e,o)})}g.set(i,n)},[n]),i),O=s(s({},j),l);return d.createElement(d.Fragment,null,E&&d.createElement(C,{sideCar:y,removeScrollBar:x,shards:M,noIsolation:R,inert:k,setCallbacks:c,allowPinchZoom:!!S,lockRef:a,gapMode:T}),p?d.cloneElement(d.Children.only(h),s(s({},O),{ref:P})):d.createElement(void 0===A?"div":A,s({},O,{className:b,ref:P}),h))});b.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},b.classNames={fullWidth:h,zeroRight:p};var x=function(e){var t=e.sideCar,n=f(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return d.createElement(r,s({},n))};x.isSideCarExport=!0;var E=function(){var e=0,t=null;return{add:function(o){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=r||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=o:i.appendChild(document.createTextNode(o)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},M=function(){var e=E();return function(t,n){d.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},C=function(){var e=M();return function(t){return e(t.styles,t.dynamic),null}},R={left:0,top:0,right:0,gap:0},k=function(e){return parseInt(e||"",10)||0},S=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[k(n),k(r),k(o)]},A=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return R;var t=S(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},T=C(),j="data-scroll-locked",P=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,u=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(u,"px ").concat(r,";\n  }\n  body[").concat(j,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(u,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(u,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(p," {\n    right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(h," {\n    margin-right: ").concat(u,"px ").concat(r,";\n  }\n  \n  .").concat(p," .").concat(p," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(h," .").concat(h," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(j,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(u,"px;\n  }\n")},O=function(){var e=parseInt(document.body.getAttribute(j)||"0",10);return isFinite(e)?e:0},_=function(){d.useEffect(function(){return document.body.setAttribute(j,(O()+1).toString()),function(){var e=O()-1;e<=0?document.body.removeAttribute(j):document.body.setAttribute(j,e.toString())}},[])},L=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;_();var i=d.useMemo(function(){return A(o)},[o]);return d.createElement(T,{styles:P(i,!t,o,n?"":"!important")})},D=!1;if("undefined"!=typeof window)try{var N=Object.defineProperty({},"passive",{get:function(){return D=!0,!0}});window.addEventListener("test",N,N),window.removeEventListener("test",N,N)}catch(e){D=!1}var I=!!D&&{passive:!1},F=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&!(n.overflowY===n.overflowX&&"TEXTAREA"!==e.tagName&&"visible"===n[t])},W=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),K(e,r)){var o=H(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},K=function(e,t){return"v"===e?F(t,"overflowY"):F(t,"overflowX")},H=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},V=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),u=a*r,l=n.target,c=t.contains(l),s=!1,f=u>0,d=0,p=0;do{var h=H(e,l),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&K(e,l)&&(d+=v,p+=m),l instanceof ShadowRoot?l=l.host:l=l.parentNode}while(!c&&l!==document.body||c&&(t.contains(l)||t===l));return f&&(o&&1>Math.abs(d)||!o&&u>d)?s=!0:!f&&(o&&1>Math.abs(p)||!o&&-u>p)&&(s=!0),s},B=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},Z=function(e){return[e.deltaX,e.deltaY]},z=function(e){return e&&"current"in e?e.current:e},$=0,U=[],Y=(c=function(e){var t=d.useRef([]),n=d.useRef([0,0]),r=d.useRef(),o=d.useState($++)[0],i=d.useState(C)[0],a=d.useRef(e);d.useEffect(function(){a.current=e},[e]),d.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(z),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=d.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=B(e),u=n.current,l="deltaX"in e?e.deltaX:u[0]-i[0],c="deltaY"in e?e.deltaY:u[1]-i[1],s=e.target,f=Math.abs(l)>Math.abs(c)?"h":"v";if("touches"in e&&"h"===f&&"range"===s.type)return!1;var d=W(f,s);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=W(f,s)),!d)return!1;if(!r.current&&"changedTouches"in e&&(l||c)&&(r.current=o),!o)return!0;var p=r.current||o;return V(p,t,e,"h"===p?l:c,!0)},[]),l=d.useCallback(function(e){if(U.length&&U[U.length-1]===i){var n="deltaY"in e?Z(e):B(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta)[0]===n[0]&&r[1]===n[1]})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(z).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=d.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),s=d.useCallback(function(e){n.current=B(e),r.current=void 0},[]),f=d.useCallback(function(t){c(t.type,Z(t),t.target,u(t,e.lockRef.current))},[]),p=d.useCallback(function(t){c(t.type,B(t),t.target,u(t,e.lockRef.current))},[]);d.useEffect(function(){return U.push(i),e.setCallbacks({onScrollCapture:f,onWheelCapture:f,onTouchMoveCapture:p}),document.addEventListener("wheel",l,I),document.addEventListener("touchmove",l,I),document.addEventListener("touchstart",s,I),function(){U=U.filter(function(e){return e!==i}),document.removeEventListener("wheel",l,I),document.removeEventListener("touchmove",l,I),document.removeEventListener("touchstart",s,I)}},[]);var h=e.removeScrollBar,m=e.inert;return d.createElement(d.Fragment,null,m?d.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?d.createElement(L,{gapMode:e.gapMode}):null)},y.useMedium(c),x),X=d.forwardRef(function(e,t){return d.createElement(b,s({},e,{ref:t,sideCar:Y}))});X.classNames=b.classNames;var G=X},952:function(e,t,n){"use strict";n.d(t,{Ee:function(){return b},NY:function(){return x},fC:function(){return w}});var r=n(7294),o=n(5360),i=n(9698),a=n(9981),u=n(5320),l=n(5893),c="Avatar",[s,f]=(0,o.b)(c),[d,p]=s(c),h=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,l.jsx)(d,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,l.jsx)(u.WV.span,{...o,ref:t})})});h.displayName=c;var m="AvatarImage",v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:c=()=>{},...s}=e,f=p(m,n),d=function(e,t){let[n,o]=r.useState("idle");return(0,a.b)(()=>{if(!e){o("error");return}let n=!0,r=new window.Image,i=e=>()=>{n&&o(e)};return o("loading"),r.onload=i("loaded"),r.onerror=i("error"),r.src=e,t&&(r.referrerPolicy=t),()=>{n=!1}},[e,t]),n}(o,s.referrerPolicy),h=(0,i.W)(e=>{c(e),f.onImageLoadingStatusChange(e)});return(0,a.b)(()=>{"idle"!==d&&h(d)},[d,h]),"loaded"===d?(0,l.jsx)(u.WV.img,{...s,ref:t,src:o}):null});v.displayName=m;var g="AvatarFallback",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=p(g,n),[c,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),c&&"loaded"!==a.imageLoadingStatus?(0,l.jsx)(u.WV.span,{...i,ref:t}):null});y.displayName=g;var w=h,b=v,x=y},8990:function(e,t,n){"use strict";n.d(t,{gm:function(){return i}});var r=n(7294);n(5893);var o=r.createContext(void 0);function i(e){let t=r.useContext(o);return e||t||"ltr"}},3133:function(e,t,n){"use strict";n.d(t,{oC:function(){return e5},VY:function(){return e2},ZA:function(){return e9},ck:function(){return e3},wU:function(){return e6},__:function(){return e4},Uv:function(){return e1},Ee:function(){return e7},Rk:function(){return e8},fC:function(){return eQ},Z0:function(){return te},Tr:function(){return tt},tu:function(){return tr},fF:function(){return tn},xz:function(){return e0}});var r=n(7294),o=n(6206),i=n(8771),a=n(5360),u=n(7342),l=n(5320),c=n(4548),s=n(8990),f=n(6063),d=n(7552),p=n(5420),h=n(1276),m=n(2593),v=n(2651),g=n(9115),y=n(650),w=n(8426),b=n(9698),x=n(3541),E=n(6223),M=n(5893),C=["Enter"," "],R=["ArrowUp","PageDown","End"],k=["ArrowDown","PageUp","Home",...R],S={ltr:[...C,"ArrowRight"],rtl:[...C,"ArrowLeft"]},A={ltr:["ArrowLeft"],rtl:["ArrowRight"]},T="Menu",[j,P,O]=(0,c.B)(T),[_,L]=(0,a.b)(T,[O,m.D7,y.Pc]),D=(0,m.D7)(),N=(0,y.Pc)(),[I,F]=_(T),[W,K]=_(T),H=e=>{let{__scopeMenu:t,open:n=!1,children:o,dir:i,onOpenChange:a,modal:u=!0}=e,l=D(t),[c,f]=r.useState(null),d=r.useRef(!1),p=(0,b.W)(a),h=(0,s.gm)(i);return r.useEffect(()=>{let e=()=>{d.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>d.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,M.jsx)(m.fC,{...l,children:(0,M.jsx)(I,{scope:t,open:n,onOpenChange:p,content:c,onContentChange:f,children:(0,M.jsx)(W,{scope:t,onClose:r.useCallback(()=>p(!1),[p]),isUsingKeyboardRef:d,dir:h,modal:u,children:o})})})};H.displayName=T;var V=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=D(n);return(0,M.jsx)(m.ee,{...o,...r,ref:t})});V.displayName="MenuAnchor";var B="MenuPortal",[Z,z]=_(B,{forceMount:void 0}),$=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=F(B,t);return(0,M.jsx)(Z,{scope:t,forceMount:n,children:(0,M.jsx)(g.z,{present:n||i.open,children:(0,M.jsx)(v.h,{asChild:!0,container:o,children:r})})})};$.displayName=B;var U="MenuContent",[Y,X]=_(U),G=r.forwardRef((e,t)=>{let n=z(U,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=F(U,e.__scopeMenu),a=K(U,e.__scopeMenu);return(0,M.jsx)(j.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.z,{present:r||i.open,children:(0,M.jsx)(j.Slot,{scope:e.__scopeMenu,children:a.modal?(0,M.jsx)(q,{...o,ref:t}):(0,M.jsx)(J,{...o,ref:t})})})})}),q=r.forwardRef((e,t)=>{let n=F(U,e.__scopeMenu),a=r.useRef(null),u=(0,i.e)(t,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,x.Ry)(e)},[]),(0,M.jsx)(Q,{...e,ref:u,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,o.M)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),J=r.forwardRef((e,t)=>{let n=F(U,e.__scopeMenu);return(0,M.jsx)(Q,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),Q=r.forwardRef((e,t)=>{let{__scopeMenu:n,loop:a=!1,trapFocus:u,onOpenAutoFocus:l,onCloseAutoFocus:c,disableOutsidePointerEvents:s,onEntryFocus:h,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:x,onDismiss:C,disableOutsideScroll:S,...A}=e,T=F(U,n),j=K(U,n),O=D(n),_=N(n),L=P(n),[I,W]=r.useState(null),H=r.useRef(null),V=(0,i.e)(t,H,T.onContentChange),B=r.useRef(0),Z=r.useRef(""),z=r.useRef(0),$=r.useRef(null),X=r.useRef("right"),G=r.useRef(0),q=S?E.Z:r.Fragment,J=S?{as:w.g7,allowPinchZoom:!0}:void 0,Q=e=>{let t=Z.current+e,n=L().filter(e=>!e.disabled),r=document.activeElement,o=n.find(e=>e.ref.current===r)?.textValue,i=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=(r=Math.max(n?e.indexOf(n):-1,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(i=i.filter(e=>e!==n));let a=i.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return a!==n?a:void 0}(n.map(e=>e.textValue),t,o),a=n.find(e=>e.textValue===i)?.ref.current;!function e(t){Z.current=t,window.clearTimeout(B.current),""!==t&&(B.current=window.setTimeout(()=>e(""),1e3))}(t),a&&setTimeout(()=>a.focus())};r.useEffect(()=>()=>window.clearTimeout(B.current),[]),(0,d.EW)();let ee=r.useCallback(e=>{var t;return X.current===$.current?.side&&!!(t=$.current?.area)&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e].x,u=t[e].y,l=t[i].x,c=t[i].y;u>r!=c>r&&n<(l-a)*(r-u)/(c-u)+a&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)},[]);return(0,M.jsx)(Y,{scope:n,searchRef:Z,onItemEnter:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),onItemLeave:r.useCallback(e=>{ee(e)||(H.current?.focus(),W(null))},[ee]),onTriggerLeave:r.useCallback(e=>{ee(e)&&e.preventDefault()},[ee]),pointerGraceTimerRef:z,onPointerGraceIntentChange:r.useCallback(e=>{$.current=e},[]),children:(0,M.jsx)(q,{...J,children:(0,M.jsx)(p.M,{asChild:!0,trapped:u,onMountAutoFocus:(0,o.M)(l,e=>{e.preventDefault(),H.current?.focus({preventScroll:!0})}),onUnmountAutoFocus:c,children:(0,M.jsx)(f.XB,{asChild:!0,disableOutsidePointerEvents:s,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:b,onInteractOutside:x,onDismiss:C,children:(0,M.jsx)(y.fC,{asChild:!0,..._,dir:j.dir,orientation:"vertical",loop:a,currentTabStopId:I,onCurrentTabStopIdChange:W,onEntryFocus:(0,o.M)(h,e=>{j.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,M.jsx)(m.VY,{role:"menu","aria-orientation":"vertical","data-state":eS(T.open),"data-radix-menu-content":"",dir:j.dir,...O,...A,ref:V,style:{outline:"none",...A.style},onKeyDown:(0,o.M)(A.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&Q(e.key));let o=H.current;if(e.target!==o||!k.includes(e.key))return;e.preventDefault();let i=L().filter(e=>!e.disabled).map(e=>e.ref.current);R.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,o.M)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(B.current),Z.current="")}),onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{let t=e.target,n=G.current!==e.clientX;if(e.currentTarget.contains(t)&&n){let t=e.clientX>G.current?"right":"left";X.current=t,G.current=e.clientX}}))})})})})})})});G.displayName=U;var ee=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,M.jsx)(l.WV.div,{role:"group",...r,ref:t})});ee.displayName="MenuGroup";var et=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,M.jsx)(l.WV.div,{...r,ref:t})});et.displayName="MenuLabel";var en="MenuItem",er="menu.itemSelect",eo=r.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:a,...u}=e,c=r.useRef(null),s=K(en,e.__scopeMenu),f=X(en,e.__scopeMenu),d=(0,i.e)(t,c),p=r.useRef(!1);return(0,M.jsx)(ei,{...u,ref:d,disabled:n,onClick:(0,o.M)(e.onClick,()=>{let e=c.current;if(!n&&e){let t=new CustomEvent(er,{bubbles:!0,cancelable:!0});e.addEventListener(er,e=>a?.(e),{once:!0}),(0,l.jH)(e,t),t.defaultPrevented?p.current=!1:s.onClose()}}),onPointerDown:t=>{e.onPointerDown?.(t),p.current=!0},onPointerUp:(0,o.M)(e.onPointerUp,e=>{p.current||e.currentTarget?.click()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;!n&&(!t||" "!==e.key)&&C.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});eo.displayName=en;var ei=r.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:a=!1,textValue:u,...c}=e,s=X(en,n),f=N(n),d=r.useRef(null),p=(0,i.e)(t,d),[h,m]=r.useState(!1),[v,g]=r.useState("");return r.useEffect(()=>{let e=d.current;e&&g((e.textContent??"").trim())},[c.children]),(0,M.jsx)(j.ItemSlot,{scope:n,disabled:a,textValue:u??v,children:(0,M.jsx)(y.ck,{asChild:!0,...f,focusable:!a,children:(0,M.jsx)(l.WV.div,{role:"menuitem","data-highlighted":h?"":void 0,"aria-disabled":a||void 0,"data-disabled":a?"":void 0,...c,ref:p,onPointerMove:(0,o.M)(e.onPointerMove,ej(e=>{a?s.onItemLeave(e):(s.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>s.onItemLeave(e))),onFocus:(0,o.M)(e.onFocus,()=>m(!0)),onBlur:(0,o.M)(e.onBlur,()=>m(!1))})})})}),ea=r.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...i}=e;return(0,M.jsx)(eh,{scope:e.__scopeMenu,checked:n,children:(0,M.jsx)(eo,{role:"menuitemcheckbox","aria-checked":eA(n)?"mixed":n,...i,ref:t,"data-state":eT(n),onSelect:(0,o.M)(i.onSelect,()=>r?.(!!eA(n)||!n),{checkForDefaultPrevented:!1})})})});ea.displayName="MenuCheckboxItem";var eu="MenuRadioGroup",[el,ec]=_(eu,{value:void 0,onValueChange:()=>{}}),es=r.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,b.W)(r);return(0,M.jsx)(el,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,M.jsx)(ee,{...o,ref:t})})});es.displayName=eu;var ef="MenuRadioItem",ed=r.forwardRef((e,t)=>{let{value:n,...r}=e,i=ec(ef,e.__scopeMenu),a=n===i.value;return(0,M.jsx)(eh,{scope:e.__scopeMenu,checked:a,children:(0,M.jsx)(eo,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":eT(a),onSelect:(0,o.M)(r.onSelect,()=>i.onValueChange?.(n),{checkForDefaultPrevented:!1})})})});ed.displayName=ef;var ep="MenuItemIndicator",[eh,em]=_(ep,{checked:!1}),ev=r.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=em(ep,n);return(0,M.jsx)(g.z,{present:r||eA(i.checked)||!0===i.checked,children:(0,M.jsx)(l.WV.span,{...o,ref:t,"data-state":eT(i.checked)})})});ev.displayName=ep;var eg=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,M.jsx)(l.WV.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});eg.displayName="MenuSeparator";var ey=r.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=D(n);return(0,M.jsx)(m.Eh,{...o,...r,ref:t})});ey.displayName="MenuArrow";var ew="MenuSub",[eb,ex]=_(ew),eE=e=>{let{__scopeMenu:t,children:n,open:o=!1,onOpenChange:i}=e,a=F(ew,t),u=D(t),[l,c]=r.useState(null),[s,f]=r.useState(null),d=(0,b.W)(i);return r.useEffect(()=>(!1===a.open&&d(!1),()=>d(!1)),[a.open,d]),(0,M.jsx)(m.fC,{...u,children:(0,M.jsx)(I,{scope:t,open:o,onOpenChange:d,content:s,onContentChange:f,children:(0,M.jsx)(eb,{scope:t,contentId:(0,h.M)(),triggerId:(0,h.M)(),trigger:l,onTriggerChange:c,children:n})})})};eE.displayName=ew;var eM="MenuSubTrigger",eC=r.forwardRef((e,t)=>{let n=F(eM,e.__scopeMenu),a=K(eM,e.__scopeMenu),u=ex(eM,e.__scopeMenu),l=X(eM,e.__scopeMenu),c=r.useRef(null),{pointerGraceTimerRef:s,onPointerGraceIntentChange:f}=l,d={__scopeMenu:e.__scopeMenu},p=r.useCallback(()=>{c.current&&window.clearTimeout(c.current),c.current=null},[]);return r.useEffect(()=>p,[p]),r.useEffect(()=>{let e=s.current;return()=>{window.clearTimeout(e),f(null)}},[s,f]),(0,M.jsx)(V,{asChild:!0,...d,children:(0,M.jsx)(ei,{id:u.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":u.contentId,"data-state":eS(n.open),...e,ref:(0,i.F)(t,u.onTriggerChange),onClick:t=>{e.onClick?.(t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,o.M)(e.onPointerMove,ej(t=>{l.onItemEnter(t),t.defaultPrevented||e.disabled||n.open||c.current||(l.onPointerGraceIntentChange(null),c.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100))})),onPointerLeave:(0,o.M)(e.onPointerLeave,ej(e=>{p();let t=n.content?.getBoundingClientRect();if(t){let r=n.content?.dataset.side,o="right"===r,i=t[o?"left":"right"],a=t[o?"right":"left"];l.onPointerGraceIntentChange({area:[{x:e.clientX+(o?-5:5),y:e.clientY},{x:i,y:t.top},{x:a,y:t.top},{x:a,y:t.bottom},{x:i,y:t.bottom}],side:r}),window.clearTimeout(s.current),s.current=window.setTimeout(()=>l.onPointerGraceIntentChange(null),300)}else{if(l.onTriggerLeave(e),e.defaultPrevented)return;l.onPointerGraceIntentChange(null)}})),onKeyDown:(0,o.M)(e.onKeyDown,t=>{let r=""!==l.searchRef.current;!e.disabled&&(!r||" "!==t.key)&&S[a.dir].includes(t.key)&&(n.onOpenChange(!0),n.content?.focus(),t.preventDefault())})})})});eC.displayName=eM;var eR="MenuSubContent",ek=r.forwardRef((e,t)=>{let n=z(U,e.__scopeMenu),{forceMount:a=n.forceMount,...u}=e,l=F(U,e.__scopeMenu),c=K(U,e.__scopeMenu),s=ex(eR,e.__scopeMenu),f=r.useRef(null),d=(0,i.e)(t,f);return(0,M.jsx)(j.Provider,{scope:e.__scopeMenu,children:(0,M.jsx)(g.z,{present:a||l.open,children:(0,M.jsx)(j.Slot,{scope:e.__scopeMenu,children:(0,M.jsx)(Q,{id:s.contentId,"aria-labelledby":s.triggerId,...u,ref:d,align:"start",side:"rtl"===c.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{c.isUsingKeyboardRef.current&&f.current?.focus(),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,o.M)(e.onFocusOutside,e=>{e.target!==s.trigger&&l.onOpenChange(!1)}),onEscapeKeyDown:(0,o.M)(e.onEscapeKeyDown,e=>{c.onClose(),e.preventDefault()}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=A[c.dir].includes(e.key);t&&n&&(l.onOpenChange(!1),s.trigger?.focus(),e.preventDefault())})})})})})});function eS(e){return e?"open":"closed"}function eA(e){return"indeterminate"===e}function eT(e){return eA(e)?"indeterminate":e?"checked":"unchecked"}function ej(e){return t=>"mouse"===t.pointerType?e(t):void 0}ek.displayName=eR;var eP="DropdownMenu",[eO,e_]=(0,a.b)(eP,[L]),eL=L(),[eD,eN]=eO(eP),eI=e=>{let{__scopeDropdownMenu:t,children:n,dir:o,open:i,defaultOpen:a,onOpenChange:l,modal:c=!0}=e,s=eL(t),f=r.useRef(null),[d=!1,p]=(0,u.T)({prop:i,defaultProp:a,onChange:l});return(0,M.jsx)(eD,{scope:t,triggerId:(0,h.M)(),triggerRef:f,contentId:(0,h.M)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:c,children:(0,M.jsx)(H,{...s,open:d,onOpenChange:p,dir:o,modal:c,children:n})})};eI.displayName=eP;var eF="DropdownMenuTrigger",eW=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...a}=e,u=eN(eF,n),c=eL(n);return(0,M.jsx)(V,{asChild:!0,...c,children:(0,M.jsx)(l.WV.button,{type:"button",id:u.triggerId,"aria-haspopup":"menu","aria-expanded":u.open,"aria-controls":u.open?u.contentId:void 0,"data-state":u.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...a,ref:(0,i.F)(t,u.triggerRef),onPointerDown:(0,o.M)(e.onPointerDown,e=>{r||0!==e.button||!1!==e.ctrlKey||(u.onOpenToggle(),u.open||e.preventDefault())}),onKeyDown:(0,o.M)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&u.onOpenToggle(),"ArrowDown"===e.key&&u.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});eW.displayName=eF;var eK=e=>{let{__scopeDropdownMenu:t,...n}=e,r=eL(t);return(0,M.jsx)($,{...r,...n})};eK.displayName="DropdownMenuPortal";var eH="DropdownMenuContent",eV=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...i}=e,a=eN(eH,n),u=eL(n),l=r.useRef(!1);return(0,M.jsx)(G,{id:a.contentId,"aria-labelledby":a.triggerId,...u,...i,ref:t,onCloseAutoFocus:(0,o.M)(e.onCloseAutoFocus,e=>{l.current||a.triggerRef.current?.focus(),l.current=!1,e.preventDefault()}),onInteractOutside:(0,o.M)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(l.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eV.displayName=eH;var eB=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(ee,{...o,...r,ref:t})});eB.displayName="DropdownMenuGroup";var eZ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(et,{...o,...r,ref:t})});eZ.displayName="DropdownMenuLabel";var ez=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(eo,{...o,...r,ref:t})});ez.displayName="DropdownMenuItem";var e$=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(ea,{...o,...r,ref:t})});e$.displayName="DropdownMenuCheckboxItem";var eU=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(es,{...o,...r,ref:t})});eU.displayName="DropdownMenuRadioGroup";var eY=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(ed,{...o,...r,ref:t})});eY.displayName="DropdownMenuRadioItem";var eX=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(ev,{...o,...r,ref:t})});eX.displayName="DropdownMenuItemIndicator";var eG=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(eg,{...o,...r,ref:t})});eG.displayName="DropdownMenuSeparator",r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(ey,{...o,...r,ref:t})}).displayName="DropdownMenuArrow";var eq=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(eC,{...o,...r,ref:t})});eq.displayName="DropdownMenuSubTrigger";var eJ=r.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=eL(n);return(0,M.jsx)(ek,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});eJ.displayName="DropdownMenuSubContent";var eQ=eI,e0=eW,e1=eK,e2=eV,e9=eB,e4=eZ,e3=ez,e5=e$,e7=eU,e8=eY,e6=eX,te=eG,tt=e=>{let{__scopeDropdownMenu:t,children:n,open:r,onOpenChange:o,defaultOpen:i}=e,a=eL(t),[l=!1,c]=(0,u.T)({prop:r,defaultProp:i,onChange:o});return(0,M.jsx)(eE,{...a,open:l,onOpenChange:c,children:n})},tn=eq,tr=eJ},7552:function(e,t,n){"use strict";n.d(t,{EW:function(){return i}});var r=n(7294),o=0;function i(){r.useEffect(()=>{let e=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",e[0]??a()),document.body.insertAdjacentElement("beforeend",e[1]??a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},5420:function(e,t,n){"use strict";let r;n.d(t,{M:function(){return d}});var o=n(7294),i=n(8771),a=n(5320),u=n(9698),l=n(5893),c="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",f={bubbles:!1,cancelable:!0},d=o.forwardRef((e,t)=>{let{loop:n=!1,trapped:r=!1,onMountAutoFocus:d,onUnmountAutoFocus:g,...y}=e,[w,b]=o.useState(null),x=(0,u.W)(d),E=(0,u.W)(g),M=o.useRef(null),C=(0,i.e)(t,e=>b(e)),R=o.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;o.useEffect(()=>{if(r){let e=function(e){if(R.paused||!w)return;let t=e.target;w.contains(t)?M.current=t:m(M.current,{select:!0})},t=function(e){if(R.paused||!w)return;let t=e.relatedTarget;null===t||w.contains(t)||m(M.current,{select:!0})};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&m(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[r,w,R.paused]),o.useEffect(()=>{if(w){v.add(R);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(c,f);w.addEventListener(c,x),w.dispatchEvent(t),t.defaultPrevented||(function(e,{select:t=!1}={}){let n=document.activeElement;for(let r of e)if(m(r,{select:t}),document.activeElement!==n)return}(p(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&m(w))}return()=>{w.removeEventListener(c,x),setTimeout(()=>{let t=new CustomEvent(s,f);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||m(e??document.body,{select:!0}),w.removeEventListener(s,E),v.remove(R)},0)}}},[w,x,E,R]);let k=o.useCallback(e=>{if(!n&&!r||R.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,o=document.activeElement;if(t&&o){let t=e.currentTarget,[r,i]=function(e){let t=p(e);return[h(t,e),h(t.reverse(),e)]}(t);r&&i?e.shiftKey||o!==i?e.shiftKey&&o===r&&(e.preventDefault(),n&&m(i,{select:!0})):(e.preventDefault(),n&&m(r,{select:!0})):o===t&&e.preventDefault()}},[n,r,R.paused]);return(0,l.jsx)(a.WV.div,{tabIndex:-1,...y,ref:C,onKeyDown:k})});function p(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function h(e,t){for(let n of e)if(!function(e,{upTo:t}){if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===t||e!==t);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function m(e,{select:t=!1}={}){if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}d.displayName="FocusScope";var v=(r=[],{add(e){let t=r[0];e!==t&&t?.pause(),(r=g(r,e)).unshift(e)},remove(e){r=g(r,e),r[0]?.resume()}});function g(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},1276:function(e,t,n){"use strict";n.d(t,{M:function(){return l}});var r,o=n(7294),i=n(9981),a=(r||(r=n.t(o,2)))["useId".toString()]||(()=>void 0),u=0;function l(e){let[t,n]=o.useState(a());return(0,i.b)(()=>{e||n(e=>e??String(u++))},[e]),e||(t?`radix-${t}`:"")}},2593:function(e,t,n){"use strict";n.d(t,{ee:function(){return eU},Eh:function(){return eX},VY:function(){return eY},fC:function(){return e$},D7:function(){return ej}});var r=n(7294);let o=["top","right","bottom","left"],i=Math.min,a=Math.max,u=Math.round,l=Math.floor,c=e=>({x:e,y:e}),s={left:"right",right:"left",bottom:"top",top:"bottom"},f={start:"end",end:"start"};function d(e,t){return"function"==typeof e?e(t):e}function p(e){return e.split("-")[0]}function h(e){return e.split("-")[1]}function m(e){return"x"===e?"y":"x"}function v(e){return"y"===e?"height":"width"}function g(e){return["top","bottom"].includes(p(e))?"y":"x"}function y(e){return e.replace(/start|end/g,e=>f[e])}function w(e){return e.replace(/left|right|bottom|top/g,e=>s[e])}function b(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function x(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function E(e,t,n){let r,{reference:o,floating:i}=e,a=g(t),u=m(g(t)),l=v(u),c=p(t),s="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,y=o[l]/2-i[l]/2;switch(c){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(h(t)){case"start":r[u]-=y*(n&&s?-1:1);break;case"end":r[u]+=y*(n&&s?-1:1)}return r}let M=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,u=i.filter(Boolean),l=await (null==a.isRTL?void 0:a.isRTL(t)),c=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:s,y:f}=E(c,r,l),d=r,p={},h=0;for(let n=0;n<u.length;n++){let{name:i,fn:m}=u[n],{x:v,y:g,data:y,reset:w}=await m({x:s,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:c,platform:a,elements:{reference:e,floating:t}});s=null!=v?v:s,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(c=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:s,y:f}=E(c,d,l)),n=-1)}return{x:s,y:f,placement:d,strategy:o,middlewareData:p}};async function C(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:u,strategy:l}=e,{boundary:c="clippingAncestors",rootBoundary:s="viewport",elementContext:f="floating",altBoundary:p=!1,padding:h=0}=d(t,e),m=b(h),v=u[p?"floating"===f?"reference":"floating":f],g=x(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(v)))||n?v:v.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(u.floating)),boundary:c,rootBoundary:s,strategy:l})),y="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,w=await (null==i.getOffsetParent?void 0:i.getOffsetParent(u.floating)),E=await (null==i.isElement?void 0:i.isElement(w))&&await (null==i.getScale?void 0:i.getScale(w))||{x:1,y:1},M=x(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:u,rect:y,offsetParent:w,strategy:l}):y);return{top:(g.top-M.top+m.top)/E.y,bottom:(M.bottom-g.bottom+m.bottom)/E.y,left:(g.left-M.left+m.left)/E.x,right:(M.right-g.right+m.right)/E.x}}function R(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function k(e){return o.some(t=>e[t]>=0)}async function S(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=p(n),u=h(n),l="y"===g(n),c=["left","top"].includes(a)?-1:1,s=i&&l?-1:1,f=d(t,e),{mainAxis:m,crossAxis:v,alignmentAxis:y}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return u&&"number"==typeof y&&(v="end"===u?-1*y:y),l?{x:v*s,y:m*c}:{x:m*c,y:v*s}}function A(){return"undefined"!=typeof window}function T(e){return O(e)?(e.nodeName||"").toLowerCase():"#document"}function j(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function P(e){var t;return null==(t=(O(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function O(e){return!!A()&&(e instanceof Node||e instanceof j(e).Node)}function _(e){return!!A()&&(e instanceof Element||e instanceof j(e).Element)}function L(e){return!!A()&&(e instanceof HTMLElement||e instanceof j(e).HTMLElement)}function D(e){return!!A()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof j(e).ShadowRoot)}function N(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=H(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!["inline","contents"].includes(o)}function I(e){return[":popover-open",":modal"].some(t=>{try{return e.matches(t)}catch(e){return!1}})}function F(e){let t=W(),n=_(e)?H(e):e;return"none"!==n.transform||"none"!==n.perspective||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||["transform","perspective","filter"].some(e=>(n.willChange||"").includes(e))||["paint","layout","strict","content"].some(e=>(n.contain||"").includes(e))}function W(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}function K(e){return["html","body","#document"].includes(T(e))}function H(e){return j(e).getComputedStyle(e)}function V(e){return _(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function B(e){if("html"===T(e))return e;let t=e.assignedSlot||e.parentNode||D(e)&&e.host||P(e);return D(t)?t.host:t}function Z(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=B(t);return K(n)?t.ownerDocument?t.ownerDocument.body:t.body:L(n)&&N(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=j(o);if(i){let e=z(a);return t.concat(a,a.visualViewport||[],N(o)?o:[],e&&n?Z(e):[])}return t.concat(o,Z(o,[],n))}function z(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function $(e){let t=H(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=L(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=u(n)!==i||u(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function U(e){return _(e)?e:e.contextElement}function Y(e){let t=U(e);if(!L(t))return c(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=$(t),a=(i?u(n.width):n.width)/r,l=(i?u(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let X=c(0);function G(e){let t=j(e);return W()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:X}function q(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=U(e),u=c(1);t&&(r?_(r)&&(u=Y(r)):u=Y(e));let l=(void 0===(o=n)&&(o=!1),r&&(!o||r===j(a))&&o)?G(a):c(0),s=(i.left+l.x)/u.x,f=(i.top+l.y)/u.y,d=i.width/u.x,p=i.height/u.y;if(a){let e=j(a),t=r&&_(r)?j(r):r,n=e,o=z(n);for(;o&&r&&t!==n;){let e=Y(o),t=o.getBoundingClientRect(),r=H(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,f*=e.y,d*=e.x,p*=e.y,s+=i,f+=a,o=z(n=j(o))}}return x({width:d,height:p,x:s,y:f})}function J(e,t){let n=V(e).scrollLeft;return t?t.left+n:q(P(e)).left+n}function Q(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:J(e,r)),y:r.top+t.scrollTop}}function ee(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=j(e),r=P(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,u=0,l=0;if(o){i=o.width,a=o.height;let e=W();(!e||e&&"fixed"===t)&&(u=o.offsetLeft,l=o.offsetTop)}return{width:i,height:a,x:u,y:l}}(e,n);else if("document"===t)r=function(e){let t=P(e),n=V(e),r=e.ownerDocument.body,o=a(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=a(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),u=-n.scrollLeft+J(e),l=-n.scrollTop;return"rtl"===H(r).direction&&(u+=a(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:u,y:l}}(P(e));else if(_(t))r=function(e,t){let n=q(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=L(e)?Y(e):c(1),a=e.clientWidth*i.x;return{width:a,height:e.clientHeight*i.y,x:o*i.x,y:r*i.y}}(t,n);else{let n=G(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return x(r)}function et(e){return"static"===H(e).position}function en(e,t){if(!L(e)||"fixed"===H(e).position)return null;if(t)return t(e);let n=e.offsetParent;return P(e)===n&&(n=n.ownerDocument.body),n}function er(e,t){let n=j(e);if(I(e))return n;if(!L(e)){let t=B(e);for(;t&&!K(t);){if(_(t)&&!et(t))return t;t=B(t)}return n}let r=en(e,t);for(;r&&["table","td","th"].includes(T(r))&&et(r);)r=en(r,t);return r&&K(r)&&et(r)&&!F(r)?n:r||function(e){let t=B(e);for(;L(t)&&!K(t);){if(F(t))return t;if(I(t))break;t=B(t)}return null}(e)||n}let eo=async function(e){let t=this.getOffsetParent||er,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=L(t),o=P(t),i="fixed"===n,a=q(e,!0,i,t),u={scrollLeft:0,scrollTop:0},l=c(0);if(r||!r&&!i){if(("body"!==T(t)||N(o))&&(u=V(t)),r){let e=q(t,!0,i,t);l.x=e.x+t.clientLeft,l.y=e.y+t.clientTop}else o&&(l.x=J(o))}let s=!o||r||i?c(0):Q(o,u);return{x:a.left+u.scrollLeft-l.x-s.x,y:a.top+u.scrollTop-l.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},ei={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=P(r),u=!!t&&I(t.floating);if(r===a||u&&i)return n;let l={scrollLeft:0,scrollTop:0},s=c(1),f=c(0),d=L(r);if((d||!d&&!i)&&(("body"!==T(r)||N(a))&&(l=V(r)),L(r))){let e=q(r);s=Y(r),f.x=e.x+r.clientLeft,f.y=e.y+r.clientTop}let p=!a||d||i?c(0):Q(a,l,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-l.scrollLeft*s.x+f.x+p.x,y:n.y*s.y-l.scrollTop*s.y+f.y+p.y}},getDocumentElement:P,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,u=[..."clippingAncestors"===n?I(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=Z(e,[],!1).filter(e=>_(e)&&"body"!==T(e)),o=null,i="fixed"===H(e).position,a=i?B(e):e;for(;_(a)&&!K(a);){let t=H(a),n=F(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&["absolute","fixed"].includes(o.position)||N(a)&&!n&&function e(t,n){let r=B(t);return!(r===n||!_(r)||K(r))&&("fixed"===H(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=B(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],l=u[0],c=u.reduce((e,n)=>{let r=ee(t,n,o);return e.top=a(r.top,e.top),e.right=i(r.right,e.right),e.bottom=i(r.bottom,e.bottom),e.left=a(r.left,e.left),e},ee(t,l,o));return{width:c.right-c.left,height:c.bottom-c.top,x:c.left,y:c.top}},getOffsetParent:er,getElementRects:eo,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=$(e);return{width:t,height:n}},getScale:Y,isElement:_,isRTL:function(e){return"rtl"===H(e).direction}},ea=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:u,platform:l,elements:c,middlewareData:s}=t,{element:f,padding:p=0}=d(e,t)||{};if(null==f)return{};let y=b(p),w={x:n,y:r},x=m(g(o)),E=v(x),M=await l.getDimensions(f),C="y"===x,R=C?"clientHeight":"clientWidth",k=u.reference[E]+u.reference[x]-w[x]-u.floating[E],S=w[x]-u.reference[x],A=await (null==l.getOffsetParent?void 0:l.getOffsetParent(f)),T=A?A[R]:0;T&&await (null==l.isElement?void 0:l.isElement(A))||(T=c.floating[R]||u.floating[E]);let j=T/2-M[E]/2-1,P=i(y[C?"top":"left"],j),O=i(y[C?"bottom":"right"],j),_=T-M[E]-O,L=T/2-M[E]/2+(k/2-S/2),D=a(P,i(L,_)),N=!s.arrow&&null!=h(o)&&L!==D&&u.reference[E]/2-(L<P?P:O)-M[E]/2<0,I=N?L<P?L-P:L-_:0;return{[x]:w[x]+I,data:{[x]:D,centerOffset:L-D-I,...N&&{alignmentOffset:I}},reset:N}}}),eu=(e,t,n)=>{let r=new Map,o={platform:ei,...n},i={...o.platform,_c:r};return M(e,t,{...o,platform:i})};var el=n(3935),ec="undefined"!=typeof document?r.useLayoutEffect:r.useEffect;function es(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!es(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!es(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function ef(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function ed(e,t){let n=ef(e);return Math.round(t*n)/n}function ep(e){let t=r.useRef(e);return ec(()=>{t.current=e}),t}let eh=e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ea({element:n.current,padding:r}).fn(t):{}:n?ea({element:n,padding:r}).fn(t):{}}}),em=(e,t)=>{var n;return{...(void 0===(n=e)&&(n=0),{name:"offset",options:n,async fn(e){var t,r;let{x:o,y:i,placement:a,middlewareData:u}=e,l=await S(e,n);return a===(null==(t=u.offset)?void 0:t.placement)&&null!=(r=u.arrow)&&r.alignmentOffset?{}:{x:o+l.x,y:i+l.y,data:{...l,placement:a}}}}),options:[e,t]}},ev=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"shift",options:n,async fn(e){let{x:t,y:r,placement:o}=e,{mainAxis:u=!0,crossAxis:l=!1,limiter:c={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...s}=d(n,e),f={x:t,y:r},h=await C(e,s),v=g(p(o)),y=m(v),w=f[y],b=f[v];if(u){let e="y"===y?"top":"left",t="y"===y?"bottom":"right",n=w+h[e],r=w-h[t];w=a(n,i(w,r))}if(l){let e="y"===v?"top":"left",t="y"===v?"bottom":"right",n=b+h[e],r=b-h[t];b=a(n,i(b,r))}let x=c.fn({...e,[y]:w,[v]:b});return{...x,data:{x:x.x-t,y:x.y-r,enabled:{[y]:u,[v]:l}}}}}),options:[e,t]}},eg=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{options:n,fn(e){let{x:t,y:r,placement:o,rects:i,middlewareData:a}=e,{offset:u=0,mainAxis:l=!0,crossAxis:c=!0}=d(n,e),s={x:t,y:r},f=g(o),h=m(f),v=s[h],y=s[f],w=d(u,e),b="number"==typeof w?{mainAxis:w,crossAxis:0}:{mainAxis:0,crossAxis:0,...w};if(l){let e="y"===h?"height":"width",t=i.reference[h]-i.floating[e]+b.mainAxis,n=i.reference[h]+i.reference[e]-b.mainAxis;v<t?v=t:v>n&&(v=n)}if(c){var x,E;let e="y"===h?"width":"height",t=["top","left"].includes(p(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(x=a.offset)?void 0:x[f])||0)+(t?0:b.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(E=a.offset)?void 0:E[f])||0)-(t?b.crossAxis:0);y<n?y=n:y>r&&(y=r)}return{[h]:v,[f]:y}}}),options:[e,t]}},ey=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"flip",options:n,async fn(e){var t,r,o,i,a;let{placement:u,middlewareData:l,rects:c,initialPlacement:s,platform:f,elements:b}=e,{mainAxis:x=!0,crossAxis:E=!0,fallbackPlacements:M,fallbackStrategy:R="bestFit",fallbackAxisSideDirection:k="none",flipAlignment:S=!0,...A}=d(n,e);if(null!=(t=l.arrow)&&t.alignmentOffset)return{};let T=p(u),j=g(s),P=p(s)===s,O=await (null==f.isRTL?void 0:f.isRTL(b.floating)),_=M||(P||!S?[w(s)]:function(e){let t=w(e);return[y(e),t,y(t)]}(s)),L="none"!==k;!M&&L&&_.push(...function(e,t,n,r){let o=h(e),i=function(e,t,n){let r=["left","right"],o=["right","left"];switch(e){case"top":case"bottom":if(n)return t?o:r;return t?r:o;case"left":case"right":return t?["top","bottom"]:["bottom","top"];default:return[]}}(p(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(y)))),i}(s,S,k,O));let D=[s,..._],N=await C(e,A),I=[],F=(null==(r=l.flip)?void 0:r.overflows)||[];if(x&&I.push(N[T]),E){let e=function(e,t,n){void 0===n&&(n=!1);let r=h(e),o=m(g(e)),i=v(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=w(a)),[a,w(a)]}(u,c,O);I.push(N[e[0]],N[e[1]])}if(F=[...F,{placement:u,overflows:I}],!I.every(e=>e<=0)){let e=((null==(o=l.flip)?void 0:o.index)||0)+1,t=D[e];if(t)return{data:{index:e,overflows:F},reset:{placement:t}};let n=null==(i=F.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(R){case"bestFit":{let e=null==(a=F.filter(e=>{if(L){let t=g(e.placement);return t===j||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=s}if(u!==n)return{reset:{placement:n}}}return{}}}),options:[e,t]}},ew=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"size",options:n,async fn(e){var t,r;let o,u;let{placement:l,rects:c,platform:s,elements:f}=e,{apply:m=()=>{},...v}=d(n,e),y=await C(e,v),w=p(l),b=h(l),x="y"===g(l),{width:E,height:M}=c.floating;"top"===w||"bottom"===w?(o=w,u=b===(await (null==s.isRTL?void 0:s.isRTL(f.floating))?"start":"end")?"left":"right"):(u=w,o="end"===b?"top":"bottom");let R=M-y.top-y.bottom,k=E-y.left-y.right,S=i(M-y[o],R),A=i(E-y[u],k),T=!e.middlewareData.shift,j=S,P=A;if(null!=(t=e.middlewareData.shift)&&t.enabled.x&&(P=k),null!=(r=e.middlewareData.shift)&&r.enabled.y&&(j=R),T&&!b){let e=a(y.left,0),t=a(y.right,0),n=a(y.top,0),r=a(y.bottom,0);x?P=E-2*(0!==e||0!==t?e+t:a(y.left,y.right)):j=M-2*(0!==n||0!==r?n+r:a(y.top,y.bottom))}await m({...e,availableWidth:P,availableHeight:j});let O=await s.getDimensions(f.floating);return E!==O.width||M!==O.height?{reset:{rects:!0}}:{}}}),options:[e,t]}},eb=(e,t)=>{var n;return{...(void 0===(n=e)&&(n={}),{name:"hide",options:n,async fn(e){let{rects:t}=e,{strategy:r="referenceHidden",...o}=d(n,e);switch(r){case"referenceHidden":{let n=R(await C(e,{...o,elementContext:"reference"}),t.reference);return{data:{referenceHiddenOffsets:n,referenceHidden:k(n)}}}case"escaped":{let n=R(await C(e,{...o,altBoundary:!0}),t.floating);return{data:{escapedOffsets:n,escaped:k(n)}}}default:return{}}}}),options:[e,t]}},ex=(e,t)=>({...eh(e),options:[e,t]});var eE=n(5320),eM=n(5893),eC=r.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,eM.jsx)(eE.WV.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,eM.jsx)("polygon",{points:"0,0 30,0 15,10"})})});eC.displayName="Arrow";var eR=n(8771),ek=n(9698),eS=n(9981),eA="Popper",[eT,ej]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),a=n.length;function u(t){let{scope:n,children:o,...u}=t,l=n?.[e][a]||i,c=r.useMemo(()=>u,Object.values(u));return(0,eM.jsx)(l.Provider,{value:c,children:o})}return n=[...n,o],u.displayName=t+"Provider",[u,function(n,u){let l=u?.[e][a]||i,c=r.useContext(l);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(eA),[eP,eO]=eT(eA),e_=e=>{let{__scopePopper:t,children:n}=e,[o,i]=r.useState(null);return(0,eM.jsx)(eP,{scope:t,anchor:o,onAnchorChange:i,children:n})};e_.displayName=eA;var eL="PopperAnchor",eD=r.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:o,...i}=e,a=eO(eL,n),u=r.useRef(null),l=(0,eR.e)(t,u);return r.useEffect(()=>{a.onAnchorChange(o?.current||u.current)}),o?null:(0,eM.jsx)(eE.WV.div,{...i,ref:l})});eD.displayName=eL;var eN="PopperContent",[eI,eF]=eT(eN),eW=r.forwardRef((e,t)=>{let{__scopePopper:n,side:o="bottom",sideOffset:u=0,align:c="center",alignOffset:s=0,arrowPadding:f=0,avoidCollisions:d=!0,collisionBoundary:p=[],collisionPadding:h=0,sticky:m="partial",hideWhenDetached:v=!1,updatePositionStrategy:g="optimized",onPlaced:y,...w}=e,b=eO(eN,n),[x,E]=r.useState(null),M=(0,eR.e)(t,e=>E(e)),[C,R]=r.useState(null),k=function(e){let[t,n]=r.useState(void 0);return(0,eS.b)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(C),S=k?.width??0,A=k?.height??0,T="number"==typeof h?h:{top:0,right:0,bottom:0,left:0,...h},j=Array.isArray(p)?p:[p],O=j.length>0,_={padding:T,boundary:j.filter(eB),altBoundary:O},{refs:L,floatingStyles:D,placement:N,isPositioned:I,middlewareData:F}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:o=[],platform:i,elements:{reference:a,floating:u}={},transform:l=!0,whileElementsMounted:c,open:s}=e,[f,d]=r.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=r.useState(o);es(p,o)||h(o);let[m,v]=r.useState(null),[g,y]=r.useState(null),w=r.useCallback(e=>{e!==M.current&&(M.current=e,v(e))},[]),b=r.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=a||m,E=u||g,M=r.useRef(null),C=r.useRef(null),R=r.useRef(f),k=null!=c,S=ep(c),A=ep(i),T=ep(s),j=r.useCallback(()=>{if(!M.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};A.current&&(e.platform=A.current),eu(M.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==T.current};P.current&&!es(R.current,t)&&(R.current=t,el.flushSync(()=>{d(t)}))})},[p,t,n,A,T]);ec(()=>{!1===s&&R.current.isPositioned&&(R.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[s]);let P=r.useRef(!1);ec(()=>(P.current=!0,()=>{P.current=!1}),[]),ec(()=>{if(x&&(M.current=x),E&&(C.current=E),x&&E){if(S.current)return S.current(x,E,j);j()}},[x,E,j,S,k]);let O=r.useMemo(()=>({reference:M,floating:C,setReference:w,setFloating:b}),[w,b]),_=r.useMemo(()=>({reference:x,floating:E}),[x,E]),L=r.useMemo(()=>{let e={position:n,left:0,top:0};if(!_.floating)return e;let t=ed(_.floating,f.x),r=ed(_.floating,f.y);return l?{...e,transform:"translate("+t+"px, "+r+"px)",...ef(_.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,l,_.floating,f.x,f.y]);return r.useMemo(()=>({...f,update:j,refs:O,elements:_,floatingStyles:L}),[f,j,O,_,L])}({strategy:"fixed",placement:o+("center"!==c?"-"+c:""),whileElementsMounted:(...e)=>(function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:u=!0,ancestorResize:c=!0,elementResize:s="function"==typeof ResizeObserver,layoutShift:f="function"==typeof IntersectionObserver,animationFrame:d=!1}=r,p=U(e),h=u||c?[...p?Z(p):[],...Z(t)]:[];h.forEach(e=>{u&&e.addEventListener("scroll",n,{passive:!0}),c&&e.addEventListener("resize",n)});let m=p&&f?function(e,t){let n,r=null,o=P(e);function u(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function c(s,f){void 0===s&&(s=!1),void 0===f&&(f=1),u();let{left:d,top:p,width:h,height:m}=e.getBoundingClientRect();if(s||t(),!h||!m)return;let v=l(p),g=l(o.clientWidth-(d+h)),y={rootMargin:-v+"px "+-g+"px "+-l(o.clientHeight-(p+m))+"px "+-l(d)+"px",threshold:a(0,i(1,f))||1},w=!0;function b(e){let t=e[0].intersectionRatio;if(t!==f){if(!w)return c();t?c(!1,t):n=setTimeout(()=>{c(!1,1e-7)},1e3)}w=!1}try{r=new IntersectionObserver(b,{...y,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(b,y)}r.observe(e)}(!0),u}(p,n):null,v=-1,g=null;s&&(g=new ResizeObserver(e=>{let[r]=e;r&&r.target===p&&g&&(g.unobserve(t),cancelAnimationFrame(v),v=requestAnimationFrame(()=>{var e;null==(e=g)||e.observe(t)})),n()}),p&&!d&&g.observe(p),g.observe(t));let y=d?q(e):null;return d&&function t(){let r=q(e);y&&(r.x!==y.x||r.y!==y.y||r.width!==y.width||r.height!==y.height)&&n(),y=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;h.forEach(e=>{u&&e.removeEventListener("scroll",n),c&&e.removeEventListener("resize",n)}),null==m||m(),null==(e=g)||e.disconnect(),g=null,d&&cancelAnimationFrame(o)}})(...e,{animationFrame:"always"===g}),elements:{reference:b.anchor},middleware:[em({mainAxis:u+A,alignmentAxis:s}),d&&ev({mainAxis:!0,crossAxis:!1,limiter:"partial"===m?eg():void 0,..._}),d&&ey({..._}),ew({..._,apply:({elements:e,rects:t,availableWidth:n,availableHeight:r})=>{let{width:o,height:i}=t.reference,a=e.floating.style;a.setProperty("--radix-popper-available-width",`${n}px`),a.setProperty("--radix-popper-available-height",`${r}px`),a.setProperty("--radix-popper-anchor-width",`${o}px`),a.setProperty("--radix-popper-anchor-height",`${i}px`)}}),C&&ex({element:C,padding:f}),eZ({arrowWidth:S,arrowHeight:A}),v&&eb({strategy:"referenceHidden",..._})]}),[W,K]=ez(N),H=(0,ek.W)(y);(0,eS.b)(()=>{I&&H?.()},[I,H]);let V=F.arrow?.x,B=F.arrow?.y,z=F.arrow?.centerOffset!==0,[$,Y]=r.useState();return(0,eS.b)(()=>{x&&Y(window.getComputedStyle(x).zIndex)},[x]),(0,eM.jsx)("div",{ref:L.setFloating,"data-radix-popper-content-wrapper":"",style:{...D,transform:I?D.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:$,"--radix-popper-transform-origin":[F.transformOrigin?.x,F.transformOrigin?.y].join(" "),...F.hide?.referenceHidden&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,eM.jsx)(eI,{scope:n,placedSide:W,onArrowChange:R,arrowX:V,arrowY:B,shouldHideArrow:z,children:(0,eM.jsx)(eE.WV.div,{"data-side":W,"data-align":K,...w,ref:M,style:{...w.style,animation:I?void 0:"none"}})})})});eW.displayName=eN;var eK="PopperArrow",eH={top:"bottom",right:"left",bottom:"top",left:"right"},eV=r.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=eF(eK,n),i=eH[o.placedSide];return(0,eM.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,eM.jsx)(eC,{...r,ref:t,style:{...r.style,display:"block"}})})});function eB(e){return null!==e}eV.displayName=eK;var eZ=e=>({name:"transformOrigin",options:e,fn(t){let{placement:n,rects:r,middlewareData:o}=t,i=o.arrow?.centerOffset!==0,a=i?0:e.arrowWidth,u=i?0:e.arrowHeight,[l,c]=ez(n),s={start:"0%",center:"50%",end:"100%"}[c],f=(o.arrow?.x??0)+a/2,d=(o.arrow?.y??0)+u/2,p="",h="";return"bottom"===l?(p=i?s:`${f}px`,h=`${-u}px`):"top"===l?(p=i?s:`${f}px`,h=`${r.floating.height+u}px`):"right"===l?(p=`${-u}px`,h=i?s:`${d}px`):"left"===l&&(p=`${r.floating.width+u}px`,h=i?s:`${d}px`),{data:{x:p,y:h}}}});function ez(e){let[t,n="center"]=e.split("-");return[t,n]}var e$=e_,eU=eD,eY=eW,eX=eV},650:function(e,t,n){"use strict";n.d(t,{ck:function(){return j},fC:function(){return T},Pc:function(){return b}});var r=n(7294),o=n(6206),i=n(4548),a=n(8771),u=n(5893),l=n(1276),c=n(5320),s=n(9698),f=n(7342),d=n(8990),p="rovingFocusGroup.onEntryFocus",h={bubbles:!1,cancelable:!0},m="RovingFocusGroup",[v,g,y]=(0,i.B)(m),[w,b]=function(e,t=[]){let n=[],o=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return o.scopeName=e,[function(t,o){let i=r.createContext(o),a=n.length;function l(t){let{scope:n,children:o,...l}=t,c=n?.[e][a]||i,s=r.useMemo(()=>l,Object.values(l));return(0,u.jsx)(c.Provider,{value:s,children:o})}return n=[...n,o],l.displayName=t+"Provider",[l,function(n,u){let l=u?.[e][a]||i,c=r.useContext(l);if(c)return c;if(void 0!==o)return o;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(o,...t)]}(m,[y]),[x,E]=w(m),M=r.forwardRef((e,t)=>(0,u.jsx)(v.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(v.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,u.jsx)(C,{...e,ref:t})})}));M.displayName=m;var C=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:i,loop:l=!1,dir:m,currentTabStopId:v,defaultCurrentTabStopId:y,onCurrentTabStopIdChange:w,onEntryFocus:b,preventScrollOnEntryFocus:E=!1,...M}=e,C=r.useRef(null),R=(0,a.e)(t,C),k=(0,d.gm)(m),[S=null,T]=(0,f.T)({prop:v,defaultProp:y,onChange:w}),[j,P]=r.useState(!1),O=(0,s.W)(b),_=g(n),L=r.useRef(!1),[D,N]=r.useState(0);return r.useEffect(()=>{let e=C.current;if(e)return e.addEventListener(p,O),()=>e.removeEventListener(p,O)},[O]),(0,u.jsx)(x,{scope:n,orientation:i,dir:k,loop:l,currentTabStopId:S,onItemFocus:r.useCallback(e=>T(e),[T]),onItemShiftTab:r.useCallback(()=>P(!0),[]),onFocusableItemAdd:r.useCallback(()=>N(e=>e+1),[]),onFocusableItemRemove:r.useCallback(()=>N(e=>e-1),[]),children:(0,u.jsx)(c.WV.div,{tabIndex:j||0===D?-1:0,"data-orientation":i,...M,ref:R,style:{outline:"none",...e.style},onMouseDown:(0,o.M)(e.onMouseDown,()=>{L.current=!0}),onFocus:(0,o.M)(e.onFocus,e=>{let t=!L.current;if(e.target===e.currentTarget&&t&&!j){let t=new CustomEvent(p,h);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=_().filter(e=>e.focusable);A([e.find(e=>e.active),e.find(e=>e.id===S),...e].filter(Boolean).map(e=>e.ref.current),E)}}L.current=!1}),onBlur:(0,o.M)(e.onBlur,()=>P(!1))})})}),R="RovingFocusGroupItem",k=r.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:i=!0,active:a=!1,tabStopId:s,...f}=e,d=(0,l.M)(),p=s||d,h=E(R,n),m=h.currentTabStopId===p,y=g(n),{onFocusableItemAdd:w,onFocusableItemRemove:b}=h;return r.useEffect(()=>{if(i)return w(),()=>b()},[i,w,b]),(0,u.jsx)(v.ItemSlot,{scope:n,id:p,focusable:i,active:a,children:(0,u.jsx)(c.WV.span,{tabIndex:m?0:-1,"data-orientation":h.orientation,...f,ref:t,onMouseDown:(0,o.M)(e.onMouseDown,e=>{i?h.onItemFocus(p):e.preventDefault()}),onFocus:(0,o.M)(e.onFocus,()=>h.onItemFocus(p)),onKeyDown:(0,o.M)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey){h.onItemShiftTab();return}if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return S[o]}(e,h.orientation,h.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let o=y().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)o.reverse();else if("prev"===t||"next"===t){var n,r;"prev"===t&&o.reverse();let i=o.indexOf(e.currentTarget);o=h.loop?(n=o,r=i+1,n.map((e,t)=>n[(r+t)%n.length])):o.slice(i+1)}setTimeout(()=>A(o))}})})})});k.displayName=R;var S={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function A(e,t=!1){let n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var T=M,j=k}}]);